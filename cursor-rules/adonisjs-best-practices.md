# AdonisJS Best Practices

## TypeScript Guidelines

- Use explicit types for all variables and function parameters/returns
- Avoid using `any` type - prefer specific types or `unknown`
- Add proper TypeScript interfaces for request/response objects
- Use TypeScript decorators consistently in models and controllers
- Create type guards for narrowing types when necessary
- Leverage utility types (Partial, Pick, Omit, etc.) when appropriate
- Use branded types for special values like IDs

## Directory Structure

- Maintain standard AdonisJS directory structure
- Use domain-driven structure for larger applications
- Place all related files in their designated directories:
  - Controllers → app/controllers
  - Models → app/models
  - Services → app/services
  - Validators → app/validators
  - Routes → start/routes.ts
  - Config → config/
  - Types → types/

## Controllers

- Keep controllers slim and only handle HTTP concerns
- Use dependency injection for service classes
- Follow single responsibility principle - one method, one task
- Implement early returns to avoid nested if/else blocks
- Return appropriate HTTP status codes
- Use resource controllers for RESTful APIs

## Services

- Create service classes for business logic
- Use dependency injection for repository/service dependencies
- Add proper TypeScript interfaces for method parameters and returns
- Keep services domain-focused (UserService, TaskService, etc.)
- Return data structures instead of directly calling response functions

## Models

- Use TypeScript decorators for defining properties and relationships
- Add proper typings to all properties with `declare` keyword
- Implement hooks for common operations
- Create query scopes for frequently used queries
- Define relationships using decorators
- Use computed properties for derived data

## Routing

- Group related routes logically
- Use route resources for RESTful endpoints
- Add route names for all routes
- Prefix API routes with version numbers
- Apply middleware at the group level
- Use lazy-loaded controllers for better performance

## Validation

- Create dedicated validator classes with VineJS
- Reuse validation schemas
- Implement descriptive error messages
- Sanitize inputs in addition to validation
- Use validation middleware when appropriate
- Add custom validation rules when needed

## Error Handling

- Create custom exception classes for domain errors
- Implement consistent error responses
- Add error codes for easier troubleshooting
- Log errors with appropriate severity
- Return user-friendly error messages
- Use try/catch for expected exceptions

## Database Operations

- Use migrations for schema changes
- Create seeders for test data
- Implement transactions for atomic operations
- Leverage query builders for complex queries
- Use repositories for complex data operations
- Add proper indexes for performance

## Authentication

- Use built-in authentication providers
- Implement secure password hashing
- Use token-based authentication for APIs
- Implement refresh token rotation
- Apply auth middleware consistently
- Create auth service for complex flows

## Testing

- Write unit, functional, and integration tests
- Use factories for test data generation
- Mock external dependencies
- Use database transactions in tests
- Test both success and error paths
- Create a separate testing environment

## Security

- Validate and sanitize all inputs
- Use CSRF protection for web routes
- Implement CORS for API routes
- Set appropriate HTTP security headers
- Apply rate limiting for sensitive endpoints
- Use environment variables for sensitive data
- Follow principle of least privilege

## Performance

- Use eager loading to prevent N+1 queries
- Implement appropriate caching strategies
- Apply pagination for large datasets
- Add database indexes strategically
- Optimize asset loading and bundling
- Measure and profile application performance
- Implement HTTP level optimizations

## Code Style

- Use consistent naming conventions
- Follow the Single Responsibility Principle
- Keep files small and focused
- Add JSDoc comments for public APIs
- Follow DRY (Don't Repeat Yourself) principle
- Maintain consistent indentation and formatting
- Use early returns to minimize nesting 