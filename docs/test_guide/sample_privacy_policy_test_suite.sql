-- Sample Privacy Policy Test Data for SkedAI
-- This file contains SQL insert statements to populate test data for the privacy policy system

-- Clean existing test data (optional - comment out if you want to keep existing data)
-- DELETE FROM privacy_policy_assignments WHERE id LIKE 'assign-%';
-- DELETE FROM policy_template_rules WHERE id LIKE 'rule-%';
-- DELETE FROM privacy_policy_templates WHERE id LIKE 'tpl-%';
-- DELETE FROM policy_categories WHERE id LIKE 'cat-%';

-- Insert Policy Categories
INSERT INTO policy_categories (id, profile_id, key, label, description, suggested_keywords, is_system_context, is_active, display_order, created_at, updated_at) VALUES
('cat-work-001', 'profile-user-001', 'work_hours', 'Work Hours', 'Professional meetings, calls, and work-related activities', '["meeting", "call", "presentation", "conference", "review", "standup"]', false, true, 1, NOW(), NOW()),
('cat-personal-001', 'profile-user-001', 'personal_time', 'Personal Time', 'Personal appointments, errands, and leisure activities', '["appointment", "doctor", "gym", "shopping", "personal"]', false, true, 2, NOW(), NOW()),
('cat-family-001', 'profile-user-001', 'family_time', 'Family Time', 'Family activities, kids events, and family gatherings', '["family", "kids", "school", "dinner", "vacation"]', false, true, 3, NOW(), NOW()),
('cat-medical-001', 'profile-user-001', 'medical_health', 'Medical & Health', 'Medical appointments, therapy sessions, and health-related activities', '["doctor", "therapy", "medical", "dentist", "checkup"]', false, true, 4, NOW(), NOW()),
('cat-travel-001', 'profile-user-001', 'travel_time', 'Travel & Transportation', 'Travel time, commuting, and transportation-related blocks', '["travel", "flight", "commute", "drive", "transport"]', false, true, 5, NOW(), NOW()),
('cat-system-default', NULL, 'default_context', 'Default Context', 'System default context for activities without specific categorization', '["general", "misc", "other"]', true, true, 0, NOW(), NOW());

-- Insert Privacy Policy Templates
INSERT INTO privacy_policy_templates (id, profile_id, name, description, category_id, is_default, is_system_template, blocks_scheduling, default_detail_visibility, default_custom_message, created_at, updated_at) VALUES
('tpl-work-public', 'profile-user-001', 'Work - Public Visibility', 'Work activities visible to all contacts with full details', 'cat-work-001', false, false, true, 'full_details', NULL, NOW(), NOW()),
('tpl-work-internal', 'profile-user-001', 'Work - Internal Team Only', 'Work activities visible only to team members with title visibility', 'cat-work-001', true, false, true, 'title_only', 'In a work meeting', NOW(), NOW()),
('tpl-work-confidential', 'profile-user-001', 'Work - Confidential', 'Confidential work activities, shows as busy to most people', 'cat-work-001', false, false, true, 'busy_only', 'In a confidential meeting', NOW(), NOW()),
('tpl-personal-private', 'profile-user-001', 'Personal - Private', 'Personal activities hidden from work contacts', 'cat-personal-001', true, false, true, 'busy_only', 'Personal time', NOW(), NOW()),
('tpl-personal-open', 'profile-user-001', 'Personal - Open to Friends', 'Personal activities visible to close friends', 'cat-personal-001', false, false, true, 'title_only', NULL, NOW(), NOW()),
('tpl-family-shared', 'profile-user-001', 'Family - Shared Calendar', 'Family activities shared with family members', 'cat-family-001', true, false, true, 'full_details', NULL, NOW(), NOW()),
('tpl-medical-strict', 'profile-user-001', 'Medical - Strict Privacy', 'Medical appointments with maximum privacy', 'cat-medical-001', true, false, true, 'busy_only', 'Medical appointment', NOW(), NOW()),
('tpl-travel-transparent', 'profile-user-001', 'Travel - Transparent', 'Travel activities visible to work and personal contacts', 'cat-travel-001', true, false, true, 'title_only', 'Traveling', NOW(), NOW()),
('tpl-system-default', NULL, 'System Default', 'Default template for uncategorized activities', 'cat-system-default', true, true, true, 'busy_only', 'Busy', NOW(), NOW());

-- Insert Policy Template Rules
INSERT INTO policy_template_rules (id, template_id, viewer_scope_type, viewer_target_user_id, viewer_target_group_id, viewer_target_contact_list_id, blocks_scheduling, detail_visibility, custom_message, priority, created_at, updated_at) VALUES
-- Work Public Template Rules
('rule-work-public-001', 'tpl-work-public', 'all_contacts', NULL, NULL, NULL, true, 'full_details', NULL, 10, NOW(), NOW()),

-- Work Internal Template Rules (Team gets full details, others get title only)
('rule-work-internal-001', 'tpl-work-internal', 'specific_group', NULL, 'group-work-team', NULL, true, 'full_details', NULL, 20, NOW(), NOW()),
('rule-work-internal-002', 'tpl-work-internal', 'all_contacts', NULL, NULL, NULL, true, 'title_only', 'In a work meeting', 10, NOW(), NOW()),

-- Work Confidential Template Rules (Manager sees title, others see busy)
('rule-work-confidential-001', 'tpl-work-confidential', 'specific_contact', 'profile-manager-001', NULL, NULL, true, 'title_only', NULL, 30, NOW(), NOW()),
('rule-work-confidential-002', 'tpl-work-confidential', 'public', NULL, NULL, NULL, true, 'busy_only', 'In a confidential meeting', 5, NOW(), NOW()),

-- Personal Private Template Rules (Close friends see title, others see busy)
('rule-personal-private-001', 'tpl-personal-private', 'specific_contact_list', NULL, NULL, 'list-close-friends', true, 'title_only', NULL, 15, NOW(), NOW()),
('rule-personal-private-002', 'tpl-personal-private', 'public', NULL, NULL, NULL, true, 'busy_only', 'Personal time', 5, NOW(), NOW()),

-- Family Shared Template Rules (Family sees all, others see busy)
('rule-family-shared-001', 'tpl-family-shared', 'specific_group', NULL, 'group-family-001', NULL, true, 'full_details', NULL, 25, NOW(), NOW()),
('rule-family-shared-002', 'tpl-family-shared', 'all_contacts', NULL, NULL, NULL, true, 'busy_only', 'Family time', 10, NOW(), NOW()),

-- Medical Strict Template Rules (Spouse sees title, others see busy)
('rule-medical-strict-001', 'tpl-medical-strict', 'specific_contact', 'profile-spouse-001', NULL, NULL, true, 'title_only', NULL, 20, NOW(), NOW()),
('rule-medical-strict-002', 'tpl-medical-strict', 'public', NULL, NULL, NULL, true, 'busy_only', 'Medical appointment', 5, NOW(), NOW()),

-- Travel Transparent Template Rules (Team sees all, contacts see title)
('rule-travel-transparent-001', 'tpl-travel-transparent', 'specific_group', NULL, 'group-work-team', NULL, true, 'full_details', NULL, 20, NOW(), NOW()),
('rule-travel-transparent-002', 'tpl-travel-transparent', 'all_contacts', NULL, NULL, NULL, true, 'title_only', 'Traveling', 10, NOW(), NOW());

-- Insert Sample Activities (assuming these tables exist)
INSERT INTO activities (id, activity_type, user_id, title, description, start_date, end_date, location, created_at, updated_at) VALUES
('activity-001', 'appointment', 'user-001', 'Team Standup Meeting', 'Daily standup with the development team', '2024-03-20 09:00:00', '2024-03-20 09:30:00', '{"type": "virtual", "url": "https://zoom.us/j/*********"}', NOW(), NOW()),
('activity-002', 'appointment', 'user-001', 'Doctor Appointment', 'Annual checkup with Dr. Smith', '2024-03-20 14:00:00', '2024-03-20 15:00:00', '{"type": "physical", "address": "123 Medical Center Dr"}', NOW(), NOW()),
('activity-003', 'event', 'user-001', 'Family Dinner', 'Birthday dinner for mom', '2024-03-20 18:00:00', '2024-03-20 20:00:00', '{"type": "physical", "address": "Home"}', NOW(), NOW()),
('activity-004', 'appointment', 'user-001', 'Board Meeting', 'Quarterly board review and strategic planning', '2024-03-21 10:00:00', '2024-03-21 12:00:00', '{"type": "physical", "address": "Conference Room A"}', NOW(), NOW()),
('activity-005', 'task', 'user-001', 'Flight to NYC', 'Business trip to New York for client meetings', '2024-03-22 08:00:00', '2024-03-22 12:00:00', '{"type": "transit", "from": "LAX", "to": "JFK"}', NOW(), NOW());

-- Insert Privacy Policy Assignments
INSERT INTO privacy_policy_assignments (id, activity_id, template_id, override_blocks_scheduling, override_detail_visibility, override_custom_message, created_at, updated_at) VALUES
('assign-001', 'activity-001', 'tpl-work-internal', NULL, NULL, NULL, NOW(), NOW()),
('assign-002', 'activity-002', 'tpl-medical-strict', NULL, NULL, NULL, NOW(), NOW()),
('assign-003', 'activity-003', 'tpl-family-shared', NULL, 'title_only', 'Family celebration', NOW(), NOW()),
('assign-004', 'activity-004', 'tpl-work-confidential', NULL, NULL, NULL, NOW(), NOW()),
('assign-005', 'activity-005', 'tpl-travel-transparent', NULL, NULL, NULL, NOW(), NOW());

-- Insert Supporting Data for Testing (Groups, Contacts, Contact Lists)
-- These would typically exist in your system already, but included for completeness

-- Sample Groups
INSERT INTO user_groups (id, owner_profile_id, name, description, created_at, updated_at) VALUES
('group-work-team', 'profile-user-001', 'Development Team', 'Core development team members', NOW(), NOW()),
('group-family-001', 'profile-user-001', 'Family', 'Immediate family members', NOW(), NOW()),
('group-executives', 'profile-user-001', 'Executive Team', 'C-level and senior management', NOW(), NOW());

-- Sample Group Members
INSERT INTO group_members (id, group_id, profile_id, role, created_at, updated_at) VALUES
('gm-001', 'group-work-team', 'profile-dev-001', 'member', NOW(), NOW()),
('gm-002', 'group-work-team', 'profile-dev-002', 'member', NOW(), NOW()),
('gm-003', 'group-work-team', 'profile-manager-001', 'admin', NOW(), NOW()),
('gm-004', 'group-family-001', 'profile-spouse-001', 'admin', NOW(), NOW()),
('gm-005', 'group-family-001', 'profile-child-001', 'member', NOW(), NOW());

-- Sample Contact Lists
INSERT INTO user_contact_lists (id, owner_profile_id, name, created_at, updated_at) VALUES
('list-close-friends', 'profile-user-001', 'Close Friends', NOW(), NOW()),
('list-work-contacts', 'profile-user-001', 'Work Contacts', NOW(), NOW()),
('list-family-extended', 'profile-user-001', 'Extended Family', NOW(), NOW());

-- Sample Contact List Members
INSERT INTO contact_list_members (id, list_id, profile_id, created_at, updated_at) VALUES
('clm-001', 'list-close-friends', 'profile-friend-001', NOW(), NOW()),
('clm-002', 'list-close-friends', 'profile-friend-002', NOW(), NOW()),
('clm-003', 'list-work-contacts', 'profile-colleague-001', NOW(), NOW()),
('clm-004', 'list-work-contacts', 'profile-client-001', NOW(), NOW());

-- Sample User Contacts (Relationships)
INSERT INTO user_contacts (id, requester_profile_id, addressee_profile_id, status, created_at, updated_at) VALUES
('contact-001', 'profile-user-001', 'profile-manager-001', 'accepted', NOW(), NOW()),
('contact-002', 'profile-user-001', 'profile-spouse-001', 'accepted', NOW(), NOW()),
('contact-003', 'profile-user-001', 'profile-friend-001', 'accepted', NOW(), NOW()),
('contact-004', 'profile-user-001', 'profile-colleague-001', 'accepted', NOW(), NOW()),
('contact-005', 'profile-client-001', 'profile-user-001', 'accepted', NOW(), NOW());

-- Additional Test Data for Edge Cases

-- Template with Conflicting Rules (for testing priority resolution)
INSERT INTO privacy_policy_templates (id, profile_id, name, description, category_id, is_default, is_system_template, blocks_scheduling, default_detail_visibility, default_custom_message, created_at, updated_at) VALUES
('tpl-test-conflicts', 'profile-user-001', 'Test - Conflicting Rules', 'Template with overlapping rules to test priority resolution', 'cat-work-001', false, false, true, 'busy_only', 'Default message', NOW(), NOW());

INSERT INTO policy_template_rules (id, template_id, viewer_scope_type, viewer_target_user_id, viewer_target_group_id, viewer_target_contact_list_id, blocks_scheduling, detail_visibility, custom_message, priority, created_at, updated_at) VALUES
-- High priority rule for specific contact
('rule-conflict-high', 'tpl-test-conflicts', 'specific_contact', 'profile-manager-001', NULL, NULL, true, 'full_details', 'High priority rule', 30, NOW(), NOW()),
-- Medium priority rule for group (should apply to group members except manager due to higher priority above)
('rule-conflict-medium', 'tpl-test-conflicts', 'specific_group', NULL, 'group-work-team', NULL, true, 'title_only', 'Medium priority rule', 20, NOW(), NOW()),
-- Low priority rule for all contacts (fallback)
('rule-conflict-low', 'tpl-test-conflicts', 'all_contacts', NULL, NULL, NULL, true, 'busy_only', 'Low priority rule', 10, NOW(), NOW());

-- Template with No Rules (tests default behavior)
INSERT INTO privacy_policy_templates (id, profile_id, name, description, category_id, is_default, is_system_template, blocks_scheduling, default_detail_visibility, default_custom_message, created_at, updated_at) VALUES
('tpl-test-no-rules', 'profile-user-001', 'Test - No Rules', 'Template with no specific rules to test default behavior', 'cat-personal-001', false, false, false, 'title_only', 'Using template defaults', NOW(), NOW());

-- Activity with Assignment Override Testing
INSERT INTO activities (id, activity_type, user_id, title, description, start_date, end_date, location, created_at, updated_at) VALUES
('activity-test-override', 'appointment', 'user-001', 'Test Override Activity', 'Activity to test assignment-level overrides', '2024-03-23 15:00:00', '2024-03-23 16:00:00', '{"type": "virtual"}', NOW(), NOW());

INSERT INTO privacy_policy_assignments (id, activity_id, template_id, override_blocks_scheduling, override_detail_visibility, override_custom_message, created_at, updated_at) VALUES
('assign-test-override', 'activity-test-override', 'tpl-personal-private', false, 'full_details', 'Override message for testing', NOW(), NOW());

-- Verification Queries (uncomment to run after data insertion)
/*
-- Verify Policy Categories
SELECT 'Policy Categories' as table_name, COUNT(*) as count FROM policy_categories WHERE id LIKE 'cat-%';

-- Verify Privacy Policy Templates  
SELECT 'Privacy Policy Templates' as table_name, COUNT(*) as count FROM privacy_policy_templates WHERE id LIKE 'tpl-%';

-- Verify Policy Template Rules
SELECT 'Policy Template Rules' as table_name, COUNT(*) as count FROM policy_template_rules WHERE id LIKE 'rule-%';

-- Verify Privacy Policy Assignments
SELECT 'Privacy Policy Assignments' as table_name, COUNT(*) as count FROM privacy_policy_assignments WHERE id LIKE 'assign-%';

-- View Template Rules by Priority
SELECT 
    t.name as template_name,
    r.viewer_scope_type,
    r.detail_visibility,
    r.priority,
    r.custom_message
FROM policy_template_rules r
JOIN privacy_policy_templates t ON r.template_id = t.id
ORDER BY t.name, r.priority DESC;
*/ 