# Privacy Profile Sample Applications

This document demonstrates how our privacy policy system would work in real-world scenarios, showing the practical value of our template-based approach with different viewer scopes and visibility levels.

## Sample Privacy Templates

First, let's establish some common privacy templates that users might create:

### 1. "Work Hours" Template
- **Category**: Work/Professional
- **Default Behavior**: `blocksScheduling: true`, `defaultDetailVisibility: title_only`
- **Rules**:
  - Work Colleagues (specific_group): `full_details`, `blocksScheduling: true`
  - All Contacts: `title_only`, `blocksScheduling: true`
  - Public: `busy_only`, `blocksScheduling: true`

### 2. "Personal Time" Template
- **Category**: Personal/Family
- **Default Behavior**: `blocksScheduling: true`, `defaultDetailVisibility: busy_only`
- **Rules**:
  - Family (specific_contact_list): `full_details`, `blocksScheduling: false`
  - Close Friends (specific_contact_list): `title_only`, `blocksScheduling: false`
  - All Contacts: `busy_only`, `blocksScheduling: true`
  - Public: `hidden`, `blocksScheduling: true`

### 3. "Medical/Private" Template
- **Category**: Health/Confidential
- **Default Behavior**: `blocksScheduling: true`, `defaultDetailVisibility: hidden`
- **Rules**:
  - Spouse (specific_contact): `full_details`, `blocksScheduling: false`
  - Family (specific_contact_list): `busy_only`, `blocksScheduling: true`, `customMessage: "Personal appointment"`
  - All Others: `hidden`, `blocksScheduling: true`

### 4. "Client Meeting" Template
- **Category**: Work/Client-facing
- **Default Behavior**: `blocksScheduling: true`, `defaultDetailVisibility: busy_only`
- **Rules**:
  - Work Team (specific_group): `full_details`, `blocksScheduling: true`
  - Work Colleagues (specific_group): `title_only`, `blocksScheduling: true`
  - All Contacts: `busy_only`, `blocksScheduling: true`, `customMessage: "In a meeting"`
  - Public: `busy_only`, `blocksScheduling: true`

### 5. "Available Time" Template
- **Category**: Open/Bookable
- **Default Behavior**: `blocksScheduling: false`, `defaultDetailVisibility: title_only`
- **Rules**:
  - All Contacts: `title_only`, `blocksScheduling: false`
  - Public: `title_only`, `blocksScheduling: false`

## Sample Tasks, Events, and Appointments

### Sample 1: Work Meeting
**Activity**: "Weekly Team Standup - Sprint Planning"
**Type**: Event
**Time**: Monday 9:00 AM - 10:00 AM
**Suggested Template**: "Work Hours"
**Keywords Matched**: "team", "standup", "sprint"

**What Different Viewers See**:
- **Work Colleagues**: "Weekly Team Standup - Sprint Planning" (full details, cannot book)
- **Personal Contacts**: "Weekly Team Standup" (title only, cannot book)
- **Public/Strangers**: "Busy" (busy only, cannot book)

**Why This Template**: Work-related activity that colleagues need full context for, but personal contacts only need to know it's work-related.

---

### Sample 2: Doctor Appointment
**Activity**: "Annual Physical Checkup - Dr. Smith"
**Type**: Appointment
**Time**: Tuesday 2:00 PM - 3:00 PM
**Suggested Template**: "Medical/Private"
**Keywords Matched**: "doctor", "physical", "checkup"

**What Different Viewers See**:
- **Spouse**: "Annual Physical Checkup - Dr. Smith" (full details, can book if urgent)
- **Family Members**: "Personal appointment" (custom message, cannot book)
- **Work Colleagues**: *Activity hidden* (cannot see or book)
- **Public**: *Activity hidden* (cannot see or book)

**Why This Template**: Medical information is highly sensitive and should only be visible to closest family members.

---

### Sample 3: Client Presentation
**Activity**: "Product Demo for Acme Corp - Q4 Features"
**Type**: Appointment
**Time**: Wednesday 3:00 PM - 4:00 PM
**Suggested Template**: "Client Meeting"
**Keywords Matched**: "demo", "client", "corp"

**What Different Viewers See**:
- **Work Team Members**: "Product Demo for Acme Corp - Q4 Features" (full details, cannot book)
- **Other Work Colleagues**: "Product Demo for Acme Corp" (title only, cannot book)
- **Personal Contacts**: "In a meeting" (custom message, cannot book)
- **Public**: "Busy" (busy only, cannot book)

**Why This Template**: Client information needs to be protected, but work team needs full context for coordination.

---

### Sample 4: Family Dinner
**Activity**: "Mom's Birthday Dinner at Olive Garden"
**Type**: Event
**Time**: Friday 6:00 PM - 8:00 PM
**Suggested Template**: "Personal Time"
**Keywords Matched**: "birthday", "dinner", "family"

**What Different Viewers See**:
- **Family Members**: "Mom's Birthday Dinner at Olive Garden" (full details, can book/coordinate)
- **Close Friends**: "Mom's Birthday Dinner" (title only, can book if needed)
- **Work Colleagues**: "Busy" (busy only, cannot book)
- **Public**: *Activity hidden* (cannot see or book)

**Why This Template**: Family events should be visible to family for coordination, but work contacts don't need details.

---

### Sample 5: Gym Session
**Activity**: "CrossFit Training Session"
**Type**: Task
**Time**: Thursday 7:00 AM - 8:00 AM
**Suggested Template**: "Personal Time"
**Keywords Matched**: "training", "gym", "fitness"

**What Different Viewers See**:
- **Family Members**: "CrossFit Training Session" (full details, can book if emergency)
- **Close Friends**: "CrossFit Training" (title only, can book)
- **Work Colleagues**: "Busy" (busy only, cannot book)
- **Public**: *Activity hidden* (cannot see or book)

**Why This Template**: Personal fitness routine that family should know about, but work contacts don't need to see.

---

### Sample 6: Open Office Hours
**Activity**: "Available for 1:1 Meetings"
**Type**: Event
**Time**: Tuesday 1:00 PM - 3:00 PM
**Suggested Template**: "Available Time"
**Keywords Matched**: "available", "meetings", "office hours"

**What Different Viewers See**:
- **Work Colleagues**: "Available for 1:1 Meetings" (full details, can book)
- **All Contacts**: "Available for 1:1 Meetings" (title only, can book)
- **Public**: "Available for 1:1 Meetings" (title only, can book)

**Why This Template**: Explicitly designed to be bookable by anyone who might need to meet.

---

### Sample 7: Therapy Session
**Activity**: "Therapy Session - Dr. Johnson"
**Type**: Appointment
**Time**: Monday 4:00 PM - 5:00 PM
**Suggested Template**: "Medical/Private"
**Keywords Matched**: "therapy", "session"

**What Different Viewers See**:
- **Spouse**: "Therapy Session - Dr. Johnson" (full details, can book if emergency)
- **Family Members**: "Personal appointment" (custom message, cannot book)
- **All Others**: *Activity hidden* (cannot see or book)

**Why This Template**: Mental health appointments require maximum privacy with limited visibility.

---

### Sample 8: Project Deadline Work
**Activity**: "Finish Q4 Report - Due Tomorrow!"
**Type**: Task
**Time**: Wednesday 9:00 AM - 5:00 PM
**Suggested Template**: "Work Hours"
**Keywords Matched**: "report", "deadline", "project"

**What Different Viewers See**:
- **Work Colleagues**: "Finish Q4 Report - Due Tomorrow!" (full details, cannot book)
- **Personal Contacts**: "Finish Q4 Report" (title only, cannot book)
- **Public**: "Busy" (busy only, cannot book)

**Why This Template**: Work task that colleagues need to know about for coordination, but personal contacts just need to know you're busy.

---

### Sample 9: Date Night
**Activity**: "Anniversary Dinner with Sarah"
**Type**: Event
**Time**: Saturday 7:00 PM - 10:00 PM
**Suggested Template**: "Personal Time"
**Keywords Matched**: "anniversary", "dinner", "date"

**What Different Viewers See**:
- **Family Members**: "Anniversary Dinner with Sarah" (full details, cannot book - respect the date!)
- **Close Friends**: "Anniversary Dinner" (title only, cannot book)
- **Work Colleagues**: "Busy" (busy only, cannot book)
- **Public**: *Activity hidden* (cannot see or book)

**Why This Template**: Personal romantic time that family should know about but others shouldn't see details.

---

### Sample 10: Emergency Dentist
**Activity**: "Emergency Dental Appointment - Broken Tooth"
**Type**: Appointment
**Time**: Friday 11:00 AM - 12:00 PM
**Suggested Template**: "Medical/Private" (with activity-level override)
**Keywords Matched**: "emergency", "dental", "appointment"
**Override**: `overrideDetailVisibility: busy_only` for work colleagues (they need to know you're unavailable)

**What Different Viewers See**:
- **Spouse**: "Emergency Dental Appointment - Broken Tooth" (full details, can book if needed)
- **Family Members**: "Personal appointment" (custom message, cannot book)
- **Work Colleagues**: "Busy" (override to busy_only so they know you're unavailable, cannot book)
- **Public**: *Activity hidden* (cannot see or book)

**Why This Template + Override**: Medical emergency that work needs to know affects availability, but details remain private.

## AI Suggestion Algorithm Examples

### Keyword-Based Suggestions

```typescript
// Example keyword matching logic
const keywordMappings = {
  "Work Hours": ["meeting", "standup", "sprint", "project", "deadline", "report", "presentation"],
  "Medical/Private": ["doctor", "dentist", "therapy", "medical", "appointment", "checkup", "surgery"],
  "Personal Time": ["family", "dinner", "birthday", "anniversary", "gym", "workout", "vacation"],
  "Client Meeting": ["client", "demo", "proposal", "sales", "pitch", "external"],
  "Available Time": ["available", "office hours", "open", "bookable", "consultation"]
}

function suggestTemplate(activityTitle: string, activityDescription: string): string {
  const text = `${activityTitle} ${activityDescription}`.toLowerCase()
  
  for (const [template, keywords] of Object.entries(keywordMappings)) {
    const matchCount = keywords.filter(keyword => text.includes(keyword)).length
    if (matchCount >= 1) { // Threshold for suggestion
      return template
    }
  }
  
  return "Work Hours" // Default fallback
}
```

### Context-Based Suggestions

```typescript
// Example context analysis
function suggestTemplateWithContext(activity: Activity): string {
  // Time-based suggestions
  if (activity.startDate.hour >= 17 || activity.startDate.weekday >= 6) {
    return "Personal Time" // Evening or weekend
  }
  
  // Location-based suggestions
  if (activity.location?.includes("Hospital") || activity.location?.includes("Clinic")) {
    return "Medical/Private"
  }
  
  // Activity type suggestions
  if (activity.activityType === "appointment" && activity.title.includes("meeting")) {
    return "Client Meeting"
  }
  
  // Fallback to keyword analysis
  return suggestTemplate(activity.title, activity.description)
}
```

## Benefits Demonstrated

### 1. **Granular Control Without Complexity**
Each sample shows how users get sophisticated privacy control without having to understand the underlying rule system. They just pick a template that matches their intent.

### 2. **Contextual Appropriateness**
The suggestions are contextually appropriate - medical appointments get maximum privacy, work meetings get professional visibility, family events get family visibility.

### 3. **Flexible Override Capability**
Sample 10 shows how users can override template behavior for specific situations (emergency dental appointment where work needs to know you're unavailable).

### 4. **Relationship-Aware Visibility**
Each sample demonstrates how the same activity appears differently to different viewer types based on their relationship to the user.

### 5. **Smart Defaults with Learning Potential**
The keyword matching and context analysis provide immediate value, with the potential to learn from user behavior over time.

## Conclusion

These samples demonstrate that our privacy profile system provides significant value by:

- **Reducing Decision Fatigue**: Users don't have to configure privacy for every activity
- **Ensuring Appropriate Privacy**: Different activity types get contextually appropriate privacy levels
- **Maintaining Flexibility**: Override capabilities handle edge cases
- **Supporting Natural Workflows**: Templates match how people naturally think about privacy ("this is a work thing" vs "this is personal")

The automatic suggestion feature would make this powerful system accessible to all user types, from simple users who just want "work" and "personal" modes to advanced users who need granular control over specific viewer relationships. 