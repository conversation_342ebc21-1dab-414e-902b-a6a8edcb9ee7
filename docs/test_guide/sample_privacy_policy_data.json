{"policy_categories": [{"id": "cat-work-001", "profileId": "profile-user-001", "key": "work_hours", "label": "Work Hours", "description": "Professional meetings, calls, and work-related activities", "suggestedKeywords": ["meeting", "call", "presentation", "conference", "review", "standup"], "isSystemContext": false, "isActive": true, "displayOrder": 1}, {"id": "cat-personal-001", "profileId": "profile-user-001", "key": "personal_time", "label": "Personal Time", "description": "Personal appointments, errands, and leisure activities", "suggestedKeywords": ["appointment", "doctor", "gym", "shopping", "personal"], "isSystemContext": false, "isActive": true, "displayOrder": 2}, {"id": "cat-family-001", "profileId": "profile-user-001", "key": "family_time", "label": "Family Time", "description": "Family activities, kids events, and family gatherings", "suggestedKeywords": ["family", "kids", "school", "dinner", "vacation"], "isSystemContext": false, "isActive": true, "displayOrder": 3}, {"id": "cat-medical-001", "profileId": "profile-user-001", "key": "medical_health", "label": "Medical & Health", "description": "Medical appointments, therapy sessions, and health-related activities", "suggestedKeywords": ["doctor", "therapy", "medical", "dentist", "checkup"], "isSystemContext": false, "isActive": true, "displayOrder": 4}, {"id": "cat-travel-001", "profileId": "profile-user-001", "key": "travel_time", "label": "Travel & Transportation", "description": "Travel time, commuting, and transportation-related blocks", "suggestedKeywords": ["travel", "flight", "commute", "drive", "transport"], "isSystemContext": false, "isActive": true, "displayOrder": 5}, {"id": "cat-system-default", "profileId": null, "key": "default_context", "label": "De<PERSON><PERSON> Context", "description": "System default context for activities without specific categorization", "suggestedKeywords": ["general", "misc", "other"], "isSystemContext": true, "isActive": true, "displayOrder": 0}], "privacy_policy_templates": [{"id": "tpl-work-public", "profileId": "profile-user-001", "name": "Work - Public Visibility", "description": "Work activities visible to all contacts with full details", "categoryId": "cat-work-001", "isDefault": false, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "full_details", "defaultCustomMessage": null}, {"id": "tpl-work-internal", "profileId": "profile-user-001", "name": "Work - Internal Team Only", "description": "Work activities visible only to team members with title visibility", "categoryId": "cat-work-001", "isDefault": true, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "title_only", "defaultCustomMessage": "In a work meeting"}, {"id": "tpl-work-confidential", "profileId": "profile-user-001", "name": "Work - Confidential", "description": "Confidential work activities, shows as busy to most people", "categoryId": "cat-work-001", "isDefault": false, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "busy_only", "defaultCustomMessage": "In a confidential meeting"}, {"id": "tpl-personal-private", "profileId": "profile-user-001", "name": "Personal - Private", "description": "Personal activities hidden from work contacts", "categoryId": "cat-personal-001", "isDefault": true, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "busy_only", "defaultCustomMessage": "Personal time"}, {"id": "tpl-personal-open", "profileId": "profile-user-001", "name": "Personal - Open to Friends", "description": "Personal activities visible to close friends", "categoryId": "cat-personal-001", "isDefault": false, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "title_only", "defaultCustomMessage": null}, {"id": "tpl-family-shared", "profileId": "profile-user-001", "name": "Family - Shared Calendar", "description": "Family activities shared with family members", "categoryId": "cat-family-001", "isDefault": true, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "full_details", "defaultCustomMessage": null}, {"id": "tpl-medical-strict", "profileId": "profile-user-001", "name": "Medical - Strict Privacy", "description": "Medical appointments with maximum privacy", "categoryId": "cat-medical-001", "isDefault": true, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "busy_only", "defaultCustomMessage": "Medical appointment"}, {"id": "tpl-travel-transparent", "profileId": "profile-user-001", "name": "Travel - Transparent", "description": "Travel activities visible to work and personal contacts", "categoryId": "cat-travel-001", "isDefault": true, "isSystemTemplate": false, "blocksScheduling": true, "defaultDetailVisibility": "title_only", "defaultCustomMessage": "Traveling"}, {"id": "tpl-system-default", "profileId": null, "name": "System Default", "description": "Default template for uncategorized activities", "categoryId": "cat-system-default", "isDefault": true, "isSystemTemplate": true, "blocksScheduling": true, "defaultDetailVisibility": "busy_only", "defaultCustomMessage": "Busy"}], "policy_template_rules": [{"id": "rule-work-public-001", "templateId": "tpl-work-public", "viewerScopeType": "all_contacts", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "full_details", "customMessage": null, "priority": 10}, {"id": "rule-work-internal-001", "templateId": "tpl-work-internal", "viewerScopeType": "specific_group", "viewerTargetUserId": null, "viewerTargetGroupId": "group-work-team", "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "full_details", "customMessage": null, "priority": 20}, {"id": "rule-work-internal-002", "templateId": "tpl-work-internal", "viewerScopeType": "all_contacts", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "title_only", "customMessage": "In a work meeting", "priority": 10}, {"id": "rule-work-confidential-001", "templateId": "tpl-work-confidential", "viewerScopeType": "specific_contact", "viewerTargetUserId": "profile-manager-001", "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "title_only", "customMessage": null, "priority": 30}, {"id": "rule-work-confidential-002", "templateId": "tpl-work-confidential", "viewerScopeType": "public", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "busy_only", "customMessage": "In a confidential meeting", "priority": 5}, {"id": "rule-personal-private-001", "templateId": "tpl-personal-private", "viewerScopeType": "specific_contact_list", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": "list-close-friends", "blocksScheduling": true, "detailVisibility": "title_only", "customMessage": null, "priority": 15}, {"id": "rule-personal-private-002", "templateId": "tpl-personal-private", "viewerScopeType": "public", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "busy_only", "customMessage": "Personal time", "priority": 5}, {"id": "rule-family-shared-001", "templateId": "tpl-family-shared", "viewerScopeType": "specific_group", "viewerTargetUserId": null, "viewerTargetGroupId": "group-family-001", "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "full_details", "customMessage": null, "priority": 25}, {"id": "rule-family-shared-002", "templateId": "tpl-family-shared", "viewerScopeType": "all_contacts", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "busy_only", "customMessage": "Family time", "priority": 10}, {"id": "rule-medical-strict-001", "templateId": "tpl-medical-strict", "viewerScopeType": "specific_contact", "viewerTargetUserId": "profile-spouse-001", "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "title_only", "customMessage": null, "priority": 20}, {"id": "rule-medical-strict-002", "templateId": "tpl-medical-strict", "viewerScopeType": "public", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "busy_only", "customMessage": "Medical appointment", "priority": 5}, {"id": "rule-travel-transparent-001", "templateId": "tpl-travel-transparent", "viewerScopeType": "specific_group", "viewerTargetUserId": null, "viewerTargetGroupId": "group-work-team", "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "full_details", "customMessage": null, "priority": 20}, {"id": "rule-travel-transparent-002", "templateId": "tpl-travel-transparent", "viewerScopeType": "all_contacts", "viewerTargetUserId": null, "viewerTargetGroupId": null, "viewerTargetContactListId": null, "blocksScheduling": true, "detailVisibility": "title_only", "customMessage": "Traveling", "priority": 10}], "sample_activities": [{"id": "activity-001", "activityType": "appointment", "userId": "user-001", "title": "Team Standup Meeting", "description": "Daily standup with the development team", "startDate": "2024-03-20T09:00:00Z", "endDate": "2024-03-20T09:30:00Z", "location": {"type": "virtual", "url": "https://zoom.us/j/*********"}}, {"id": "activity-002", "activityType": "appointment", "userId": "user-001", "title": "Doctor Appointment", "description": "Annual checkup with <PERSON><PERSON>", "startDate": "2024-03-20T14:00:00Z", "endDate": "2024-03-20T15:00:00Z", "location": {"type": "physical", "address": "123 Medical Center Dr"}}, {"id": "activity-003", "activityType": "event", "userId": "user-001", "title": "Family Dinner", "description": "Birthday dinner for mom", "startDate": "2024-03-20T18:00:00Z", "endDate": "2024-03-20T20:00:00Z", "location": {"type": "physical", "address": "Home"}}], "sample_policy_assignments": [{"id": "assign-001", "activityId": "activity-001", "templateId": "tpl-work-internal", "overrideBlocksScheduling": null, "overrideDetailVisibility": null, "overrideCustomMessage": null}, {"id": "assign-002", "activityId": "activity-002", "templateId": "tpl-medical-strict", "overrideBlocksScheduling": null, "overrideDetailVisibility": null, "overrideCustomMessage": null}, {"id": "assign-003", "activityId": "activity-003", "templateId": "tpl-family-shared", "overrideBlocksScheduling": null, "overrideDetailVisibility": "title_only", "overrideCustomMessage": "Family celebration"}], "test_scenarios": [{"name": "Work Meeting Visibility", "description": "Test how work meetings appear to different viewer types", "activityId": "activity-001", "templateId": "tpl-work-internal", "expectedResults": {"team_member": {"visibility": "full_details", "title": "Team Standup Meeting", "description": "Daily standup with the development team", "customMessage": null}, "work_contact": {"visibility": "title_only", "title": "Team Standup Meeting", "description": null, "customMessage": "In a work meeting"}, "public_viewer": {"visibility": "title_only", "title": "Team Standup Meeting", "description": null, "customMessage": "In a work meeting"}}}, {"name": "Medical Appointment Privacy", "description": "Test maximum privacy for medical appointments", "activityId": "activity-002", "templateId": "tpl-medical-strict", "expectedResults": {"spouse": {"visibility": "title_only", "title": "Doctor Appointment", "description": null, "customMessage": null}, "work_contact": {"visibility": "busy_only", "title": "Busy", "description": null, "customMessage": "Medical appointment"}, "public_viewer": {"visibility": "busy_only", "title": "Busy", "description": null, "customMessage": "Medical appointment"}}}, {"name": "Family Event with Override", "description": "Test family event with assignment-level overrides", "activityId": "activity-003", "templateId": "tpl-family-shared", "expectedResults": {"family_member": {"visibility": "title_only", "title": "Family Dinner", "description": null, "customMessage": "Family celebration"}, "work_contact": {"visibility": "busy_only", "title": "Busy", "description": null, "customMessage": "Family celebration"}}}], "api_test_requests": {"create_category": {"method": "POST", "endpoint": "/api/privacy-policies/categories", "body": {"name": "Executive Time", "description": "High-level executive meetings and strategic planning"}}, "create_template": {"method": "POST", "endpoint": "/api/privacy-policies/templates", "body": {"name": "Executive - Board Level", "description": "Maximum privacy for board and C-level discussions", "permissions": {"defaultVisibility": "hidden", "allowOverrides": false}, "isDefault": false, "policyCategoryId": "cat-work-001"}}, "query_categories": {"method": "GET", "endpoint": "/api/privacy-policies/categories?sort=displayOrder&status=active"}, "query_templates": {"method": "GET", "endpoint": "/api/privacy-policies/templates?policyCategory=cat-work-001&isDefault=true"}}}