# Test Users Reference

This file contains the credentials for the test users created by the seeder using **SessionService**.

## Successfully Created Test Users ✅

### 1. <PERSON> Smith
- **Email:** `<EMAIL>`
- **Username:** `alice_smith`
- **Password:** `password123`
- **Country:** US
- **Birth Date:** March 15, 1992

### 2. <PERSON>
- **Email:** `<EMAIL>`
- **Username:** `bob_johnson`
- **Password:** `password123`
- **Country:** CA
- **Birth Date:** July 22, 1988

### 3. <PERSON>
- **Email:** `<EMAIL>`
- **Username:** `charlie_brown`
- **Password:** `password123`
- **Country:** UK
- **Birth Date:** November 8, 1995

### 4. <PERSON>
- **Email:** `<EMAIL>`
- **Username:** `diana_wilson`
- **Password:** `password123`
- **Country:** AU
- **Birth Date:** December 3, 1990

## Usage Instructions

### To Run the Seeder
```bash
node ace db:seed --files=database/seeders/user_seeder.ts
```

### To Test Login (Example with Alice)
```bash
curl -X POST http://localhost:3333/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### Expected Login Response
```json
{
  "authUser": {
    "id": "user-uuid",
    "username": "alice_smith",
    "email": "<EMAIL>",
    "createdAt": "timestamp"
  },
  "token": {
    "type": "bearer",
    "token": "oat_xxx...",
    "abilities": ["*"],
    "expiresAt": "timestamp"
  },
  "refreshToken": "refresh-token-uuid",
  "expiresIn": 7200,
  "userId": "user-uuid"
}
```

## Implementation Details

✅ **SessionService Architecture**: The seeder now uses `SessionService.registerUser()` instead of mocking HTTP context  
✅ **Proper Password Hashing**: Users are created with correctly hashed passwords that work with authentication  
✅ **Complete User Records**: Each user has AuthUser, User, and Profile records created automatically  
✅ **Business Logic Consistency**: Uses the same registration logic as the API endpoints  

## What's Created

For each user, the seeder creates:
1. **AuthUser record** - for authentication (in `auth_users` table)
2. **User record** - linked to AuthUser (in `users` table) 
3. **Profile record** - created automatically via model hook (in `profiles` table)

## Testing the Contact API

Now that you have authenticated users, you can test the Contact Management API by:

1. **Login to get tokens** for multiple users
2. **Send contact requests** between users
3. **Test contact list functionality**

Refer to `docs/contact_api_test_guide.md` for complete Contact API testing instructions.

---

*Last updated: Successfully refactored to use SessionService approach* 