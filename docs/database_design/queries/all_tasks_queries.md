# `getTasks` Function Diagrams

Here are the Mermaid diagrams visualizing the `getTasks` function flow, filtering, sorting, and optimization strategy.

---

## 1. `getTasks` Main Flow

```mermaid
graph TD
    A["getTasks Called<br/>userId + options"] --> B["Extract Parameters<br/>queryParams = {filters, sort, pagination}<br/>paginate = true/false"]
    
    B --> C["Build Base Query<br/>Task.query()<br/>.where('user_id', userId)<br/>.whereNotNull('activity_id')"]
    
    C --> D["SELECT with JOIN<br/>.select(['tasks.*', 'activities.id as activity_id', ...])<br/>.join('activities', 'tasks.activity_id', 'activities.id')<br/>.whereNull('activities.deleted_at')"]
    
    D --> E{Has Filters?}
    E -->|Yes| F["Apply Filters<br/>applyFilters(query, filters)"]
    E -->|No| G["Check Sorting"]
    F --> G
    
    G --> H{Has Sort?}
    H -->|Yes| I["Apply Sorting<br/>applySorting(query, sort)"]
    H -->|No| J["Default Sort<br/>.orderBy('activities.created_at', 'desc')"]
    I --> K["Check Pagination"]
    J --> K
    
    K --> L{Paginate?}
    L -->|No| M["Execute Query<br/>const tasks = await query"]
    L -->|Yes| N["Execute Paginated<br/>query.paginate(page, limit)"]
    
    M --> O["Map Activity Data<br/>mapActivityDataToTasks(tasks)"]
    N --> P["Extract Tasks<br/>const tasks = paginatedResult.all()"]
    P --> O
    
    O --> Q["Return Response<br/>{data: tasks, pagination?}"]
```

---

## 2. Filter System (`applyFilters` method)

```mermaid
graph TD
    A["Filters Object<br/>{status, priority, search, startDate, endDate, tags}"] --> B{Check Each Filter}
    
    B --> C["Status Filter<br/>filters.status?<br/>query.where('tasks.status', status)"]
    B --> D["Priority Filter<br/>filters.priority?<br/>query.where('tasks.priority', priority)"]
    B --> E["Search Filter<br/>filters.search?<br/>Search in title OR description"]
    B --> F["Start Date Filter<br/>filters.startDate?<br/>Convert to SQL date"]
    B --> G["End Date Filter<br/>filters.endDate?<br/>Convert to SQL date"]
    B --> H["Tags Filter<br/>filters.tags?<br/>(Currently skipped - needs redesign)"]
    
    E --> E1["Subquery for Search<br/>query.where((subQuery) => {<br/>  subQuery.whereILike('activities.title', '%search%')<br/>    .orWhereILike('activities.description', '%search%')<br/>})"]
    
    F --> F1["Start Date Logic<br/>DateTime.fromISO(startDate).toSQL()<br/>query.where('activities.start_date', '>=', startDate)"]
    
    G --> G1["End Date Logic<br/>DateTime.fromISO(endDate).toSQL()<br/>query.where('activities.end_date', '<=', endDate)"]
```

---

## 3. Sorting System (`applySorting` method)

```mermaid
graph TD
    A["Sort Object<br/>{field, direction: 'asc'|'desc'}"] --> B{Check Field Type}
    
    B --> C["Priority Sorting<br/>field === 'priority'<br/>Custom CASE logic"]
    B --> D["Activity Fields<br/>endDate, startDate, title"]
    B --> E["Task Fields<br/>createdAt, status"]
    B --> F["Fallback<br/>Other fields"]
    
    C --> C1["Priority CASE Statement<br/>ASC: low=1, medium=2, high=3<br/>DESC: high=1, medium=2, low=3<br/>query.orderByRaw(priorityOrder)"]
    
    D --> D1["Activity Field Mapping<br/>endDate → 'activities.end_date'<br/>startDate → 'activities.start_date'<br/>title → 'activities.title'"]
    
    E --> E1["Task Field Mapping<br/>createdAt → 'tasks.created_at'<br/>status → 'tasks.status'"]
    
    F --> F1["Generic Fallback<br/>query.orderBy('tasks.' + field, direction)"]
```

---

## 4. Query Optimization (`mapActivityDataToTasks`)

```mermaid
graph TD
    A["BEFORE OPTIMIZATION<br/>3 Queries Total"] --> A1["Main Query<br/>JOIN + WHERE + ORDER"]
    A --> A2["Count Query<br/>For pagination"]
    A --> A3["Preload Query<br/>SELECT * FROM activities<br/>WHERE id IN (...)"]
    
    B["AFTER OPTIMIZATION<br/>2 Queries Total"] --> B1["Main Query<br/>SELECT tasks.*, activities.*<br/>FROM tasks JOIN activities<br/>+ filters + sorting"]
    B --> B2["Count Query<br/>For pagination only"]
    
    A3 --> X["ELIMINATED<br/>Redundant activity fetch"]
    
    B1 --> C["Manual Mapping<br/>mapActivityDataToTasks()"]
    
    C --> C1["For each task:<br/>1. Extract activity data from $extras<br/>2. Create Activity model instance<br/>3. Set relationship"]
    
    C1 --> C2["Activity.$createFromAdapterResult()<br/>• Creates proper model instance<br/>• Has serialize() method<br/>• Marked as persisted<br/>• Works with JSON serialization"]
    
    C2 --> C3["task.$setRelated('activity', activity)<br/>Properly sets the relationship"]
``` 