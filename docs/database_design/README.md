# SkedAI Database Design

This document provides an overview of the SkedAI database schema, focusing on the core entities and their relationships. The database is designed to support a comprehensive scheduling and task management system with advanced availability tracking and social features.

## Overview

The SkedAI database uses a **polymorphic architecture** for activity management, with separate `tasks` and `events` tables that are referenced by related features through polymorphic relationships. Key features include:

- **Polymorphic Activity Management**: Separate `tasks` and `events` tables with polymorphic references from related features.
- **Policy Category-Based Privacy**: A sophisticated **Policy Template System** (`privacy_policy_template`, `policy_template_rule`, `privacy_policy_assignment`) for granular control over activity visibility and availability.
- **Flexible Availability**: `user_availability_slot` for defining general recurring or one-time availability.
- **Social Engagement**: Robust support for contacts, groups, commitments, and invitations.
- **Comprehensive Organization**: Unified tagging and categorization across all activity types using polymorphic relationships.

## Key Design Patterns

### 1. Polymorphic Relationships for Activities
- **Separate `tasks` and `events` tables** with all activity fields included directly.
- **Polymorphic references** using `activityType` + `activityId` pattern in related tables.
- **Benefits**: Type safety, clear separation of task vs event specific fields, no complex joins, consistent polymorphic pattern across related features.

### 2. Template-Based Policy Control
- Users define reusable `privacy_policy_template`s with detailed `policy_template_rule`s.
- Activities are linked to templates via `privacy_policy_assignment`.
- **Benefits**: Reduces per-activity configuration, ensures consistency, simplifies UX for privacy settings, and allows for powerful default behaviors with override capabilities. `policy_category` allows for categorization and smart suggestions for templates.

### 3. Soft Deletes
- Most user data tables include `deletedAt` timestamp for data retention and audit compliance (e.g., `user`, `profile`, `activity`, `privacy_policy_template`).

### 4. Granular Permissions & Scopes
- Consistent `AvailabilityScopeType` enum and target ID fields used across `user_availability_slot` and `policy_template_rule` for fine-grained control.

### 5. Unified Tagging
- Single `activity_tag` table references the `activity.id`, allowing any activity type to be tagged uniformly.

## Core Entities

### 1. User and Profile
- **`user`**: Stores authentication data (username, email, password hash).
- **`profile`**: Contains user profile information (name, bio, profile picture).
- **`profile_settings`**: Manages user-specific settings, including default policy template and auto-accept preferences for invitations.

### 2. Separate Activity Tables
The polymorphic approach uses separate tables for different activity types:

- **`tasks` table**:
  - **Purpose**: Task management with all activity fields included directly.
  - **Key Fields**: `id`, `userId`, `title`, `description`, `startDate`, `endDate`, `location`, `status`, `priority`, `parentTaskId`, `metadata`.
  - **Features**: Self-referencing parent tasks, AI-generated vs user-created tracking, rich metadata support.

- **`events` table**:
  - **Purpose**: Event scheduling with all activity fields included directly.
  - **Key Fields**: `id`, `userId`, `title`, `description`, `startDate`, `endDate`, `location`, `status`, `maxAttendees`.
  - **Features**: Attendee limits, event status management.

### 3. Social Features
- **`user_contact`**: Manages contact relationships with a request/acceptance workflow.
- **`user_group`**: Defines user-created groups with ownership.
- **`group_member`**: Manages group membership with role-based permissions (member/admin).
- **`user_contact_list`**: Allows users to create named contact lists for better organization.
- **`contact_list_member`**: Manages membership within contact lists.

### 4. Organization & Audit (Planned)
- **`activity_log`**: System-wide audit trail using polymorphic references to tasks/events.
- **`mentions_to_task`**: Handles mentions within descriptions using polymorphic references.
- **`tag`**: Global and personal tags for categorizing activities.
- **`activity_tag`**: Unified tagging table with polymorphic references to tasks or events.

## Detailed System Descriptions

### Availability and Privacy Management System

This system empowers users with fine-grained control over who can see their availability, who can book their time, and what details are shared about their activities. It revolves around general availability slots and a powerful template-based policy system for activities.

#### `user_availability_slot`
- **Purpose**: Defines a user's general availability, supporting both recurring schedules (e.g., "Mon-Fri 9am-5pm") and specific one-time slots.
- **Key Features**:
  - **Temporal Flexibility**: `dayOfWeek`, `startTime`, `endTime` for recurring; `specificStartDate`, `specificEndDate` for specific instances.
  - **Visibility Control**: Granular permission system using `visibilityScope` (public, all_contacts, specific_contact, specific_group, specific_contact_list) and corresponding target ID fields.
  - **Availability State**: `blocksScheduling` boolean to explicitly mark periods as busy or free.
- **Relationships**: Links to `profile` (owner and specific visibility targets), `user_group`, and `user_contact_list`.

#### Policy Template System Tables

This system allows users to define reusable privacy and availability policies that can be quickly applied to activities.

##### `policy_category`
- **Purpose**: Defines categories or themes for policy templates (e.g., "Work Hours", "Personal Time", "Emergency").
- **Key Features**: `key`, `label`, `description`, `suggestedKeywords` (JSONB for smart suggestions), `isSystemContext` (for predefined categories), `profileId` (for user-defined categories).

###### Role of Policy Category

The `policy_category` entity plays a specific role from technical, user, and design perspectives:

*   **Technical Role**:
    *   **Data Structure**: Stores metadata for categories (label, description, keywords, system/user ownership).
    *   **Relationship**: Linked to `privacy_policy_template` via `categoryId`.
    *   **Runtime Logic**: Does *not* directly influence runtime policy evaluation.
    *   **Purpose**: Provides structured data for organizing and suggesting policy templates, supporting UI and potential AI features.

*   **User-wise Role**:
    *   **Organization**: Helps users categorize policy templates (e.g., "Work Hours", "Personal Time").
    *   **Clarity & Intuition**: Provides clear thematic labels, making template purpose understandable.
    *   **Discovery & Selection**: Simplifies finding the right template by allowing users to filter or search by category.
    *   **Potential AI Assistance**: Keywords aid AI in suggesting relevant categories/templates based on activity descriptions.

*   **Design-wise Role**:
    *   **UI Element**: Requires a dropdown or selection control for category in template forms.
    *   **Information Display**: Provides `label` and `description` for UI elements.
    *   **Structure**: Suggests grouping templates by category in UI lists.
    *   **Navigation/Views**: Justifies a "Manage Categories" area for user-defined categories.
    *   **Visual Cues**: `isSystemContext` allows visual differentiation in the UI.

##### `privacy_policy_template`
- **Purpose**: Stores reusable policy templates created by users or provided by the system.
- **Key Features**: `name`, `description`, `categoryId` (links to `policy_category`), `isDefault`, `isSystemTemplate`, `blocksScheduling`, and `defaultDetailVisibility`.

##### `policy_template_rule`
- **Purpose**: Defines specific rules within a template for different viewer scopes. Each template can have multiple rules.
- **Key Features**: `templateId`, `viewerScopeType`, `viewerTargetUserId/GroupId/ContactListId`, `blocksScheduling`, `detailVisibilityLevel`, `customMessage`, `priority` (for rule conflict resolution).

##### `privacy_policy_assignment`
- **Purpose**: Lightweight table linking tasks or events to privacy policy templates using polymorphic relationships.
- **Key Features**: `activityType`, `activityId` (polymorphic), `templateId`, `overrideBlocksScheduling`, `overrideDetailVisibility`, and `overrideCustomMessage`.

#### How it Works Together

1. Users define general availability using `user_availability_slot`.
2. Users (or the system) create `privacy_policy_template`s (e.g., "Work Mode", "Family Mode") with associated `policy_template_rule`s that specify:
    - Who can book time (`blocksScheduling`).
    - What level of detail is visible (`detailVisibilityLevel`: hidden, busy_only, title_only, full_details).
    - Custom busy messages.
3. When creating a task or event, the user selects an appropriate `privacy_policy_template` via `privacy_policy_assignment` (using polymorphic reference).
4. The system then uses the template's rules to determine how that activity appears to different viewers, effectively layering activity-specific privacy and availability on top of the user's general availability.

#### Examples

##### Table-Specific Examples

To illustrate the role of each table:

- **`user_availability_slot`**: User sets recurring availability: "Available Mon-Fri 9am-5pm". User adds a one-time slot for a vacation week: "Unavailable July 1-5 all day". *Edge Case*: User marks a specific future hour as `blocksScheduling: false` to intentionally show "Busy" without linking it to an activity.
- **`policy_category`**: System creates a "Work Hours" category (`isSystemContext: true`). User creates a "Hobby Time" category (`profileId: user-id`). The "Work Hours" category has `suggestedKeywords: ["meeting", "call"]`.
- **`privacy_policy_template`**: User creates a template named "Gym Time" and links it to the "Personal Time" `policy_category`. User marks their "Default Work" template as `isDefault: true`. The "Gym Time" template has `blocksScheduling: true` and `defaultDetailVisibility: busy_only`.
- **`policy_template_rule`**: *Within* the "Gym Time" template, a rule is set: `viewerScopeType: specific_contact_list`, `viewerTargetContactListId: 'family-list-id'`, `blocksScheduling: false` (can schedule), `detailVisibilityLevel: full_details`. Another rule for "Gym Time" template: `viewerScopeType: all_contacts`, `blocksScheduling: true` (blocks scheduling), `detailVisibilityLevel: busy_only`, `priority: 0`. (The family rule with higher priority will override this general rule for family members).
- **`privacy_policy_assignment`**: When creating a "Morning Workout" event (`activityType: event`), the system creates an assignment: `activityType: 'event'`, `activityId: workout-event-id`, `templateId: gym-time-template-id`. *Edge Case*: User assigns the "Work Hours" template to a task, but overrides the `blocksScheduling` to `false` for a specific colleague by setting `overrideBlocksScheduling: false` in the assignment row.

##### Real-World Usage Examples

- **Scenario 1: Selective Booking & Visibility**
  - **General Availability**: Mon-Fri 9am-5pm (`user_availability_slot`).
  - **Activity**: "Doctor Appointment", Tue 2-3pm.
  - **Policy Template Applied ("Medical Privacy")**:
    - **Rule for Work Contacts**: `blocksScheduling = true` (blocks scheduling), `detailVisibilityLevel = busy_only`, `customMessage = "Unavailable"`.
    - **Rule for Family Contacts**: `blocksScheduling = false` (available for scheduling), `detailVisibilityLevel = title_only`.
  - **Result**:
    - Work contacts see "Unavailable" from 2-3pm and cannot book.
    - Family sees "Doctor Appointment" from 2-3pm and CAN still book if urgent (user manages conflict).

- **Scenario 2: Template-Driven Workday**
  - **Activity**: "Team Meeting", Wed 10-11am.
  - **Policy Template Applied ("Work Hours")**:
    - **Rule for Work Colleagues**: `blocksScheduling = true`, `detailVisibilityLevel = full_details`.
    - **Rule for External Contacts**: `blocksScheduling = true`, `detailVisibilityLevel = busy_only`.
    - **Rule for Family/Friends**: `blocksScheduling = false` (available for scheduling), `detailVisibilityLevel = hidden`.
  - **Result**: Colleagues see full meeting details and are blocked. External contacts see "Busy". Family/friends don't see the meeting and perceive the user as available.

This template system provides powerful, reusable controls, simplifying privacy management for users while offering deep customization.

##### Detailed Policy Template Example

Let's trace a detailed example of creating and applying a "Client Meeting Privacy" template.

**Scenario:** Sarah wants a template for client meetings where:
- Work contacts see full details and cannot book.
- Family contacts see only "Busy" but *can* still book if necessary.
- Everyone else sees nothing and cannot book.

**Step 1: Define a Category (if not already system-defined)**
If a "Client Meeting" category doesn't exist (either system-wide or user-defined), it would be added to `policy_category`.

```sql
-- Example row in `policy_category` table
{
  "id": "client-category-uuid",
  "profileId": null, -- System category
  "key": "client_meeting",
  "label": "Client Meeting",
  "description": "Policies for meetings with clients",
  "suggestedKeywords": ["client", "meeting", "call", "demo"],
  "isSystemContext": true,
  "isActive": true,
  "displayOrder": 10
}
```

**Step 2: Create the Policy Template**
Sarah creates a new template for client meetings, linking it to the "Client Meeting" category.

```sql
-- Example row in `privacy_policy_template` table
{
  "id": "client-template-uuid",
  "profileId": "sarah-profile-uuid",
  "name": "Client Meeting Privacy",
  "description": "Rules for external client interactions",
  "categoryId": "client-category-uuid",
  "isDefault": false,
  "isSystemTemplate": false,
  "blocksScheduling": true, -- Default blocks scheduling
  "defaultDetailVisibility": "busy_only", -- Default is busy only
  "createdAt": "...",
  "updatedAt": "...",
  "deletedAt": null
}
```

**Step 3: Define Rules within the Template**
Sarah adds specific rules for different viewer groups within the "Client Meeting Privacy" template.

```sql
-- Example rows in `policy_template_rule` table

-- Rule 1: Work Contacts
{
  "id": "client-rule-work-uuid",
  "templateId": "client-template-uuid",
  "viewerScopeType": "specific_group",
  "viewerTargetUserId": null,
  "viewerTargetGroupId": "sarahs-work-group-uuid",
  "viewerTargetContactListId": null,
  "blocksScheduling": true,      -- Blocks scheduling
  "detailVisibility": "full_details", -- See everything
  "customMessage": null,
  "priority": 10,             -- High priority
  "createdAt": "...",
  "updatedAt": "..."
},
-- Rule 2: Family Contacts
{
  "id": "client-rule-family-uuid",
  "templateId": "client-template-uuid",
  "viewerScopeType": "specific_contact_list",
  "viewerTargetUserId": null,
  "viewerTargetGroupId": null,
  "viewerTargetContactListId": "sarahs-family-list-uuid",
  "blocksScheduling": false,     -- Can schedule
  "detailVisibility": "busy_only", -- See only "Busy"
  "customMessage": "Personal appointment", -- Or a custom message
  "priority": 5,              -- Medium priority
  "createdAt": "...",
  "updatedAt": "..."
},
-- Rule 3: Default for Everyone Else (Lower Priority Catch-all)
{
  "id": "client-rule-default-uuid",
  "templateId": "client-template-uuid",
  "viewerScopeType": "public", -- Applies to anyone not covered by other rules
  "viewerTargetUserId": null,
  "viewerTargetGroupId": null,
  "viewerTargetContactListId": null,
  "blocksScheduling": true,      -- Blocks scheduling
  "detailVisibility": "hidden", -- See nothing
  "customMessage": null,
  "priority": 0,              -- Lowest priority
  "createdAt": "...",
  "updatedAt": "..."
}
```
*(Note: The actual "everyone else" rule might not be explicitly needed if the system applies the template's defaults when no specific rule matches a viewer's scope, but defining a public/catch-all rule is good practice for clarity and control.)*

**Step 4: Apply the Template to an Activity**
Sarah creates a "Client Demo" appointment and selects the "Client Meeting Privacy" template.

```sql
-- Example row in `events` table (polymorphic approach)
{
    "id": "client-demo-event-uuid",
    "userId": "sarah-user-uuid",
    "title": "Client Demo - Acme Corp",
    "description": "Demo of new feature set",
    "startDate": "2024-03-20 14:00:00",
    "endDate": "2024-03-20 15:00:00",
    "location": {"type": "virtual", "url": "..."},
    "status": "active",
    "maxAttendees": 5,
    "createdAt": "...",
    "updatedAt": "...",
    "deletedAt": null
}

-- Example row in `privacy_policy_assignment` table (polymorphic linking)
{
  "id": "client-assign-uuid",
  "activityType": "event",
  "activityId": "client-demo-event-uuid",
  "templateId": "client-template-uuid",
  "overrideBlocksScheduling": null, -- Use template rules
  "overrideDetailVisibility": null, -- Use template rules
  "overrideCustomMessage": null,
  "createdAt": "...",
  "updatedAt": "..."
}
```

**Resulting Behavior:**

- When a **work contact** views Sarah's availability for 2024-03-20 14:00-15:00, the system finds the `privacy_policy_assignment` linking the activity to the template. It then checks the `policy_template_rule`s for this template that match the viewer's scope ("specific_group", targeting the work group). It finds Rule 1, sees `blocksScheduling: true` and `detailVisibility: full_details`, so it shows the work contact the activity title "Client Demo - Acme Corp" and marks the time as "Busy" (blocks scheduling).

- When a **family contact** views Sarah's availability, the system finds Rule 2 (higher priority than the default) matching their scope ("specific_contact_list", targeting the family list). It sees `blocksScheduling: false` and `detailVisibility: busy_only` (or "Personal appointment" if using the custom message), so it shows them "Busy" (or the custom message) but allows them to potentially book/schedule during that time.

- When **anyone else** (not in work group or family list) views Sarah's availability, the system falls back to Rule 3 (or template defaults if no rule matches). It sees `blocksScheduling: true` and `detailVisibility: hidden`, so they see nothing about the activity and blocks scheduling.

This example demonstrates how the four tables work together to create reusable, sophisticated privacy and availability policies that are easily applied per activity.

#### Sample Implementation: Querying Availability with Policy Rules

Here's how you might implement the essential availability query logic in AdonisJS using Lucid ORM. For the complete implementation code and detailed explanation, please refer to [policy_logic.md](mdc:docs/database_design/policy_logic.md).

```typescript
// Essential query logic for getting availability with policy filtering (polymorphic approach)
async getAvailabilityForDay(requesterUserId: string, targetUserId: string, date: DateTime) {
  // Get tasks and events for the target user on this day with policy templates
  const [tasks, events] = await Promise.all([
    Task.query()
      .where('userId', targetUserId)
      .whereRaw('DATE(start_date) = ?', [date.toISODate()])
      .preload('policyAssignments', (query) => {
        query.preload('template', (templateQuery) => {
          templateQuery.preload('rules')
        })
      }),
    Event.query()
      .where('userId', targetUserId)
      .whereRaw('DATE(start_date) = ?', [date.toISODate()])
      .preload('policyAssignments', (query) => {
        query.preload('template', (templateQuery) => {
          templateQuery.preload('rules')
        })
      })
  ])

  // Determine requester's relationship category (contact, shared groups, etc.)
  const requesterContext = await this.getRequesterCategory(
    requesterUserId,
    targetUserId
  )

  // Filter activities based on policy rules
  const visibleTasks = tasks
    .map(task => this.applyPolicyRules(task, 'task', requesterContext))
    .filter(result => result.isVisible)
    
  const visibleEvents = events
    .map(event => this.applyPolicyRules(event, 'event', requesterContext))
    .filter(result => result.isVisible)

  return { 
    tasks: visibleTasks,
    events: visibleEvents,
    activities: [...visibleTasks, ...visibleEvents]
  }
}

// Core logic for evaluating policy rules (works with both tasks and events)
private applyPolicyRules(activity: Task | Event, activityType: 'task' | 'event', requesterContext: any) {
  const policyAssignment = activity.policyAssignments?.[0] // Polymorphic relationship
  const template = policyAssignment?.template
  
  if (!template) {
    // Default: show as busy, block scheduling
    return { 
      isVisible: true, 
      title: 'Busy', 
      blocksScheduling: true,
      activityType 
    }
  }

  // Find matching rule by priority
  const matchingRule = template.rules
    .sort((a, b) => b.priority - a.priority)
    .find(rule => this.ruleMatches(rule, requesterContext))

  const detailLevel = matchingRule?.detailVisibility || template.defaultDetailVisibility
  const blocksScheduling = matchingRule?.blocksScheduling ?? template.blocksScheduling

  return {
    isVisible: detailLevel !== 'hidden',
    title: this.getVisibleTitle(activity, detailLevel),
    description: detailLevel === 'full_details' ? activity.description : null,
    blocksScheduling,
    customMessage: matchingRule?.customMessage,
    activityType,
    activityId: activity.id
  }
}

// Check if a policy rule applies to the requester
private ruleMatches(rule: PolicyTemplateRule, category: any): boolean {
  switch (rule.viewerScopeType) {
    case 'public': return true
    case 'all_contacts': return category.isContact
    case 'specific_group': return category.sharedGroupIds.includes(rule.viewerTargetGroupId)
    case 'specific_contact_list': return category.sharedContactListIds.includes(rule.viewerTargetContactListId)
    default: return false
  }
}
```

**Key Logic Points:**

1. **Polymorphic Query**: Load both tasks and events separately with their policy assignments and rules
2. **Rule Priority**: Evaluate rules by priority (highest first) to find the first match
3. **Category Matching**: Check if the requester matches the rule's viewer scope (contact, group, etc.)
4. **Data Transformation**: Return different detail levels based on visibility rules (hidden/busy_only/title_only/full_details)
5. **Fallback Behavior**: Default to "Busy" with blocked scheduling if no policy template exists
6. **Type Safety**: Maintain activityType information throughout the pipeline for proper handling

### Commitment & Engagement System (Planned)

- **`commitment`**:
  - **Purpose**: Central table managing all invitations, acceptances, and engagements for tasks or events.
  - **Key Features**:
    - **Polymorphic Linking**: `activityType` + `activityId` references either tasks or events.
    - **Bidirectional Status**: Separate `hostStatus` and `inviteeStatus` (pending, accepted, declined, maybe).
    - **Auto-Accept Support**: `isAutoAccepted` flag, potentially driven by `profile_settings`.
    - **Messaging**: Optional message field for invitation context.
  - **Relationships**: Polymorphic links to tasks/events and direct links to users (host and invitee).

### Group Management System

- **`group_invitation_request`**:
  - **Purpose**: Manages both group invitations (owner/admin invites user) and join requests (user requests to join a group).
  - **Key Features**:
    - **Dual Purpose**: `type` enum (invite/request).
    - **Flexible Initiation**: `initiatorProfileId` tracks who started the process.
    - **Status Tracking**: Uses `ContactRequestStatus` enum.
  - **Relationships**: Links to `user_group`, `profile` (target user and initiator).

This revised database design provides a robust, scalable, and maintainable foundation for SkedAI, emphasizing data integrity, query efficiency, and a user-friendly approach to complex scheduling and privacy management.