// Skedai Database Schema - Separate Entity Architecture + Direct Relationships
// This DBML file reflects the separate tasks/events architecture with direct relationships
// for cross-cutting features like privacy policies and commitments

// ===== ENUMS =====

Enum TaskStatus {
  pending
  inProgress
  completed
  deferred
}

Enum EventStatus {
  active
  cancelled
  postponed
  delayed
}

Enum TaskEntityType {
  userSave
  aiGenerated
}

Enum AvailabilityScopeType {
  public
  allContacts
  specificContact
  specificGroup
  specificContactList
}

Enum ActivityDetailVisibilityLevel {
  hidden // Activity completely hidden from viewer
  busyOnly // Show as "Busy" with no details
  titleOnly // Show activity title but no description
  fullDetails // Show all activity details
}

// ===== PLANNED FUTURE ENUMS =====

Enum EngagementStatus {
  pending
  accepted
  declined
  maybe
}

Enum LogType {
  created
  archived
  deleted
  modified
}

Enum ContactRequestStatus {
  pending
  accepted
  declined
  blockedByRequester
  blockedByAddressee
}

Enum GroupMemberRole {
  member
  admin
}

Enum GroupInvitationRequestType {
  invite // Owner/Admin invited a user
  request // User requested to join
}

// ===== CORE AUTHENTICATION & USER TABLES (IMPLEMENTED) =====

Table auth_users {
  id uuid [primary key, default: `gen_random_uuid()`]
  username string [unique, not null]
  email string [unique, not null]
  passwordHash string [not null]
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table users {
  id uuid [primary key, default: `gen_random_uuid()`]
  authUserId uuid [unique, not null, ref: > auth_users.id]
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table profiles {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [unique, not null, ref: > auth_users.id]
  firstName string [not null]
  lastName string [not null]
  birthDate date [null]
  profilePicture string [null]
  countryCode string [null]
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table profile_settings {
  id uuid [primary key, default: `gen_random_uuid()`]
  profileId uuid [unique, not null, ref: > profiles.id]
  defaultAutoAcceptInvitations boolean [default: false]
  globalBusyMessage text [null]
  defaultPolicyTemplateId uuid [null, ref: > privacy_policy_templates.id]
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

// ===== AUTHENTICATION TOKENS (IMPLEMENTED) =====

Table auth_access_tokens {
  id integer [primary key, increment]
  tokenableId uuid [not null, ref: > auth_users.id]
  type string [not null]
  name string [null]
  hash string [not null]
  abilities text [not null]
  createdAt timestamp [null]
  updatedAt timestamp [null]
  lastUsedAt timestamp [null]
  expiresAt timestamp [null]
}

Table refresh_tokens {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [not null, ref: > auth_users.id]
  token string [unique, not null]
  revoked boolean [default: false]
  expiresAt timestamp [not null]
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
}

// ===== SEPARATE ENTITY SYSTEM (IMPLEMENTED) =====

Table tasks {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [not null, ref: > users.id]
  
  // Core fields
  title string [not null]
  description text [null]
  startDate timestamp [null]
  endDate timestamp [null]
  location jsonb [null]
  
  // Task-specific fields
  status TaskStatus [default: 'pending']
  priority string [null]
  remindBefore jsonb [null]
  tags jsonb [null]
  suggestedTags jsonb [null]
  people jsonb [null]
  locations jsonb [null]
  metadata jsonb [null]
  entityType TaskEntityType [default: 'userSave']
  parentTaskId uuid [null, ref: > tasks.id]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table events {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [not null, ref: > users.id]
  
  // Core fields
  title string [not null]
  description text [null]
  startDate timestamp [null]
  endDate timestamp [null]
  location jsonb [null]
  
  // Event-specific fields
  status EventStatus [default: 'active']
  maxAttendees integer [null]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

// ===== PRIVACY POLICY SYSTEM (IMPLEMENTED) =====

Table policy_categories {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [null, ref: > users.id] // null = system-wide context
  key string [not null] // "work_hours", "personal_time", etc.
  label string [not null] // "Work Hours", "Personal Time"
  description text [null]
  suggestedKeywords jsonb [null] // ["meeting", "call", "presentation"]
  isSystemContext boolean [default: false]
  isActive boolean [default: true]
  displayOrder integer [default: 0] // For UI ordering
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table privacy_policy_templates {
  id uuid [primary key, default: `gen_random_uuid()`]
  userId uuid [not null, ref: > users.id]
  name string [not null] // "Work Privacy", "Family Friendly", "Personal Time"
  description text [null]
  categoryId uuid [null, ref: > policy_categories.id]
  isDefault boolean [default: false]
  isSystemTemplate boolean [default: false]
  
  // Default behaviors for this template
  blocksScheduling boolean [default: true]
  defaultDetailVisibility ActivityDetailVisibilityLevel [default: 'busyOnly']
  defaultCustomMessage text [null]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table policy_template_rules {
  id uuid [primary key, default: `gen_random_uuid()`]
  templateId uuid [not null, ref: > privacy_policy_templates.id]
  viewerScopeType AvailabilityScopeType [not null]
  viewerTargetUserId uuid [null, ref: > users.id]
  viewerTargetGroupId uuid [null] // Future: ref to user_groups
  viewerTargetContactListId uuid [null] // Future: ref to contact_lists
  
  blocksScheduling boolean [default: true]
  detailVisibility ActivityDetailVisibilityLevel [default: 'busyOnly']
  customMessage text [null]
  priority integer [default: 0] // For conflict resolution
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
}

Table privacy_policy_assignments {
  id uuid [primary key, default: `gen_random_uuid()`]
  
  // Direct relationships to tasks or events (nullable, one must be set)
  taskId uuid [null, ref: > tasks.id]
  eventId uuid [null, ref: > events.id]
  
  templateId uuid [not null, ref: > privacy_policy_templates.id]
  
  // Override fields (optional, inherits from template if null)
  overrideBlocksScheduling boolean [null]
  overrideDetailVisibility ActivityDetailVisibilityLevel [null]
  overrideCustomMessage text [null]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  
  // Ensure exactly one entity is referenced
  note: 'Constraint: (taskId IS NOT NULL AND eventId IS NULL) OR (taskId IS NULL AND eventId IS NOT NULL)'
}

// ===== PLANNED FUTURE FEATURES (DIRECT RELATIONSHIPS) =====

// Commitments/Invitations System (PLANNED) - Direct relationships
Table commitments {
  id uuid [primary key, default: `gen_random_uuid()`]
  
  // Direct relationships to tasks or events (nullable, one must be set)
  taskId uuid [null, ref: > tasks.id]
  eventId uuid [null, ref: > events.id]
  
  hostUserId uuid [ref: > users.id]
  inviteeUserId uuid [ref: > users.id]
  inviteeStatus EngagementStatus [default: 'pending']
  hostStatus EngagementStatus [default: 'accepted']
  message string [null]
  isAutoAccepted boolean [default: false]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
  
  // Ensure exactly one entity is referenced
  note: 'Constraint: (taskId IS NOT NULL AND eventId IS NULL) OR (taskId IS NULL AND eventId IS NOT NULL)'
}

// Activity Logging System (PLANNED) - Direct audit trail
Table activity_log {
  id uuid [primary key, default: `gen_random_uuid()`]
  
  // Direct relationships to tasks or events (nullable, one must be set)
  taskId uuid [null, ref: > tasks.id]
  eventId uuid [null, ref: > events.id]
  
  logCode string [not null]
  logType LogType [not null]
  userId uuid [ref: > users.id]
  changes jsonb [null] // Store what changed
  metadata jsonb [null] // Additional context
  
  createdAt timestamp [not null, default: `now()`]
  
  // Ensure exactly one entity is referenced
  note: 'Constraint: (taskId IS NOT NULL AND eventId IS NULL) OR (taskId IS NULL AND eventId IS NOT NULL)'
}

// Activity Mentions System (PLANNED) - Direct mentions
Table mentions {
  id uuid [primary key, default: `gen_random_uuid()`]
  
  // Direct relationships to tasks or events (nullable, one must be set)
  taskId uuid [null, ref: > tasks.id]
  eventId uuid [null, ref: > events.id]
  
  userId uuid [ref: > users.id] // User being mentioned
  isRead boolean [default: false]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
  
  // Ensure exactly one entity is referenced
  note: 'Constraint: (taskId IS NOT NULL AND eventId IS NULL) OR (taskId IS NULL AND eventId IS NOT NULL)'
}

// Contact Management System (PLANNED)
Table user_contact {
  id uuid [primary key]
  requesterUserId uuid [ref: > users.id]
  addresseeUserId uuid [ref: > users.id]
  status ContactRequestStatus [default: 'pending']
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

// Group Management System (PLANNED)
Table user_group {
  id uuid [primary key]
  ownerUserId uuid [ref: > users.id]
  name string
  description string
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

Table group_member {
  id uuid [primary key]
  groupId uuid [ref: > user_group.id]
  userId uuid [ref: > users.id]
  role GroupMemberRole [default: 'member']
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

Table group_invitation_request {
  id uuid [primary key]
  groupId uuid [ref: > user_group.id]
  userId uuid [ref: > users.id]
  type GroupInvitationRequestType
  status ContactRequestStatus [default: 'pending']
  initiatorUserId uuid [ref: > users.id, null]
  message string
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

// Contact Lists System (PLANNED)
Table user_contact_list {
  id uuid [primary key]
  ownerUserId uuid [ref: > users.id]
  name string
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

Table contact_list_member {
  id uuid [primary key]
  listId uuid [ref: > user_contact_list.id]
  userId uuid [ref: > users.id]
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

// Availability Management System (PLANNED)
Table user_availability_slot {
  id uuid [primary key]
  userId uuid [ref: > users.id]
  title string
  dayOfWeek integer
  startTime time
  endTime time
  recurringStartDate date
  recurringEndDate date
  specificStartDate timestamp
  specificEndDate timestamp
  isAvailable boolean [default: true]
  visibilityScope AvailabilityScopeType
  visibilityTargetUserId uuid [ref: > users.id, null]
  visibilityTargetGroupId uuid [ref: > user_group.id, null]
  visibilityTargetContactListId uuid [ref: > user_contact_list.id, null]
  createdAt timestamp
  updatedAt timestamp
  deletedAt timestamp
}

// Tagging System (PLANNED) - Separate junction tables
Table tags {
  id uuid [primary key, default: `gen_random_uuid()`]
  name string [unique, not null]
  userId uuid [ref: > users.id, null] // null = global/system tag
  color string [null] // hex color for UI
  isSystemTag boolean [default: false]
  
  createdAt timestamp [not null, default: `now()`]
  updatedAt timestamp [not null, default: `now()`]
  deletedAt timestamp [null]
}

Table task_tags {
  id uuid [primary key, default: `gen_random_uuid()`]
  taskId uuid [ref: > tasks.id, not null]
  tagId uuid [ref: > tags.id, not null]
  createdAt timestamp [not null, default: `now()`]
  
  indexes {
    (taskId, tagId) [unique] // Prevent duplicate tags on same task
  }
}

Table event_tags {
  id uuid [primary key, default: `gen_random_uuid()`]
  eventId uuid [ref: > events.id, not null]
  tagId uuid [ref: > tags.id, not null]
  createdAt timestamp [not null, default: `now()`]
  
  indexes {
    (eventId, tagId) [unique] // Prevent duplicate tags on same event
  }
}

// ===== IMPLEMENTATION STATUS =====
// 
// IMPLEMENTED TABLES (23 tables):
// - Authentication: auth_users, users, profiles, profile_settings, auth_access_tokens, refresh_tokens  
// - Core Entities: tasks, events (separate tables with direct relationships)
// - Privacy System: policy_categories, privacy_policy_templates, policy_template_rules, privacy_policy_assignments
// - Contact System: user_contacts, user_contact_lists, contact_list_members
// - Group System: user_groups, group_members, group_invitation_requests
//
// PLANNED/FUTURE TABLES (11 tables):
// - Collaboration: commitments, activity_log, mentions
// - Availability: user_availability_slot
// - Tagging: tags, task_tags, event_tags
//
// ARCHITECTURE NOTES:
// 1. SEPARATE ENTITY DESIGN: Tasks and events are separate tables for optimal performance
// 2. DIRECT RELATIONSHIPS: Cross-cutting features use direct foreign keys (taskId/eventId) with constraints
// 3. UUID primary keys with gen_random_uuid() for all user-facing entities
// 4. Soft deletes implemented using deletedAt timestamp columns
// 5. Tasks and events contain all relevant fields directly (no inheritance)
// 6. Related tables use nullable foreign keys with CHECK constraints for integrity
// 7. Privacy policy system provides template-based rule engine for granular access control
// 8. Strategic database indexing for query performance
// 9. JSONB columns for flexible metadata storage
// 10. Authentication separated (auth_users) from business logic (users)
// 11. Column names follow camelCase (ORM), table names use snake_case (SQL)
// 12. Contact and group management with approval workflows
// 13. Tagging system uses separate junction tables for type safety and performance