# SkedAI Privacy Policy System - Performance Strategy & Implementation Plan

**Document Version**: 1.0  
**Created**: 2024  
**Last Updated**: 2024  
**Status**: Analysis Complete - Ready for Implementation

## Executive Summary

This document provides a comprehensive analysis and implementation strategy for the SkedAI privacy policy system's performance optimizations. The system implements a sophisticated template-based, rule-driven approach to activity visibility and scheduling permissions, designed to handle enterprise-scale workloads while maintaining sub-second response times.

### Key Performance Targets
- **Cache Hit**: 2 queries, <50ms response time, >90% hit rate
- **Cache Miss**: 3 queries, <150ms response time
- **Batch Requests**: <500ms for 7-day availability queries
- **Scalability**: O(1) per user, O(log n) for activities, O(k) for rules

---

## System Architecture Overview

### Four-Layer Privacy Control Architecture

```
1. Policy Categories (Organizational)
   ↓
2. Privacy Policy Templates (Reusable Configurations)
   ↓  
3. Policy Template Rules (Granular Permissions)
   ↓
4. Privacy Policy Assignments (Activity-Specific Applications)
```

### Core Components

#### 1. Policy Categories (`policy_categories`)
**Purpose**: Organizational buckets for templates to help users discover appropriate privacy configurations.

```typescript
interface PolicyCategory {
  key: string;              // "work_hours", "personal_time"
  label: string;            // "Work Hours", "Personal Time"
  description: string;      // Human-readable description
  suggestedKeywords: string[]; // ["meeting", "call", "presentation"]
  isSystemContext: boolean; // System-wide vs user-defined
  displayOrder: number;     // UI ordering
}
```

#### 2. Privacy Policy Templates (`privacy_policy_templates`)
**Purpose**: Reusable privacy configurations that users can apply to multiple activities.

```typescript
interface PrivacyPolicyTemplate {
  name: string;                    // "Work Hours", "Family Time"
  categoryId: string;              // Link to policy category
  isDefault: boolean;              // User's default template
  isSystemTemplate: boolean;       // System vs user-created
  
  // Default behaviors (can be overridden by rules)
  blocksScheduling: boolean;       // Prevents others from booking time
  defaultDetailVisibility: VisibilityLevel; // What viewers see by default
  defaultCustomMessage: string;    // Default message to show
}
```

#### 3. Policy Template Rules (`policy_template_rules`)
**Purpose**: Define specific permissions for different viewer relationships within templates.

```typescript
interface PolicyTemplateRule {
  templateId: string;
  viewerScopeType: AvailabilityScopeType; // Who this rule applies to
  viewerTargetContactListId?: string;     // Specific target references
  viewerTargetGroupId?: string;
  viewerTargetUserId?: string;
  
  // Rule-specific overrides
  blocksScheduling: boolean;       // Can this viewer book time?
  detailVisibility: VisibilityLevel; // What details can they see?
  customMessage?: string;          // Custom message for this rule
  priority: number;                // Conflict resolution (higher wins)
}
```

#### 4. Privacy Policy Assignments (`privacy_policy_assignments`)
**Purpose**: Link activities to templates with optional per-activity customization.

```typescript
interface PrivacyPolicyAssignment {
  activityId: string;              // Task or event ID
  activityType: 'task' | 'event'; // Activity type
  templateId: string;              // Applied template
  
  // Activity-specific overrides (optional)
  overrideBlocksScheduling?: boolean;
  overrideDetailVisibility?: VisibilityLevel;
  overrideCustomMessage?: string;
}
```

### Viewer Scope Types

```typescript
enum AvailabilityScopeType {
  PUBLIC = 'public',                    // Anyone on the internet
  ALL_CONTACTS = 'all_contacts',        // All accepted contacts
  SPECIFIC_CONTACT = 'specific_contact', // Individual person
  SPECIFIC_GROUP = 'specific_group',     // Work team, family, etc.
  SPECIFIC_CONTACT_LIST = 'specific_contact_list' // Custom contact lists
}
```

### Visibility Levels

```typescript
enum ActivityDetailVisibilityLevel {
  HIDDEN = 'hidden',           // Activity completely invisible
  BUSY_ONLY = 'busy_only',     // Shows "Busy" with no details
  TITLE_ONLY = 'title_only',   // Shows activity title only
  FULL_DETAILS = 'full_details' // Shows all activity information
}
```

---

## Performance Optimization Strategy

### Database Index Strategy

#### 1. Privacy Policy Templates
```sql
-- Core performance indexes
CREATE INDEX idx_templates_user_id ON privacy_policy_templates(user_id);
CREATE INDEX idx_templates_category_id ON privacy_policy_templates(category_id);
CREATE INDEX idx_templates_is_default ON privacy_policy_templates(is_default);
CREATE INDEX idx_templates_is_system ON privacy_policy_templates(is_system_template);
CREATE INDEX idx_templates_blocks_scheduling ON privacy_policy_templates(blocks_scheduling);
CREATE INDEX idx_templates_deleted_at ON privacy_policy_templates(deleted_at);
```

**Optimization Benefits**:
- Fast template retrieval by owner
- Quick category-based queries
- Instant default template lookup
- Efficient system-wide template access
- Soft delete support

#### 2. Policy Template Rules
```sql
-- Rule evaluation indexes
CREATE INDEX idx_rules_template_id ON policy_template_rules(template_id);
CREATE INDEX idx_rules_viewer_scope ON policy_template_rules(viewer_scope_type);
CREATE INDEX idx_rules_target_profile ON policy_template_rules(viewer_target_profile_id);
CREATE INDEX idx_rules_target_group ON policy_template_rules(viewer_target_group_id);
CREATE INDEX idx_rules_target_contact_list ON policy_template_rules(viewer_target_contact_list_id);
CREATE INDEX idx_rules_priority ON policy_template_rules(priority);

-- Composite index for priority-based rule resolution
CREATE INDEX idx_rules_template_priority ON policy_template_rules(template_id, priority DESC);
```

**Optimization Benefits**:
- Fast rule retrieval for policy evaluation
- Quick rule matching by viewer relationship
- Efficient conflict resolution with ordered priority
- Fast lookup by specific targets

#### 3. Privacy Policy Assignments
```sql
-- Activity-to-policy mapping indexes
CREATE INDEX idx_assignments_activity ON privacy_policy_assignments(activity_id, activity_type);
CREATE INDEX idx_assignments_template ON privacy_policy_assignments(template_id);

-- Unique constraint prevents duplicate assignments
UNIQUE INDEX unique_activity_template ON privacy_policy_assignments(activity_id, activity_type, template_id);
```

#### 4. Activity Tables (Tasks & Events)
```sql
-- Activity query optimization
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_start_date ON tasks(start_date);
CREATE INDEX idx_tasks_end_date ON tasks(end_date);
CREATE INDEX idx_tasks_deleted_at ON tasks(deleted_at);

CREATE INDEX idx_events_user_id ON events(user_id);
CREATE INDEX idx_events_start_date ON events(start_date);
CREATE INDEX idx_events_end_date ON events(end_date);
CREATE INDEX idx_events_deleted_at ON events(deleted_at);
```

#### 5. Contact Relationship Indexes
```sql
-- Contact relationship optimization
CREATE INDEX idx_contacts_requester ON user_contact(requester_user_id);
CREATE INDEX idx_contacts_addressee ON user_contact(addressee_user_id);
CREATE INDEX idx_contacts_status ON user_contact(status);

-- Composite indexes for relationship context queries
CREATE INDEX idx_contacts_requester_status ON user_contact(requester_user_id, status);
CREATE INDEX idx_contacts_addressee_status ON user_contact(addressee_user_id, status);
```

### Redis Caching Architecture

#### Cache Strategy
```typescript
// Cache key pattern for relationship context
const cacheKey = `rel_ctx:${requesterUserId}:${targetUserId}`;

// Cached data structure
interface RelationshipContext {
  isContact: boolean;
  contactLists: string[];
  groups: string[];
  contactStatus: 'accepted' | 'pending' | 'blocked' | null;
  lastUpdated: DateTime;
}

// Cache configuration
const cacheConfig = {
  ttl: 15 * 60, // 15 minutes
  hitRateTarget: 0.90, // >90%
  invalidationEvents: [
    'contact.accepted',
    'contact.blocked', 
    'contactList.memberAdded',
    'contactList.memberRemoved',
    'group.memberAdded',
    'group.memberRemoved'
  ]
};
```

### Optimized Query Flow

```mermaid
graph TD
    A[Request: Get Availability] --> B{Redis Cache Hit?}
    B -->|Yes| C[2 Queries Total]
    B -->|No| D[3 Queries Total]
    
    C --> E[Query 1: Activities + Policy Data]
    C --> F[Query 2: Availability Slots]
    
    D --> G[Query 1: Relationship Context]
    D --> H[Cache Context Data]
    D --> E
    D --> F
    
    E --> I[In-Memory Policy Evaluation]
    F --> I
    G --> I
    
    I --> J[Filtered Results]
    
    style B fill:#e1f5fe
    style C fill:#c8e6c9
    style D fill:#ffcdd2
```

### Single Query Activity + Policy Loading

```typescript
// Optimized query with eager loading
const activities = await Task.query()
  .where('userId', targetUserId)
  .whereBetween('startDate', [dayStart, dayEnd])
  .whereNull('deletedAt')
  .preload('policyAssignment', (assignmentQuery) => {
    assignmentQuery.preload('template', (templateQuery) => {
      templateQuery.preload('rules', (rulesQuery) => {
        rulesQuery.orderBy('priority', 'desc');
      });
    });
  });
```

**Benefits**:
- Single database round-trip for all policy data
- Eager loading prevents N+1 query problems
- Priority ordering enables efficient rule resolution

### Relationship Context CTE Query

```sql
-- Single query to fetch all relationship context
WITH relationship_context AS (
  SELECT 
    'contact' as type,
    CASE 
      WHEN uc.status = 'accepted' THEN true 
      ELSE false 
    END as is_active,
    uc.status as contact_status
  FROM user_contact uc
  WHERE (uc.requester_user_id = $1 AND uc.addressee_user_id = $2)
     OR (uc.requester_user_id = $2 AND uc.addressee_user_id = $1)
  
  UNION ALL
  
  SELECT 
    'contact_list' as type,
    true as is_active,
    ucl.id as list_id
  FROM user_contact_list ucl
  JOIN contact_list_member clm ON ucl.id = clm.contact_list_id
  WHERE ucl.owner_user_id = $2 AND clm.user_id = $1
)
SELECT * FROM relationship_context;
```

---

## Policy Evaluation Algorithm

### Step-by-Step Process

1. **Template Selection**
   - Activity → Privacy Policy Assignment → Privacy Policy Template

2. **Rule Matching**
   - Determine requester's relationship to activity owner
   - Find matching rules based on `viewerScopeType` and target IDs
   - Apply rule with highest `priority` if multiple matches

3. **Override Application**
   - Apply any activity-specific overrides from the assignment
   - Return final visibility and scheduling permissions

### Implementation Example

```typescript
class PolicyEvaluationService {
  async evaluateActivityVisibility(
    activityId: string,
    activityType: 'task' | 'event',
    requesterUserId: string,
    targetUserId: string
  ): Promise<ActivityVisibilityResult> {
    
    // 1. Get relationship context (cached)
    const relationshipContext = await this.getRelationshipContext(
      requesterUserId, 
      targetUserId
    );
    
    // 2. Get activity with policy data (single query)
    const activity = await this.getActivityWithPolicy(activityId, activityType);
    
    // 3. Evaluate policy rules in memory
    const applicableRules = this.findApplicableRules(
      activity.policyAssignment.template.rules,
      relationshipContext
    );
    
    // 4. Apply highest priority rule
    const effectiveRule = applicableRules.sort((a, b) => b.priority - a.priority)[0];
    
    // 5. Apply assignment overrides
    const finalVisibility = this.applyOverrides(
      effectiveRule,
      activity.policyAssignment
    );
    
    return finalVisibility;
  }
}
```

---

## Real-World Example

### Sarah's "Work Hours" Template

```json
{
  "template": {
    "name": "Work Hours",
    "blocksScheduling": true,
    "defaultDetailVisibility": "title_only"
  },
  "rules": [
    {
      "viewerScopeType": "specific_contact_list",
      "targetContactList": "work-colleagues",
      "detailVisibility": "full_details",
      "blocksScheduling": false,
      "priority": 10
    },
    {
      "viewerScopeType": "all_contacts", 
      "detailVisibility": "title_only",
      "blocksScheduling": true,
      "priority": 5
    },
    {
      "viewerScopeType": "public",
      "detailVisibility": "busy_only", 
      "blocksScheduling": true,
      "priority": 1
    }
  ]
}
```

### Visibility Results for Sarah's 2:00 PM Meeting

| Viewer Type | Visibility | Title | Details | Can Book? |
|-------------|------------|-------|---------|-----------|
| Work Colleague | `full_details` | "Client Strategy Meeting" | Full description | ✅ Yes |
| Personal Contact | `title_only` | "Client Strategy Meeting" | Hidden | ❌ No |
| Public User | `busy_only` | "Busy" | Hidden | ❌ No |
| Family Member | `title_only` | "Client Strategy Meeting" | Hidden | ❌ No |

---

## Performance Metrics & Monitoring

### Performance Targets

```typescript
interface PerformanceTargets {
  cacheHit: {
    totalQueries: 2;
    responseTime: '<50ms';
    cacheHitRate: '>90%';
  };
  cacheMiss: {
    totalQueries: 3;
    responseTime: '<150ms';
  };
  batchRequests: {
    firstDay: 3; // Cache miss
    subsequentDays: 2; // Cache hits
    totalTime: '<500ms for 7 days';
  };
}
```

### Key Metrics to Monitor

```typescript
interface PerformanceMetrics {
  cacheHitRate: number;         // Target: >90%
  avgQueryTime: number;         // Target: <100ms
  p95QueryTime: number;         // Target: <250ms
  indexUsageRatio: number;      // Target: >95%
  connectionPoolUtilization: number; // Target: <80%
}
```

### Performance Degradation Indicators

1. **Cache hit rate drops** below 85%
2. **Query times exceed** 200ms consistently  
3. **Index scans decrease** relative to sequential scans
4. **Connection pool exhaustion** during peak usage

### Database Performance Monitoring

```sql
-- Index usage verification
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN (
  'privacy_policy_templates',
  'policy_template_rules', 
  'privacy_policy_assignments',
  'tasks',
  'events',
  'user_contact'
)
ORDER BY idx_scan DESC;
```

---

## Advanced Optimization Techniques

### Composite Index Strategy

```sql
-- Multi-column indexes for complex queries
CREATE INDEX idx_rules_template_scope_priority 
ON policy_template_rules(template_id, viewer_scope_type, priority DESC);

CREATE INDEX idx_assignments_activity_template 
ON privacy_policy_assignments(activity_id, activity_type, template_id);
```

### Partial Indexes for Common Patterns

```sql
-- Index only active templates
CREATE INDEX idx_templates_active 
ON privacy_policy_templates(user_id, is_default) 
WHERE deleted_at IS NULL;

-- Index only accepted contacts
CREATE INDEX idx_contacts_accepted 
ON user_contact(requester_user_id, addressee_user_id) 
WHERE status = 'accepted';
```

### JSONB Optimization

```sql
-- GIN indexes for JSONB fields in tasks/events
CREATE INDEX idx_tasks_tags_gin ON tasks USING GIN(tags);
CREATE INDEX idx_tasks_metadata_gin ON tasks USING GIN(metadata);
```

---

## Implementation Roadmap

### Phase 1: Core Infrastructure (Weeks 1-2)
- [ ] Implement Redis caching layer
- [ ] Add comprehensive database indexes
- [ ] Create relationship context CTE query
- [ ] Set up performance monitoring

### Phase 2: Query Optimization (Weeks 3-4)
- [ ] Implement eager loading for policy data
- [ ] Add composite indexes for complex queries
- [ ] Optimize rule evaluation algorithm
- [ ] Add query performance logging

### Phase 3: Caching Strategy (Weeks 5-6)
- [ ] Implement cache invalidation events
- [ ] Add cache warming strategies
- [ ] Optimize cache key patterns
- [ ] Add cache performance metrics

### Phase 4: Testing & Validation (Weeks 7-8)
- [ ] Load testing with realistic data volumes
- [ ] Performance regression testing
- [ ] Cache hit rate optimization
- [ ] Production monitoring setup

### Phase 5: Advanced Features (Weeks 9-10)
- [ ] Partial indexes for edge cases
- [ ] Query plan optimization
- [ ] Horizontal scaling preparation
- [ ] Advanced monitoring dashboards

---

## Testing Strategy

### Performance Test Scenarios

```typescript
// Cache performance test
describe('Privacy Policy Performance', () => {
  it('should achieve target performance metrics', async () => {
    // First request (cache miss) - should be 3 queries
    const result1 = await availabilityService.getAvailabilityForDay(
      'user-001', 'user-002', DateTime.fromISO('2024-03-20')
    );
    expect(result1.queryCount).toBe(3);
    expect(result1.responseTime).toBeLessThan(150);
    
    // Second request (cache hit) - should be 2 queries  
    const result2 = await availabilityService.getAvailabilityForDay(
      'user-001', 'user-002', DateTime.fromISO('2024-03-21')
    );
    expect(result2.queryCount).toBe(2);
    expect(result2.responseTime).toBeLessThan(50);
    expect(result2.cacheHit).toBe(true);
  });
});
```

### Load Testing Scenarios

1. **Single User Load**: 1000 requests/minute per user
2. **Multi-User Load**: 100 concurrent users
3. **Batch Request Load**: Weekly availability queries
4. **Cache Invalidation Load**: Relationship changes during peak usage

---

## Scalability Considerations

### Horizontal Scaling Strategy

1. **Read Replicas**: Route policy evaluation queries to read replicas
2. **Cache Clustering**: Redis cluster for high availability
3. **Database Sharding**: Partition by user ID for extreme scale
4. **CDN Integration**: Cache static policy templates

### Multi-Tenant Optimization

1. **Tenant Isolation**: Separate Redis namespaces per tenant
2. **Resource Limits**: Per-tenant query limits and rate limiting
3. **Performance Isolation**: Dedicated connection pools per tenant
4. **Monitoring Segmentation**: Tenant-specific performance metrics

---

## Security Considerations

### Data Protection

1. **Cache Encryption**: Encrypt sensitive relationship data in Redis
2. **Query Parameterization**: Prevent SQL injection in dynamic queries
3. **Access Control**: Validate user permissions before policy evaluation
4. **Audit Logging**: Log all policy evaluation decisions

### Privacy Compliance

1. **Data Minimization**: Cache only necessary relationship data
2. **Retention Policies**: Automatic cache expiration and cleanup
3. **Right to Deletion**: Immediate cache invalidation on user deletion
4. **Consent Management**: Respect user privacy preferences

---

## Conclusion

This performance strategy provides a comprehensive approach to scaling the SkedAI privacy policy system for enterprise workloads. The combination of strategic indexing, intelligent caching, and optimized query patterns achieves the target performance metrics while maintaining the sophisticated privacy controls that make the system valuable.

### Key Success Factors

1. **Comprehensive Indexing**: Strategic database indexes for all query patterns
2. **Intelligent Caching**: Redis-based relationship context caching with event-driven invalidation
3. **Query Optimization**: Single-query data loading with eager loading relationships
4. **Performance Monitoring**: Continuous monitoring with automated alerting
5. **Scalable Architecture**: Horizontal scaling preparation for future growth

### Next Steps

1. **Implement Phase 1** of the roadmap to establish core infrastructure
2. **Set up monitoring** to establish baseline performance metrics
3. **Begin load testing** with realistic data volumes
4. **Iterate and optimize** based on real-world usage patterns

This strategy ensures the privacy policy system can handle enterprise-scale workloads while maintaining the user experience and sophisticated privacy controls that differentiate SkedAI in the market.