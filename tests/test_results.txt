c[ info ] booting application to run tests...

functional / Contact Basic (app/features/contact/tests/functional/contact-basic.spec.ts)
  ✔ user can view contacts (123.09ms)
✅ Requester found: 7746f0b0-71c9-49a6-92ea-0e3e5bfc4cd4
✅ Addressee found: 8bfb039e-9431-4a2f-ad7e-bc3714b08bbc
  ✔ user can send contact request (113.41ms)
  ✔ user can create contact list (54.81ms)
  ✔ user can view contact lists (50.27ms)
  ✔ contact operations require authentication (3.33ms)

functional / Event Basic (app/features/event/tests/functional/event-basic.spec.ts)
  ✔ user can create event (59.62ms)
  ✔ user can create minimal event with only title (51.38ms)
Error creating event: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:277:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at EventController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/event/event_controller.ts:30:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The title field must be defined',
      rule: 'required',
      field: 'title'
    }
  ]
}
Error creating event: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:277:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at EventController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/event/event_controller.ts:30:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The title field must not be greater than 255 characters',
      rule: 'maxLength',
      field: 'title',
      meta: [Object]
    }
  ]
}
Error creating event: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:277:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at EventController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/event/event_controller.ts:30:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The selected status is invalid',
      rule: 'in',
      field: 'status',
      meta: [Object]
    }
  ]
}
  ✔ event creation validates required fields (64.93ms)
  ✔ user can get specific event by ID (58.92ms)
  ✔ user cannot access other users events (104.6ms)
  ✔ user can list their events (64.64ms)
  ✔ event listing supports pagination (67.54ms)
  ✔ event listing supports status filtering (61.35ms)
  ✔ event listing supports search (62.28ms)
  ✔ user can update event (57.53ms)
  ✔ user can partially update event (55.32ms)
  ✔ user cannot update other users events (101.15ms)
  ✔ user can delete event (57.49ms)
  ✔ user cannot delete other users events (96.47ms)
  ✔ event operations require authentication (2.71ms)
  ✖ invalid event ID returns 400 (55.1ms)

functional / Group Basic (app/features/group/tests/functional/group-basic.spec.ts)
  ✔ user can create group (60.17ms)
  ✔ user can view groups (49.02ms)
  ✔ user can view group invitations (56.42ms)
  ✔ user can view join requests (48.3ms)
  ✔ group operations require authentication (1.68ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:163:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/group/groups/group_controller.ts:56:23)
    at GroupController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must be defined',
      rule: 'required',
      field: 'name'
    }
  ]
}
  ✔ group validation works (49.63ms)
  ✔ group owner can manage members and invitations (120.18ms)
  ✔ user can invite someone to group (103.18ms)
  ✔ invited user can respond to invitation (119.43ms)

functional / Group Collaboration (app/features/group/tests/functional/group-collaboration.spec.ts)
  ✔ host can create group for collaboration testing (8.37ms)
  ✔ host can send group invitation to another user (9.24ms)
  ✔ invitee can view received group invitations (6ms)
  ✔ invitee can respond to group invitation (accept) (5.73ms)
  ✔ invitee can respond to group invitation (decline) (5.75ms)
  ✔ user can create join request for group (53.95ms)
  ✔ host can view join requests for their group (6.61ms)
  ✔ user can add member to group (53.92ms)
  ✔ user can remove member from group (5.52ms)
  ✔ collaboration operations require authentication (2.35ms)

functional / Policy Category Basic (app/features/privacy-policy/tests/functional/policy-category-basic.spec.ts)
  ✔ user can create policy category (64.21ms)
  ✔ user can create minimal policy category with only name (52.83ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:160:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyCategoryController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/category/policy_category_controller.ts:82:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must be defined',
      rule: 'required',
      field: 'name'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:160:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyCategoryController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/category/policy_category_controller.ts:82:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must not be greater than 255 characters',
      rule: 'maxLength',
      field: 'name',
      meta: [Object]
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:160:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyCategoryController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/category/policy_category_controller.ts:82:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must be defined',
      rule: 'required',
      field: 'name'
    }
  ]
}
  ✔ policy category creation validates required fields (57.83ms)
resourceId 7bda1782-8274-4b4e-bffb-1cdfe16cd0d7
  ✔ user can get specific policy category by ID (57.33ms)
resourceId 7c56ba2a-79ea-47fb-87f8-17a7be9c1120
[BaseController] Handling error: Exception: You do not have permission to access this policy category
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot access other users policy categories (103.05ms)
  ✔ user can list their policy categories (67.21ms)
  ✖ policy category listing supports pagination (78.01ms)
  ✔ policy category listing supports search (60.45ms)
  ✔ policy category listing supports sorting (62.74ms)
  ✔ user can update policy category (57.6ms)
  ✔ user can partially update policy category (78.27ms)
[13:34:22.889] WARN (13738): Invitation/request not found
    request_id: "c8m7cvlv0xgdwxw1q7645sdt"
    x-request-id: "c8m7cvlv0xgdwxw1q7645sdt"
[BaseController] Handling error: Exception: You do not have permission to access this policy category
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at Module.validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PolicyCategoryController.update (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/category/policy_category_controller.ts:115:7) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot update other users policy categories (104.7ms)
resourceId 34065ce0-6f00-4123-9680-215c29849efd
[BaseController] Handling error: Exception: policy category not found
    at notFoundException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:51:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:76:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 404,
  code: 'E_NOT_FOUND'
}
  ✔ user can delete policy category (59.78ms)
[BaseController] Handling error: Exception: You do not have permission to access this policy category
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:252:7) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot delete other users policy categories (98.11ms)
  ✔ policy category operations require authentication (2.22ms)
  ✔ key generation handles special characters (50.22ms)

functional / Policy Template Rules Basic (app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can create policy template rule (67.87ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can create minimal policy rule (58.18ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    },
    {
      message: 'The selected viewerScopeType is invalid',
      rule: 'enum',
      field: 'viewerScopeType',
      meta: [Object]
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    },
    {
      message: 'The selected detailVisibility is invalid',
      rule: 'enum',
      field: 'detailVisibility',
      meta: [Object]
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    },
    {
      message: 'The priority field must not be greater than 100',
      rule: 'max',
      field: 'priority',
      meta: [Object]
    }
  ]
}
  ✔ policy rule creation validates required fields and enums (66.32ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can get specific policy rule by ID (55.74ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user cannot access other users policy rules (109.14ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can list their policy rules (68.22ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can update policy rule (56.15ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ user can delete policy rule (56.83ms)
  ✔ policy rule operations require authentication (5.26ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ policy rules support priority-based precedence (64.36ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ policy rules with same priority are handled consistently (65.54ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ policy rules with scope-specific target validation (64.54ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ policy rules conflict resolution through priority ordering (64.24ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:323:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PolicyTemplateRuleController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/rule/policy_template_rule_controller.ts:72:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The templateId field must be defined',
      rule: 'required',
      field: 'templateId'
    }
  ]
}
  ✖ policy rules support all valid enum values (59.89ms)

functional / Privacy Policy Template Basic (app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts)
  ✖ user can create privacy policy template (56.27ms)
  ✔ user can create privacy template with policy category (64.61ms)
  ✔ user can create minimal privacy template with only name (55.01ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:232:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PrivacyPolicyTemplateController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/template/privacy_policy_template_controller.ts:110:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must be defined',
      rule: 'required',
      field: 'name'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:232:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PrivacyPolicyTemplateController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/template/privacy_policy_template_controller.ts:110:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The name field must not be greater than 255 characters',
      rule: 'maxLength',
      field: 'name',
      meta: [Object]
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:232:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at PrivacyPolicyTemplateController.store (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/privacy-policy/template/privacy_policy_template_controller.ts:110:34) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The policyCategoryId field must be a valid UUID',
      rule: 'uuid',
      field: 'policyCategoryId'
    }
  ]
}
  ✔ privacy template creation validates required fields (58.6ms)
  ✖ user can get specific privacy template by ID (52.56ms)
  ✖ user cannot access other users privacy templates (104.21ms)
  ✖ user can list their privacy templates (67.8ms)
  ✖ privacy template listing supports pagination (80.96ms)
  ✖ privacy template listing supports isDefault filtering (62.72ms)
  ✖ privacy template listing supports search (62.17ms)
  ✖ privacy template listing supports sorting (59.97ms)
  ✖ user can update privacy template (54.26ms)
  ✖ user can partially update privacy template (55.07ms)
  ✖ user cannot update other users privacy templates (97.98ms)
  ✖ user can delete privacy template (52.17ms)
  ✖ user cannot delete other users privacy templates (98.59ms)
  ✔ privacy template operations require authentication (5.43ms)
  ✖ privacy template with category relationship (67.32ms)

functional / Profile Basic (app/features/profile/tests/functional/profile-basic.spec.ts)
[ProfileService:createOrUpdate] userId: 42dc33c3-26d6-4afb-86a2-028abb3df740
[ProfileService:createOrUpdate] ✅ User found: 42dc33c3-26d6-4afb-86a2-028abb3df740
  ✔ user can create profile (70.03ms)
[ProfileService:createOrUpdate] userId: 9fb04347-3e67-427a-96cb-1a041319b17f
[ProfileService:createOrUpdate] ✅ User found: 9fb04347-3e67-427a-96cb-1a041319b17f
resourceId 9fb04347-3e67-427a-96cb-1a041319b17f
  ✔ user can retrieve existing profile (71.55ms)
resourceId 5725b601-fc6c-4233-9201-53def05cf1e1
[BaseController] Handling error: Exception: profile not found
    at notFoundException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:51:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:76:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 404,
  code: 'E_NOT_FOUND'
}
  ✔ returns 404 when profile does not exist (52.43ms)
  ✔ profile operations require authentication (1.21ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:235:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_controller.ts:62:68)
    at <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The lastName field must be defined',
      rule: 'required',
      field: 'lastName'
    }
  ]
}
  ✔ profile validation works (49.52ms)

functional / Profile Settings Basic (app/features/profile/tests/functional/profile-settings-basic.spec.ts)
[ProfileService:createOrUpdate] userId: 3fb30390-7e3b-4015-8d1a-8af4cd4c68c6
[ProfileService:createOrUpdate] ✅ User found: 3fb30390-7e3b-4015-8d1a-8af4cd4c68c6
  ✔ user can create profile settings (61.46ms)
[ProfileService:createOrUpdate] userId: ea794622-21da-4808-9d6f-b611bdc39696
[ProfileService:createOrUpdate] ✅ User found: ea794622-21da-4808-9d6f-b611bdc39696
  ✔ user can create profile settings with privacy policy template (61.85ms)
[ProfileService:createOrUpdate] userId: b9f1c6ac-b8b9-4ee5-9522-c21571329075
[ProfileService:createOrUpdate] ✅ User found: b9f1c6ac-b8b9-4ee5-9522-c21571329075
  ✔ user can create minimal profile settings with only profileId (54.39ms)
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:201:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_settings_controller.ts:119:23)
    at <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The profileId field must be defined',
      rule: 'required',
      field: 'profileId'
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:201:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_settings_controller.ts:119:23)
    at <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The profileId field must be a valid UUID',
      rule: 'uuid',
      field: 'profileId'
    }
  ]
}
[ProfileService:createOrUpdate] userId: e02f2637-fc1d-4598-93d6-dc6ec9c5d4a8
[ProfileService:createOrUpdate] ✅ User found: e02f2637-fc1d-4598-93d6-dc6ec9c5d4a8
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:201:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_settings_controller.ts:119:23)
    at <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The globalBusyMessage field must not be greater than 255 characters',
      rule: 'maxLength',
      field: 'globalBusyMessage',
      meta: [Object]
    }
  ]
}
[BaseController] Handling error: ValidationError: Validation failure
    at SimpleErrorReporter.createError (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/reporters/simple_error_reporter.ts:76:12)
    at eval (eval at #toAsyncFunction (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/compiler/build/index.js:1023:12), <anonymous>:201:23)
    at VineValidator.VineValidator.validate (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@vinejs/vine/src/vine/validator.ts:157:16)
    at RequestValidator.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/modules/http/request_validator.js:64:26)
    at Request.validateUsing (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/core/build/providers/vinejs_provider.js:96:51)
    at Object.validateCreatePayload (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_settings_controller.ts:119:23)
    at <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:289:24) {
  status: 422,
  code: 'E_VALIDATION_ERROR',
  messages: [
    {
      message: 'The defaultPolicyTemplateId field must be a valid UUID',
      rule: 'uuid',
      field: 'defaultPolicyTemplateId'
    }
  ]
}
  ✔ profile settings creation validates required fields (63.46ms)
[ProfileService:createOrUpdate] userId: aeb9db9a-7ddb-4212-956b-9e5c250909f1
[ProfileService:createOrUpdate] ✅ User found: aeb9db9a-7ddb-4212-956b-9e5c250909f1
  ✖ user cannot create duplicate profile settings for same profile (58.52ms)
[ProfileService:createOrUpdate] userId: bcf10ca2-7599-4d08-89a2-cb74e610418c
[ProfileService:createOrUpdate] ✅ User found: bcf10ca2-7599-4d08-89a2-cb74e610418c
[BaseController] Handling error: Exception: Profile not found or access denied
    at notFoundException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:51:21)
    at Object.createResource (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/profile/profile_settings_controller.ts:85:17)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:292:24) {
  status: 404,
  code: 'E_NOT_FOUND'
}
  ✔ user cannot create settings for other users profiles (99.46ms)
[ProfileService:createOrUpdate] userId: 637b679d-a919-4225-b610-f2f7d0e6cb44
[ProfileService:createOrUpdate] ✅ User found: 637b679d-a919-4225-b610-f2f7d0e6cb44
resourceId 821dcc1a-6671-42c1-8dcc-393daf6d8e16
  ✔ user can get specific profile settings by ID (59.86ms)
[ProfileService:createOrUpdate] userId: 39310399-1748-4e43-be33-d7de89a7546a
[ProfileService:createOrUpdate] ✅ User found: 39310399-1748-4e43-be33-d7de89a7546a
resourceId 177a61cd-0034-427a-8527-3e0cb1eb1490
[BaseController] Handling error: Exception: You do not have permission to access this profile settings
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot access other users profile settings (115.32ms)
[ProfileService:createOrUpdate] userId: d4ddc55c-ee78-4012-a252-78bdf40e16a1
[ProfileService:createOrUpdate] ✅ User found: d4ddc55c-ee78-4012-a252-78bdf40e16a1
  ✔ user can update profile settings (72.15ms)
[ProfileService:createOrUpdate] userId: 8e75d508-01d0-4d72-89c7-84842d87f62d
[ProfileService:createOrUpdate] ✅ User found: 8e75d508-01d0-4d72-89c7-84842d87f62d
  ✔ user can partially update profile settings (68.77ms)
[ProfileService:createOrUpdate] userId: 3ca1052d-92fd-45e8-aa98-dc36309b5937
[ProfileService:createOrUpdate] ✅ User found: 3ca1052d-92fd-45e8-aa98-dc36309b5937
  ✔ user can update privacy policy template reference (74.58ms)
[ProfileService:createOrUpdate] userId: 1d58ae10-2177-4d90-aec6-e71d5f33059f
[ProfileService:createOrUpdate] ✅ User found: 1d58ae10-2177-4d90-aec6-e71d5f33059f
[BaseController] Handling error: Exception: You do not have permission to access this profile settings
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:329:7) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot update other users profile settings (118.47ms)
[ProfileService:createOrUpdate] userId: afeb763a-7370-4307-95e6-ac9ebd07857b
[ProfileService:createOrUpdate] ✅ User found: afeb763a-7370-4307-95e6-ac9ebd07857b
resourceId e965daf2-52c4-48eb-a1aa-a29ee711b1b2
[BaseController] Handling error: Exception: profile settings not found
    at notFoundException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:51:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:76:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 404,
  code: 'E_NOT_FOUND'
}
  ✔ user can delete profile settings (68.64ms)
[ProfileService:createOrUpdate] userId: bfc8fdf9-9d84-4f9e-9ec2-92dbea5da714
[ProfileService:createOrUpdate] ✅ User found: bfc8fdf9-9d84-4f9e-9ec2-92dbea5da714
[BaseController] Handling error: Exception: You do not have permission to access this profile settings
    at authorizationException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:37:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:79:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:252:7) {
  status: 403,
  code: 'E_UNAUTHORIZED'
}
  ✔ user cannot delete other users profile settings (113.67ms)
  ✔ profile settings operations require authentication (2.54ms)
[ProfileService:createOrUpdate] userId: 359c3179-2794-4050-aafa-706a8a329127
[ProfileService:createOrUpdate] ✅ User found: 359c3179-2794-4050-aafa-706a8a329127
  ✖ profile settings enforce one-to-one relationship with profile (65.05ms)
[ProfileService:createOrUpdate] userId: bd2ae5c9-f6b7-41c7-bb92-663b03f44f4c
[ProfileService:createOrUpdate] ✅ User found: bd2ae5c9-f6b7-41c7-bb92-663b03f44f4c
resourceId 945b8f4d-7d79-4be7-a013-0cc997861f35
resourceId 945b8f4d-7d79-4be7-a013-0cc997861f35
  ✔ profile settings with privacy template integration (83.66ms)
[ProfileService:createOrUpdate] userId: 15e8e51a-83a5-4260-ade8-1b74cc01e521
[ProfileService:createOrUpdate] ✅ User found: 15e8e51a-83a5-4260-ade8-1b74cc01e521
  ✖ profile settings cannot reference other users privacy templates (109.88ms)

functional / Authentication Basic (app/features/session/tests/functional/auth-basic.spec.ts)
  ✔ user can register successfully (31.74ms)
  ✔ user can login with valid credentials (53.37ms)
Login error: E_INVALID_CREDENTIALS: Invalid user credentials
    at AuthUser.verifyCredentials (file:///Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/node_modules/@adonisjs/auth/build/src/mixins/lucid.js:43:17)
    at async SessionService.loginUser (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/session/session_service.ts:65:22)
    at async SessionController.login (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/features/session/session_controller.ts:30:57) {
  status: 400,
  code: 'E_INVALID_CREDENTIALS',
  identifier: 'errors.E_INVALID_CREDENTIALS'
}
  ✔ login with invalid credentials fails (23.95ms)
  ✔ protected routes require authentication (0.65ms)
resourceId 1585e62d-e634-4fbb-a852-caa749a27355
[BaseController] Handling error: Exception: profile not found
    at notFoundException (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/utils/error.ts:51:21)
    at validateResourceOwnership (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:76:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Volumes/Keen/Desk/Career/Personal/Projects/Skedai/backend/skedai-adonisjs/app/controllers/base_controller.ts:216:24) {
  status: 404,
  code: 'E_NOT_FOUND'
}
  ✔ valid token can access protected routes (76.34ms)

functional / Task Basic (app/features/task/tests/functional/task-basic.spec.ts)
(node:13738) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
  ✔ user can list tasks (202.1ms)
genPrompt Create a simple test task. System context: Current datetime is 2025-06-29T13:34:28.135Z. Current day is Sunday.
  ✔ user can create task via AI prompt (2s)
  ✔ task operations require authentication (3.72ms)
 ERRORS 

  ❯ Event Basic / invalid event ID returns 400

  Assertion Error: expected 200 to be one of [ 400, 404, 500 ]

  Comparing two different types of values. Expected array but received number.

   at Object.executor app/features/event/tests/functional/event-basic.spec.ts:428
   426|  
   427|        // Should return error status for invalid/non-existent IDs
 ❯ 428|        assert.oneOf(getResponse.response.status, [400, 404, 500])
   429|        assert.oneOf(updateResponse.response.status, [400, 404, 500])
   430|        assert.oneOf(deleteResponse.response.status, [400, 404, 500])
[1/31]─

  ❯ Policy Category Basic / policy category listing supports pagination

  Assertion Error: expected undefined to equal 1

  Comparing two different types of values. Expected number but received undefined.

   at Object.executor app/features/privacy-policy/tests/functional/policy-category-basic.spec.ts:210
   208|      assert.equal(body.data.length, 2)
   209|      assert.exists(body.pagination)
 ❯ 210|      assert.equal(body.pagination.page, 1)
   211|      assert.equal(body.pagination.perPage, 2)
   212|      assert.isAtLeast(body.pagination.total, 5)
[2/31]─

  ❯ Policy Template Rules Basic / user can create policy template rule

  Assertion Error: expected 422 to equal 201

- Expected  - 1
+ Received  + 1

- 201
+ 422

   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:94
   92|        .json(ruleData)
   93|  
 ❯ 94|      response.assertStatus(201)
   95|      const body = response.body()
   96|
[3/31]─

  ❯ Policy Template Rules Basic / user can create minimal policy rule

  Assertion Error: expected 422 to equal 201

- Expected  - 1
+ Received  + 1

- 201
+ 422

   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:127
   125|        .json(ruleData)
   126|  
 ❯ 127|      response.assertStatus(201)
   128|      const body = response.body()
   129|
[4/31]─

  ❯ Policy Template Rules Basic / user can get specific policy rule by ID

   TypeError:
createResponse.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:202
   200|      // Create a policy rule
   201|      const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
 ❯ 202|      createResponse.assertStatus(201)
   203|      const ruleId = createResponse.body().data.id
   204|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[5/31]─

  ❯ Policy Template Rules Basic / user cannot access other users policy rules

   TypeError:
createResponse.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:228
   226|      const template = await createTestPrivacyTemplate(client, user1Token)
   227|      const { response: createResponse } = await createTestPolicyRule(client, user1Token, template.id)
 ❯ 228|      createResponse.assertStatus(201)
   229|      const ruleId = createResponse.body().data.id
   230|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[6/31]─

  ❯ Policy Template Rules Basic / user can list their policy rules

  Assertion Error: expected undefined to exist


   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:259
   257|      assert.equal(body.status, 'success')
   258|      assert.exists(body.data)
 ❯ 259|      assert.exists(body.data.data)
   260|      assert.exists(body.data.pagination)
   261|      assert.isArray(body.data.data)
[7/31]─

  ❯ Policy Template Rules Basic / user can update policy rule

   TypeError:
createResponse.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:273
   271|      // Create a policy rule
   272|      const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
 ❯ 273|      createResponse.assertStatus(201)
   274|      const ruleId = createResponse.body().data.id
   275|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[8/31]─

  ❯ Policy Template Rules Basic / user can delete policy rule

   TypeError:
createResponse.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:310
   308|      // Create a policy rule
   309|      const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
 ❯ 310|      createResponse.assertStatus(201)
   311|      const ruleId = createResponse.body().data.id
   312|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[9/31]─

  ❯ Policy Template Rules Basic / policy rules support priority-based precedence

   ReferenceError:
permissiveRule
is
not
defined
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:384
   382|  
   383|      // All should be created successfully
 ❯ 384|          permissiveRule.assertStatus(201)
   385|      mediumPriorityRule.response.assertStatus(201)
   386|      lowPriorityRule.response.assertStatus(201)

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[10/31]─

  ❯ Policy Template Rules Basic / policy rules with same priority are handled consistently

   TypeError:
rule1.response.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:437
   435|  
   436|      // Both should be created successfully
 ❯ 437|      rule1.response.assertStatus(201)
   438|      rule2.response.assertStatus(201)
   439|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[11/31]─

  ❯ Policy Template Rules Basic / policy rules with scope-specific target validation

   TypeError:
contactScopeRule.response.assertStatus
is
not
a
function
 
   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:473
   471|  
   472|      // Both should be created successfully (target validation may be handled at business logic level)
 ❯ 473|      contactScopeRule.response.assertStatus(201)
   474|      groupScopeRule.response.assertStatus(201)
   475|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[12/31]─

  ❯ Policy Template Rules Basic / policy rules conflict resolution through priority ordering

  Assertion Error: expected 422 to equal 201

- Expected  - 1
+ Received  + 1

- 201
+ 422

   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:504
   502|  
   503|      // Both should be created
 ❯ 504|          restrictiveRule.assertStatus(201)
   505|          permissiveRuleResponse.assertStatus(201)
   506|
[13/31]─

  ❯ Policy Template Rules Basic / policy rules support all valid enum values

  Assertion Error: expected 422 to equal 201

- Expected  - 1
+ Received  + 1

- 201
+ 422

   at Object.executor app/features/privacy-policy/tests/functional/policy-rules-basic.spec.ts:549
   547|        })
   548|  
 ❯ 549|        response.assertStatus(201)
   550|        assert.equal(response.body().data.viewerScopeType, scope)
   551|      }
[14/31]─

  ❯ Privacy Policy Template Basic / user can create privacy policy template

  Assertion Error: expected { canViewCalendar: false, …(2) } to deeply equal { canViewCalendar: true, …(2) }

- Expected  - 1
+ Received  + 1

  Object {
    "canScheduleMeeting": false,
-   "canViewCalendar": true,
+   "canViewCalendar": false,
    "showDetails": false,
  }

   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:98
    96|       assert.equal(body.data.name, templateData.name)
    97|       assert.equal(body.data.description, templateData.description)
 ❯  98|       assert.deepEqual(body.data.permissions, templateData.permissions)
    99|       assert.equal(body.data.isDefault, templateData.isDefault)
   100|      assert.exists(body.data.id)
[15/31]─

  ❯ Privacy Policy Template Basic / user can get specific privacy template by ID

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:192
   190|      // Create a privacy template first
   191|      const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
 ❯ 192|      createResponse.assertStatus(201)
   193|      const templateId = createResponse.body().data.id
   194|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[16/31]─

  ❯ Privacy Policy Template Basic / user cannot access other users privacy templates

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:216
   214|      // User 1 creates a privacy template
   215|      const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
 ❯ 216|      createResponse.assertStatus(201)
   217|      const templateId = createResponse.body().data.id
   218|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[17/31]─

  ❯ Privacy Policy Template Basic / user can list their privacy templates

  Assertion Error: expected undefined to exist


   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:244
   242|      assert.equal(body.status, 'success')
   243|      assert.exists(body.data)
 ❯ 244|      assert.exists(body.data.data)
   245|      assert.exists(body.data.pagination)
   246|      assert.isArray(body.data.data)
[18/31]─

  ❯ Privacy Policy Template Basic / privacy template listing supports pagination

   TypeError:
Cannot
read
properties
of
undefined
(reading
'length')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:267
   265|      const body = response.body()
   266|  
 ❯ 267|      assert.equal(body.data.data.length, 2)
   268|      assert.exists(body.data.pagination)
   269|      assert.equal(body.data.pagination.page, 1)

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[19/31]─

  ❯ Privacy Policy Template Basic / privacy template listing supports isDefault filtering

   TypeError:
Cannot
read
properties
of
undefined
(reading
'length')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:290
   288|      const body = response.body()
   289|  
 ❯ 290|      assert.isAtLeast(body.data.data.length, 1)
   291|      // All templates should be default
   292|      body.data.data.forEach((template: any) => {

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[20/31]─

  ❯ Privacy Policy Template Basic / privacy template listing supports search

   TypeError:
Cannot
read
properties
of
undefined
(reading
'length')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:319
   317|      const body = response.body()
   318|  
 ❯ 319|      assert.isAtLeast(body.data.data.length, 1)
   320|      // Should find the work template
   321|      const foundTemplate = body.data.data.find((template: any) => template.name.includes('Work'))

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[21/31]─

  ❯ Privacy Policy Template Basic / privacy template listing supports sorting

   TypeError:
Cannot
read
properties
of
undefined
(reading
'length')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:341
   339|      const body = response.body()
   340|  
 ❯ 341|      assert.isAtLeast(body.data.data.length, 2)
   342|      // First template should be Alpha (alphabetically first)
   343|      assert.equal(body.data.data[0].name, 'Alpha Template')

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[22/31]─

  ❯ Privacy Policy Template Basic / user can update privacy template

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:351
   349|      // Create a privacy template first
   350|      const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
 ❯ 351|      createResponse.assertStatus(201)
   352|      const templateId = createResponse.body().data.id
   353|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[23/31]─

  ❯ Privacy Policy Template Basic / user can partially update privacy template

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:386
   384|      // Create a privacy template first
   385|      const { response: createResponse, templateData } = await createTestPrivacyTemplate(client, authToken)
 ❯ 386|      createResponse.assertStatus(201)
   387|      const templateId = createResponse.body().data.id
   388|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[24/31]─

  ❯ Privacy Policy Template Basic / user cannot update other users privacy templates

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:418
   416|      // User 1 creates a privacy template
   417|      const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
 ❯ 418|      createResponse.assertStatus(201)
   419|      const templateId = createResponse.body().data.id
   420|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[25/31]─

  ❯ Privacy Policy Template Basic / user can delete privacy template

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:435
   433|      // Create a privacy template first
   434|      const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
 ❯ 435|      createResponse.assertStatus(201)
   436|      const templateId = createResponse.body().data.id
   437|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[26/31]─

  ❯ Privacy Policy Template Basic / user cannot delete other users privacy templates

   TypeError:
Cannot
read
properties
of
undefined
(reading
'assertStatus')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:463
   461|      // User 1 creates a privacy template
   462|      const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
 ❯ 463|      createResponse.assertStatus(201)
   464|      const templateId = createResponse.body().data.id
   465|  

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[27/31]─

  ❯ Privacy Policy Template Basic / privacy template with category relationship

   TypeError:
Cannot
read
properties
of
undefined
(reading
'length')
 
   at Object.executor app/features/privacy-policy/tests/functional/privacy-template-basic.spec.ts:524
   522|      const listBody = listResponse.body()
   523|  
 ❯ 524|      assert.isAtLeast(listBody.data.data.length, 1)
   525|      const foundTemplate = listBody.data.data.find((t: any) => t.id === body.data.id)
   526|      assert.exists(foundTemplate)

   ⁃ async TestRunner.#wrapTestInTimeout
     node_modules/@japa/core/src/test/runner.ts:382
   ⁃ async TestRunner.run
     node_modules/@japa/core/src/test/runner.ts:443
   ⁃ async Test.exec
     node_modules/@japa/core/src/test/main.ts:466
   ⁃ async GroupRunner.run
     node_modules/@japa/core/src/group/runner.ts:166
   ⁃ async Group.exec
     node_modules/@japa/core/src/group/main.ts:190
   ⁃ async SuiteRunner.run
     node_modules/@japa/core/src/suite/runner.ts:163
   ⁃ async Suite.exec
     node_modules/@japa/core/src/suite/main.ts:159
   ⁃ async Runner.exec
     node_modules/@japa/core/src/runner.ts:160
[28/31]─

  ❯ Profile Settings Basic / user cannot create duplicate profile settings for same profile

  Assertion Error: expected 201 to equal 422

- Expected  - 1
+ Received  + 1

- 422
+ 201

   at Object.executor app/features/profile/tests/functional/profile-settings-basic.spec.ts:223
   221|        })
   222|  
 ❯ 223|      response.assertStatus(422) // Should fail due to uniqueness constraint
   224|    }).timeout(10000)
   225|
[29/31]─

  ❯ Profile Settings Basic / profile settings enforce one-to-one relationship with profile

  Assertion Error: expected 201 to equal 422

- Expected  - 1
+ Received  + 1

- 422
+ 201

   at Object.executor app/features/profile/tests/functional/profile-settings-basic.spec.ts:488
   486|  
   487|      // Should fail due to one-to-one constraint
 ❯ 488|      response.assertStatus(422)
   489|    }).timeout(10000)
   490|
[30/31]─

  ❯ Profile Settings Basic / profile settings cannot reference other users privacy templates

  Assertion Error: expected 201 to be one of [ 400, 404, 422 ]

  Comparing two different types of values. Expected array but received number.

   at Object.executor app/features/profile/tests/functional/profile-settings-basic.spec.ts:561
   559|      // This should fail at the business logic level (template ownership validation)
   560|      // The exact response code may vary based on implementation
 ❯ 561|      assert.oneOf(response.response.status, [400, 404, 422])
   562|    }).timeout(10000)
   563|  })
[31/31]─

 FAILED 

Tests  88 passed, 31 failed (119)
 Time  10s
