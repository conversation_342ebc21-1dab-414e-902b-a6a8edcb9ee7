import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

/**
 * Database transaction helper for tests
 * Provides better isolation and error handling than global transactions
 */
export class DatabaseTransactionHelper {
  /**
   * Execute a test operation within its own transaction
   * This prevents transaction abortion cascades across tests
   */
  static async withTransaction<T>(
    operation: (trx: TransactionClientContract) => Promise<T>
  ): Promise<T> {
    const trx = await db.transaction()
    
    try {
      const result = await operation(trx)
      await trx.commit()
      return result
    } catch (error) {
      await trx.rollback()
      console.error('Test transaction failed:', {
        error: error.message,
        code: error.code,
        constraint: error.constraint,
      })
      throw error
    }
  }

  /**
   * Check if current transaction is still active
   */
  static async isTransactionActive(trx: TransactionClientContract): Promise<boolean> {
    try {
      const result = await trx.raw('SELECT txid_status(txid_current())')
      return result.rows[0].txid_status !== 'aborted'
    } catch {
      return false
    }
  }

  /**
   * Clean specific tables (alternative to transactions for simple cleanup)
   */
  static async cleanTables(tables: string[]) {
    for (const table of tables.reverse()) {
      try {
        await db.from(table).del()
      } catch (error) {
        console.warn(`Failed to clean table ${table}:`, error.message)
      }
    }
  }
}

/**
 * Simple test isolation using table cleanup
 * Use this for tests that don't need full transaction isolation
 */
export class TestDataCleaner {
  private static testData: Map<string, Set<string>> = new Map()

  static trackTable(testName: string, tableName: string) {
    if (!this.testData.has(testName)) {
      this.testData.set(testName, new Set())
    }
    this.testData.get(testName)!.add(tableName)
  }

  static async cleanupTest(testName: string) {
    const tables = this.testData.get(testName)
    if (tables) {
      await DatabaseTransactionHelper.cleanTables(Array.from(tables))
      this.testData.delete(testName)
    }
  }

  static async cleanupAll() {
    const allTables = new Set<string>()
    for (const tables of this.testData.values()) {
      tables.forEach(table => allTables.add(table))
    }
    await DatabaseTransactionHelper.cleanTables(Array.from(allTables))
    this.testData.clear()
  }
}