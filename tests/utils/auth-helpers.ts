import { ApiClient } from '@japa/api-client'
import AuthUser from '#models/auth_user_model'
import User from '#features/user/user_model'
import Profile from '#features/profile/profile_model'
import { TestStateManager, UserData } from './test-state-manager.js'
import GroupInvitation from '#features/group/invitations/group_invitation_model'
import UserContact from '#features/contact/user_contact_model'
import { ContactRequestStatus } from '#types/contact_types'
import UserGroup from '#features/group/groups/user_group_model'
import GroupMember from '#features/group/members/group_member_model'
import { buildGroup, GroupData } from './data-builders.js'

export async function createInvitation(groupId: string, inviterId: string, inviteeId: string) {
  return GroupInvitation.create({
    groupId,
    userId: inviteeId,
    invitedById: inviterId,
    isInvite: true,
  })
}

export async function createGroup(adminId: string, options: Partial<GroupData> = {}) {
  const groupData = buildGroup('Test Group', options)
  const group = await UserGroup.create({
    ...groupData,
    ownerUserId: adminId,
  })
  await GroupMember.create({
    groupId: group.id,
    userId: adminId,
    role: 'admin',
  })
  return group
}

export async function setupGroupAdminAndUser() {
  const admin = await createTestUser('admin', { createProfile: true })
  const user = await createTestUser('user', { createProfile: true })
  return { admin, user }
}

export async function createTestUsers(
  client: ApiClient,
  userKeys: string[],
  options: TestUserOptions = {}
): Promise<Map<string, { userData: UserData; authToken: string }>> {
  const users = new Map<string, { userData: UserData; authToken: string }>()

  for (const key of userKeys) {
    const result = await registerAndLoginUser(client, key, {
      ...options,
      username: `${options.username || 'testuser'}_${key}`,
      email: `${options.email || `test_${key}`}@example.com`,
    })
    users.set(key, result)
  }

  return users
}

export interface TestUserOptions {
  username?: string
  email?: string
  password?: string
  createProfile?: boolean
  profileData?: {
    firstName?: string
    lastName?: string
    birthDate?: string
    countryCode?: string
    bio?: string
  }
}

export async function createTestUser(
  key: string,
  options: TestUserOptions = {}
): Promise<UserData> {
  // Check if user already exists in state
  const existingUser = TestStateManager.getUser(key)
  if (existingUser) {
    return existingUser
  }

  // Generate unique credentials
  const uniqueId = Date.now() + Math.random().toString(36).substring(2, 15)
  const username = options.username || `testuser_${key}_${uniqueId}`
  const email = options.email || `test_${key}_${uniqueId}@example.com`
  const password = options.password || 'TestPassword123!'

  // Create AuthUser
  const authUser = await AuthUser.create({
    username,
    email,
    passwordHash: password,
  })

  // Create User profile
  const user = await User.create({
    authUserId: authUser.id,
  })

  // Create Profile if requested
  if (options.createProfile) {
    const profileData = {
      userId: user.id,
      firstName: options.profileData?.firstName || 'Test',
      lastName: options.profileData?.lastName || 'User',
      birthDate: options.profileData?.birthDate || '1990-01-01',
      countryCode: options.profileData?.countryCode || 'PHL',
      bio: options.profileData?.bio,
    }

    await Profile.create(profileData)
  }

  const userData: UserData = {
    id: user.id,
    authUserId: authUser.id,
    username,
    email,
    password,
  }

  TestStateManager.setUser(key, userData)
  return userData
}

export async function loginUser(
  client: ApiClient,
  userKey: string
): Promise<string> {
  // Check if token already exists
  const existingToken = TestStateManager.getAuthToken(userKey)
  if (existingToken) {
    return existingToken
  }

  const user = TestStateManager.getUserOrThrow(userKey)

  const loginResponse = await client.post('/api/v1/auth/login').form({
    email: user.email,
    password: user.password,
  })

  if (loginResponse.response.status !== 200) {
    throw new Error(
      `Login failed for user '${userKey}' with status ${loginResponse.response.status}`
    )
  }

  const responseBody = loginResponse.body()
  if (!responseBody.token || !responseBody.token.token) {
    throw new Error(
      `Login response for user '${userKey}' did not contain a valid token: ${JSON.stringify(responseBody)}`
    )
  }

  const token = responseBody.token.token
  TestStateManager.setAuthToken(userKey, token)
  return token
}

export async function registerAndLoginUser(
  client: ApiClient,
  userKey: string,
  options: TestUserOptions = {}
): Promise<{ userData: UserData; authToken: string }> {
  // Generate unique credentials
  const uniqueId = Date.now() + Math.random().toString(36).substring(2, 15)
  const username = options.username || `testuser_${key}_${uniqueId}`
  const email = options.email || `test_${key}_${uniqueId}@example.com`
  const password = options.password || 'TestPassword123!'

  // Register user via API
  const registerResponse = await client.post('/api/v1/auth/register').form({
    username,
    email,
    password,
  })

  if (registerResponse.response.status !== 201) {
    throw new Error(
      `Registration failed for user '${userKey}' with status ${registerResponse.response.status}: ${JSON.stringify(registerResponse.body())}`
    )
  }

  const registerBody = registerResponse.body()
  const authUserId = registerBody.data.user.id

  // Find the created User profile
  const user = await User.findByOrFail('authUserId', authUserId)

  const userData: UserData = {
    id: user.id,
    authUserId,
    username,
    email,
    password,
  }

  TestStateManager.setUser(userKey, userData)

  // Now login to get token
  const authToken = await loginUser(client, userKey)

  return { userData, authToken }
}

export async function ensureTestUser(
  client: ApiClient,
  userKey: string,
  options: TestUserOptions = {}
): Promise<{ userData: UserData; authToken: string }> {
  let userData = TestStateManager.getUser(userKey)
  
  if (!userData) {
    // Create user if doesn't exist
    userData = await createTestUser(userKey, options)
  }

  // Ensure we have auth token
  let authToken = TestStateManager.getAuthToken(userKey)
  if (!authToken) {
    authToken = await loginUser(client, userKey)
  }

  return { userData, authToken }
}

export async function blockUser(blockerId: string, blockedId: string) {
  return UserContact.create({
    requesterUserId: blockerId,
    addresseeUserId: blockedId,
    status: ContactRequestStatus.BLOCKED_BY_REQUESTER,
  })
}

export async function createJoinRequest(groupId: string, requesterId: string) {
  return GroupInvitation.create({
    groupId,
    userId: requesterId,
    isInvite: false,
  })
}