/**
 * Sample AI prompts for testing task creation
 * These prompts are designed to generate predictable task structures
 */

export interface PromptSample {
  prompt: string
  expectedTaskData: {
    titleContains: string[]
    descriptionContains?: string[]
    priority?: 'low' | 'medium' | 'high'
    estimatedDuration?: string
  }
}

export const AI_PROMPT_SAMPLES: Record<string, PromptSample> = {
  simpleTask: {
    prompt: 'Create a task to review quarterly reports by Friday',
    expectedTaskData: {
      titleContains: ['review', 'quarterly', 'reports'],
      descriptionContains: ['quarterly', 'reports', 'friday'],
      priority: 'medium',
    }
  },

  urgentTask: {
    prompt: 'URGENT: Fix critical bug in authentication system before production deploy',
    expectedTaskData: {
      titleContains: ['fix', 'bug', 'authentication'],
      descriptionContains: ['critical', 'authentication', 'production'],
      priority: 'high',
    }
  },

  meetingTask: {
    prompt: 'Schedule a team meeting next week to discuss project roadmap',
    expectedTaskData: {
      titleContains: ['schedule', 'team', 'meeting'],
      descriptionContains: ['team', 'meeting', 'roadmap'],
      priority: 'medium',
    }
  },

  researchTask: {
    prompt: 'Research new React libraries for the frontend modernization project',
    expectedTaskData: {
      titleContains: ['research', 'react', 'libraries'],
      descriptionContains: ['react', 'libraries', 'frontend', 'modernization'],
      priority: 'low',
    }
  },

  planningTask: {
    prompt: 'Plan the architecture for the new microservices deployment',
    expectedTaskData: {
      titleContains: ['plan', 'architecture', 'microservices'],
      descriptionContains: ['architecture', 'microservices', 'deployment'],
      priority: 'high',
    }
  },

  documentationTask: {
    prompt: 'Update API documentation for the new user management endpoints',
    expectedTaskData: {
      titleContains: ['update', 'api', 'documentation'],
      descriptionContains: ['api', 'documentation', 'user', 'management'],
      priority: 'low',
    }
  },

  testingTask: {
    prompt: 'Write unit tests for the contact management service',
    expectedTaskData: {
      titleContains: ['write', 'unit', 'tests'],
      descriptionContains: ['unit', 'tests', 'contact', 'management'],
      priority: 'medium',
    }
  },

  deploymentTask: {
    prompt: 'Deploy the staging environment with latest changes for QA testing',
    expectedTaskData: {
      titleContains: ['deploy', 'staging', 'environment'],
      descriptionContains: ['staging', 'environment', 'qa', 'testing'],
      priority: 'high',
    }
  },

  analysisTask: {
    prompt: 'Analyze user feedback from the last sprint and prepare improvement recommendations',
    expectedTaskData: {
      titleContains: ['analyze', 'user', 'feedback'],
      descriptionContains: ['user', 'feedback', 'sprint', 'improvement'],
      priority: 'medium',
    }
  },

  optimizationTask: {
    prompt: 'Optimize database queries to improve application performance',
    expectedTaskData: {
      titleContains: ['optimize', 'database', 'queries'],
      descriptionContains: ['database', 'queries', 'performance'],
      priority: 'medium',
    }
  }
}

/**
 * Get a random prompt sample for testing
 */
export function getRandomPromptSample(): PromptSample {
  const keys = Object.keys(AI_PROMPT_SAMPLES)
  const randomKey = keys[Math.floor(Math.random() * keys.length)]
  return AI_PROMPT_SAMPLES[randomKey]
}

/**
 * Get a specific prompt sample by key
 */
export function getPromptSample(key: string): PromptSample {
  const sample = AI_PROMPT_SAMPLES[key]
  if (!sample) {
    throw new Error(`Prompt sample with key '${key}' not found`)
  }
  return sample
}

/**
 * Get all prompt sample keys
 */
export function getPromptSampleKeys(): string[] {
  return Object.keys(AI_PROMPT_SAMPLES)
}

/**
 * Validate that a generated task matches the expected criteria
 */
export function validateTaskAgainstSample(
  task: any,
  sample: PromptSample
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check title contains expected keywords
  const titleLower = task.title?.toLowerCase() || ''
  for (const keyword of sample.expectedTaskData.titleContains) {
    if (!titleLower.includes(keyword.toLowerCase())) {
      errors.push(`Title should contain keyword: ${keyword}`)
    }
  }

  // Check description contains expected keywords (if specified)
  if (sample.expectedTaskData.descriptionContains) {
    const descriptionLower = task.description?.toLowerCase() || ''
    for (const keyword of sample.expectedTaskData.descriptionContains) {
      if (!descriptionLower.includes(keyword.toLowerCase())) {
        errors.push(`Description should contain keyword: ${keyword}`)
      }
    }
  }

  // Check priority (if specified)
  if (sample.expectedTaskData.priority) {
    if (task.priority !== sample.expectedTaskData.priority) {
      errors.push(
        `Priority should be '${sample.expectedTaskData.priority}' but was '${task.priority}'`
      )
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}