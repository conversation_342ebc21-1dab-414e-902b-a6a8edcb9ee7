import { ApiClient } from '@japa/api-client'

export interface UserData {
  id: string
  authUserId: string
  username: string
  email: string
  password: string
  authToken?: string
}

export interface TaskData {
  id: string
  activityId: string
  title: string
  description?: string
  status: string
  priority: string
  userId: string
}

export interface ContactData {
  id: string
  requesterUserId: string
  addresseeUserId: string
  status: string
}

export interface GroupData {
  id: string
  name: string
  description?: string
  hostUserId: string
  memberCount: number
}

/**
 * Manages shared test state across different test suites
 * Enables data flow between feature tests while maintaining isolation
 */
export class TestStateManager {
  private static users: Map<string, UserData> = new Map()
  private static tasks: Map<string, TaskData> = new Map()
  private static contacts: Map<string, ContactData> = new Map()
  private static groups: Map<string, GroupData> = new Map()
  private static authTokens: Map<string, string> = new Map()

  // User management
  static setUser(key: string, userData: UserData): void {
    this.users.set(key, userData)
    if (userData.authToken) {
      this.authTokens.set(key, userData.authToken)
    }
  }

  static getUser(key: string): UserData | undefined {
    return this.users.get(key)
  }

  static getUserOrThrow(key: string): UserData {
    const user = this.users.get(key)
    if (!user) {
      throw new Error(`User with key '${key}' not found in test state`)
    }
    return user
  }

  static getAllUsers(): UserData[] {
    return Array.from(this.users.values())
  }

  // Auth token management
  static setAuthToken(userKey: string, token: string): void {
    this.authTokens.set(userKey, token)
    const user = this.users.get(userKey)
    if (user) {
      user.authToken = token
      this.users.set(userKey, user)
    }
  }

  static getAuthToken(userKey: string): string | undefined {
    return this.authTokens.get(userKey)
  }

  static getAuthTokenOrThrow(userKey: string): string {
    const token = this.authTokens.get(userKey)
    if (!token) {
      throw new Error(`Auth token for user '${userKey}' not found in test state`)
    }
    return token
  }

  // Task management
  static setTask(key: string, taskData: TaskData): void {
    this.tasks.set(key, taskData)
  }

  static getTask(key: string): TaskData | undefined {
    return this.tasks.get(key)
  }

  static getTaskOrThrow(key: string): TaskData {
    const task = this.tasks.get(key)
    if (!task) {
      throw new Error(`Task with key '${key}' not found in test state`)
    }
    return task
  }

  static getAllTasks(): TaskData[] {
    return Array.from(this.tasks.values())
  }

  // Contact management
  static setContact(key: string, contactData: ContactData): void {
    this.contacts.set(key, contactData)
  }

  static getContact(key: string): ContactData | undefined {
    return this.contacts.get(key)
  }

  static getContactOrThrow(key: string): ContactData {
    const contact = this.contacts.get(key)
    if (!contact) {
      throw new Error(`Contact with key '${key}' not found in test state`)
    }
    return contact
  }

  static getAllContacts(): ContactData[] {
    return Array.from(this.contacts.values())
  }

  // Group management
  static setGroup(key: string, groupData: GroupData): void {
    this.groups.set(key, groupData)
  }

  static getGroup(key: string): GroupData | undefined {
    return this.groups.get(key)
  }

  static getGroupOrThrow(key: string): GroupData {
    const group = this.groups.get(key)
    if (!group) {
      throw new Error(`Group with key '${key}' not found in test state`)
    }
    return group
  }

  static getAllGroups(): GroupData[] {
    return Array.from(this.groups.values())
  }

  // Utility methods
  static clear(): void {
    this.users.clear()
    this.tasks.clear()
    this.contacts.clear()
    this.groups.clear()
    this.authTokens.clear()
  }

  static getUserCount(): number {
    return this.users.size
  }

  static getTaskCount(): number {
    return this.tasks.size
  }

  static getContactCount(): number {
    return this.contacts.size
  }

  static getGroupCount(): number {
    return this.groups.size
  }

  // Debug methods
  static debugPrint(): void {
    console.log('=== Test State Manager Debug ===')
    console.log(`Users: ${this.users.size}`)
    console.log(`Tasks: ${this.tasks.size}`)
    console.log(`Contacts: ${this.contacts.size}`)
    console.log(`Groups: ${this.groups.size}`)
    console.log(`Auth Tokens: ${this.authTokens.size}`)
    console.log('================================')
  }
}