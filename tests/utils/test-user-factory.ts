import AuthUser from '#models/auth_user_model'
import User from '#features/user/user_model'
import hash from '@adonisjs/core/services/hash'

export interface TestUser {
  authUser: AuthUser
  user: User
  userId: string
  email: string
  password: string
  token?: string
}

/**
 * Create a test user directly in the database (participates in transactions)
 */
export async function createTestUser(prefix: string = 'testuser'): Promise<TestUser> {
  // Enhanced uniqueness generation to prevent collisions
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const nanotime = process.hrtime.bigint().toString()
  const uniqueId = `${timestamp}_${random}_${nanotime.slice(-6)}`
  
  const userData = {
    username: `${prefix}_${uniqueId}`,
    email: `${prefix}_${uniqueId}@example.com`,
    password: 'TestPassword123!',
  }

  try {
    // Create auth user
    const authUser = await AuthUser.create({
      username: userData.username,
      email: userData.email,
      passwordHash: await hash.make(userData.password),
    })

    // Create user
    const user = await User.create({
      authUserId: authUser.id,
    })

    return {
      authUser,
      user,
      userId: user.id,
      email: userData.email,
      password: userData.password,
    }
  } catch (error) {
    console.error('Error creating test user:', {
      error: error.message,
      code: error.code,
      constraint: error.constraint,
      detail: error.detail,
      userData: { username: userData.username, email: userData.email }
    })
    throw error
  }
}

/**
 * Create a test user and generate an auth token
 */
export async function createTestUserWithToken(prefix: string = 'testuser'): Promise<TestUser & { token: string }> {
  const testUser = await createTestUser(prefix)
  
  // Generate token (simplified - in real app this would go through the auth service)
  const token = await AuthUser.accessTokens.create(testUser.authUser, ['*'], {
    expiresIn: '1 hour'
  })

  return {
    ...testUser,
    token: token.value!.release(),
  }
}