---
description: 
globs: 
alwaysApply: true
---
# Clean AdonisJS APIs with TypeScript Cursor Rules

You are a senior TypeScript programmer with experience in the AdonisJS framework, focusing on clean programming and design patterns.

Generate code, corrections, and refactorings that comply with the principles below.

## Core Principles

### TypeScript Best Practices
- Use explicit types for variables, parameters, and return values.
- Avoid `any` type; prefer specific types or `unknown`.
- Follow naming conventions: PascalCase for classes, camelCase for variables/functions.
- Use interfaces for object shapes and enums for fixed values.
- Write short, focused functions with early returns.

**Detailed guidelines**: See `.cursor/rules/typescript-guidelines.md`.

### AdonisJS Architecture
- Follow standard AdonisJS directory structure.
- Keep controllers lightweight, focused on HTTP concerns.
- Implement business logic in service classes.
- Use dependency injection for testability.
- Define models with proper TypeScript decorators and typings.
- For new routes, put the endpoint logic on their own controller file, create one if not yet. Do not populate `routes.ts` with endpoint logic codes.

**Detailed guidelines**: See `.cursor/rules/adonisjs-architecture.mdc`.

### AdonisJS Operations
- Group related routes; apply middleware at group level.
- Use VineJS for request validation.
- Create custom exceptions for domain-specific errors.
- Implement transactions for atomic database operations.
- Apply authentication and security measures.
- Optimize with eager loading and pagination.

**Detailed guidelines**: See `.cursor/rules/adonisjs-operations.md`.

### Database and ORM
- Follow best practices for PostgreSQL schema design and AdonisJS Lucid ORM.
- Use soft deletes and UUID primary keys where applicable. This means every user data should have `deleted_at` column which will be used to determine if it's archived or not.

**Detailed guidelines**: See `.cursor/rules/database-orm-guidelines.mdc`.

### Testing and Debugging
- Create tests for all new features (unit, functional, integration).
- Use Japa testing framework in `tests/` directory.
- Follow Test-Driven Development (TDD) when possible.
- Ensure all tests pass before considering a feature complete.
- Update tests when modifying features.
- During debugging, focus on identifying and fixing the root cause of issues, not just the symptoms.
- Create and run test cases for every new or updated feature; continue debugging until all tests pass to confirm the root cause is resolved.
- **Ensure tests call the actual function being tested**; avoid mocking the target function or its immediate dependencies solely to pass tests without verifying the true logic.
- When debugging something, fix the function or the test case (if the function has changed but test case is outdated) and run the test command without watch flag and fix until tests works

## Essential Project Documentation Workflow
- **Before Starting a Task**: **Always consult** `docs/deliverables.md` for milestone status and `docs/strategy_plan.md` for project approach.
- **For Milestone Work**: **Must read** the corresponding guide in `docs/milestone_guides/` for detailed instructions **before implementation**.
- **During Development**: Refer to `docs/findings.md` for contextual insights related to the milestone.
- **After Completing a Deliverable**: **Update** `docs/deliverables.md` by checking off completed items to reflect progress.

## Key Reminder
- **Always follow** the above workflow for documentation to ensure alignment with project goals and milestones.
- **Prioritize** consulting specialized guideline files for detailed rules on specific topics.
