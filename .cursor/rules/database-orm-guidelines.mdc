---
description: for detailed best practices on PostgreSQL schema design and AdonisJS Lucid ORM usage
globs: 
alwaysApply: false
---
# Database and ORM Guidelines for Skedai Backend

## Overview

This document outlines best practices for database design and operations using PostgreSQL, as well as guidelines for working with AdonisJS's Lucid ORM. These rules ensure consistency, performance, and maintainability in the Skedai backend database architecture.

## PostgreSQL Best Practices

### Schema Design
- **Normalization**: Design schemas to minimize data redundancy while balancing performance needs. Use appropriate normalization levels (typically up to 3NF) unless denormalization is justified for performance.
- **Naming Conventions**: Use snake_case for table and column names to maintain readability and consistency with PostgreSQL conventions.
- **Data Types**: Choose the most specific data type for each column (e.g., use `TIMESTAMP` for dates, `NUMERIC` for monetary values, `TEXT` for variable-length strings).
- **Constraints**: Enforce data integrity with primary keys, foreign keys, unique constraints, and check constraints where applicable.
- **Indexes**: Create indexes on frequently queried columns (e.g., foreign keys, search fields) to optimize performance, but avoid over-indexing to prevent write operation slowdowns.
- **Partitioning**: For large tables, consider partitioning to improve query performance and manageability (e.g., range partitioning for time-series data).

### Performance Optimization
- **Query Optimization**: Write efficient queries by avoiding SELECT *, using JOINs appropriately, and leveraging EXPLAIN to analyze query plans.
- **Caching**: Use database-level caching or materialized views for frequently accessed, static data.
- **Connection Pooling**: Configure connection pooling (via PgBouncer or similar) to manage database connections efficiently.
- **Vacuuming and Analyzing**: Regularly run `VACUUM` and `ANALYZE` to maintain database health and update statistics for the query planner.

### Security
- **Roles and Permissions**: Use PostgreSQL roles to manage access control, granting minimal necessary permissions to users and applications.
- **Encryption**: Encrypt sensitive data at rest using PostgreSQL's built-in encryption features or external tools, and ensure connections use SSL/TLS.
- **Parameterized Queries**: Always use parameterized queries to prevent SQL injection attacks.

### Things to Avoid and Gotchas
- **Avoid Over-Normalization**: Excessive normalization can lead to complex queries and performance issues. Balance normalization with practical query needs.
- **Avoid Unnecessary Indexes**: Too many indexes can slow down INSERT, UPDATE, and DELETE operations. Only index columns that are frequently queried or used in JOINs.
- **Gotcha - Case Sensitivity**: PostgreSQL is case-sensitive for identifiers unless quoted. Always use lowercase with snake_case to avoid confusion or errors when querying.
- **Gotcha - Transaction Pitfalls**: Be cautious with long-running transactions as they can hold locks and cause deadlocks. Keep transactions short and commit or rollback promptly.
- **Avoid Raw SQL Without Sanitization**: Even with trusted inputs, raw SQL can introduce vulnerabilities or maintenance challenges. Use parameterized queries or ORM methods instead.
- **Gotcha - Time Zone Handling**: PostgreSQL stores timestamps in UTC by default. Ensure application logic handles time zones correctly to avoid display or calculation errors.
- **Avoid Large Data Dumps in Logs**: Logging large query results or database dumps can expose sensitive data and clutter logs. Filter or mask sensitive information.

## AdonisJS Lucid ORM Best Practices

### Model Definition
- **Type Safety**: Define model properties with explicit TypeScript types using decorators like `@column` to ensure type safety and IDE support.
- **Relationships**: Use Lucid decorators (`@hasOne`, `@hasMany`, `@manyToMany`, etc.) to define relationships clearly, ensuring proper foreign key mappings.
- **Hooks**: Implement model hooks (`@beforeSave`, `@afterCreate`, etc.) for common operations like hashing passwords or logging changes.
- **Scopes**: Create query scopes for reusable query logic (e.g., filtering active users) to keep controllers and services clean.

### Database Operations
- **Transactions**: Use Lucid's transaction support for atomic operations, especially for operations involving multiple models.
- **Eager Loading**: Leverage eager loading (`preload`) to prevent N+1 query issues when accessing related data.
- **Query Builder**: Use Lucid's query builder for complex queries, ensuring readability and maintainability over raw SQL where possible.
- **Migrations**: Write clear, reversible migrations for schema changes, using AdonisJS CLI to generate and manage migration files.
- **Seeders**: Create seeders for initial or test data, ensuring they are environment-specific and idempotent.

### Performance
- **Pagination**: Implement pagination for large datasets using Lucid's built-in pagination methods to limit data retrieval.
- **Caching**: Integrate caching strategies (e.g., Redis) with Lucid queries for frequently accessed data to reduce database load.

### Specific Lucid ORM Rules
- **Use Soft Deletes**: Configure models to use soft deletes by including a `deletedAt` column and using the `@column.dateTime({ autoCreate: false, autoUpdate: false }) deletedAt` decorator. Utilize built-in methods like `withTrashed()` or `onlyTrashed()` for querying.
- **Consistent Naming**: Ensure model and column names in Lucid match the database schema (e.g., snake_case in database, camelCase in TypeScript with proper mapping via `@column`).
- **Define Computed Properties**: Use `@computed` decorator for derived data that should not be stored in the database but is frequently accessed (e.g., full name from first and last name).
- **Leverage Model Traits**: Use traits for reusable model behaviors (e.g., soft delete trait) to keep code DRY and maintainable.

### Things to Avoid with Lucid ORM
- **Avoid Raw SQL Unless Necessary**: Raw SQL bypasses Lucid's type safety and can lead to maintenance issues. Use only for complex queries that cannot be handled by the query builder.
- **Avoid Overloading Models**: Keep models focused on data structure and relationships. Move complex business logic to services rather than embedding it in model methods or hooks.
- **Avoid Ignoring Relationships**: Failing to define relationships properly can lead to inefficient queries or data inconsistency. Always map relationships explicitly with decorators.
- **Avoid Unmanaged Migrations**: Never manually edit the database schema outside of migration files, as this can desynchronize Lucid models and database structure.

### Gotchas with Lucid ORM
- **Gotcha - N+1 Query Problem**: Forgetting to use `preload` for related data can result in multiple database queries. Always eager load relationships when accessing related models in a loop.
- **Gotcha - Transaction Scope**: Ensure transactions are properly scoped and closed. Uncommitted transactions can lock resources and cause performance issues.
- **Gotcha - Soft Delete Misuse**: Be cautious when querying with soft deletes; forgetting to use `withTrashed()` can result in missing data that appears deleted but is still in the database.
- **Gotcha - Model Serialization**: When serializing models to JSON, be aware that computed properties and relationships might not be included by default. Use `serialize()` or custom serialization logic to include necessary data.
- **Gotcha - Migration Order**: Migrations are run in order of creation. Ensure foreign key constraints are added after related tables are created to avoid errors.

## Specific Rules for Skedai Backend

- **Soft Deletes**: Implement soft delete functionality wherever possible to protect user data. Use a `deleted_at` timestamp column to mark records as deleted without physically removing them from the database. Ensure Lucid models are configured with `@column.dateTime({ autoCreate: false, autoUpdate: false }) deletedAt` and use the `withTrashed()` or `onlyTrashed()` scopes for querying.
- **UUID Primary Keys**: Use UUIDs as primary keys or unique identifiers for all tables instead of auto-incrementing integers to enhance security and uniqueness across distributed systems. Configure Lucid models to use UUIDs by setting `@column({ isPrimary: true }) public id: string` and ensure migrations define the column as `uuid` with a default value of `gen_random_uuid()`.

## Documentation and Testing

- **Document Schema Changes**: Update `docs/findings.md` with details of any database schema changes, including rationale and migration snippets.
- **Test Database Operations**: Write integration tests for database operations using Japa, ensuring coverage for CRUD operations, relationships, and edge cases. Mock database interactions in unit tests to avoid actual database calls.
- **Consistency Checks**: Regularly review database schema and Lucid models for consistency with project requirements and these guidelines.

## Conclusion

Adhering to these PostgreSQL and Lucid ORM best practices ensures a robust, secure, and performant database layer for the Skedai backend. Always refer to this guide when making changes to database schemas or ORM configurations to maintain alignment with project standards.
