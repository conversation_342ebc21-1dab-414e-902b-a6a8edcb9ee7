---
description: when agent is doing changes in folder structure like adding/update files
globs: 
alwaysApply: false
---
# AdonisJS Architecture Guidelines

## Directory Structure

- Maintain standard AdonisJS directory structure
- Use domain-driven structure for larger applications
- Place all related files in their designated directories:
  - Controllers → app/controllers
  - Models → app/models
  - Services → app/services
  - Validators → app/validators
  - Routes → start/routes.ts
  - Config → config/
  - Types → types/

For larger applications, consider organizing by domain rather than technical role:
```
app/
  └── Modules/
      └── User/
          ├── Controllers/
          ├── Models/
          ├── Services/
          ├── Validators/
      └── Task/
          ├── Controllers/
          ├── Models/
          ├── Services/
          ├── Validators/
```

## Controllers

- Keep controllers lightweight and focused on HTTP concerns
- Use dependency injection for service classes instead of instantiating them directly
- Follow single responsibility principle - one method, one task
- Implement early returns to avoid nested if/else blocks
- Return appropriate HTTP status codes
- Use resource controllers for RESTful endpoints
- Utilize route model binding when possible
- **Always create dedicated controllers for endpoint logic rather than embedding logic directly in `routes.ts`** to maintain separation of concerns and improve maintainability.

```typescript
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import TaskService from '#services/task_service'

@inject()
export default class TasksController {
  constructor(private taskService: TaskService) {}

  async index({ response }: HttpContext) {
    const tasks = await this.taskService.getAllTasks()
    return tasks
  }

  async store({ request, response }: HttpContext) {
    const data = request.only(['title', 'description', 'due_date'])
    
    // Return early pattern
    if (!data.title) {
      return response.badRequest({ error: 'Title is required' })
    }
    
    const task = await this.taskService.createTask(data)
    return response.created(task)
  }
}
```

## Services

- Create service classes for business logic
- Use dependency injection for repository/service dependencies
- Add proper TypeScript interfaces for method parameters and returns
- Keep services domain-focused (UserService, TaskService, etc.)
- Return data structures instead of directly calling response functions
- Abstract database operations from controllers

```typescript
import { inject } from '@adonisjs/core'
import Task from '#models/task'

@inject()
export default class TaskService {
  async getAllTasks() {
    return Task.all()
  }

  async createTask(data: TaskCreationData): Promise<Task> {
    return Task.create(data)
  }
  
  async updateTask(id: number, data: Partial<TaskUpdateData>): Promise<Task> {
    const task = await Task.findOrFail(id)
    task.merge(data)
    await task.save()
    return task
  }
}
```

## Models

- Use TypeScript decorators to define model properties and relationships
- Add proper TypeScript typing to all model properties with `declare` keyword
- Implement hooks for common operations like hashing passwords
- Create query scopes for commonly used queries
- Define relationships using decorators
- Use computed properties for derived data

```typescript
import { BaseModel, column, beforeSave, scope } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class Task extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string | null

  @column()
  declare status: 'pending' | 'in_progress' | 'completed'

  @column.dateTime()
  declare dueDate: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @beforeSave()
  static setDefaultStatus(task: Task) {
    if (!task.status) {
      task.status = 'pending'
    }
  }

  @scope('pending')
  static pending(query) {
    return query.where('status', 'pending')
  }
}
```

## Middleware

- Keep middleware single-purpose and focused
- Use middleware for cross-cutting concerns like authentication, logging
- Return early in middleware when conditions are not met
- Chain middleware for complex processing pipelines
- Create middleware using the Ace CLI: `node ace make:middleware`

```typescript
import { HttpContext } from '@adonisjs/core/http'
import { NextFn } from '@adonisjs/core/types'

export default class AuthMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    // Early return if not authenticated
    if (!ctx.auth.isAuthenticated) {
      return ctx.response.unauthorized({ error: 'Authentication required' })
    }
    
    await next()
  }
}
``` 