# Dependencies and AdonisJS build
node_modules
build
tmp

# Secrets
.env
.env.local
.env.production.local
.env.development.local

# Frontend assets compiled code
public/assets

# Build tools specific
npm-debug.log
yarn-error.log

# Editors specific
.fleet
.idea
.vscode

# Platform specific
.DS_Store

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 

requirements/