{"name": "sked<PERSON>-ad<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node -r dotenv/config build/bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js", "#utils/*": "./app/utils/*.js", "#features/*": "./app/features/*.js", "#types/*": "./app/types/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0-beta.6", "@adonisjs/prettier-config": "^1.4.0", "@adonisjs/tsconfig": "^1.4.0", "@japa/api-client": "^3.0.4", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^3.0.1", "@japa/runner": "^3.1.4", "@swc/core": "1.7.26", "@types/luxon": "^3.4.2", "@types/node": "^22.7.5", "eslint": "^9.12.0", "hot-hook": "^0.3.1", "pino-pretty": "^11.2.2", "prettier": "^3.3.3", "ts-node-maintained": "^10.9.4", "typescript": "~5.6"}, "dependencies": {"@adonisjs/auth": "^9.4.0", "@adonisjs/core": "^6.14.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/limiter": "^2.4.0", "@adonisjs/lucid": "^21.3.0", "@adonisjs/redis": "^9.2.0", "@ai-sdk/google": "^1.2.11", "@ai-sdk/openai": "^1.0.8", "@langchain/openai": "^0.5.6", "@vinejs/vine": "^2.1.0", "adonis-lucid-soft-deletes": "^2.1.0", "ai": "^4.0.18", "dotenv": "^16.4.7", "langchain": "^0.3.23", "luxon": "^3.5.0", "pg": "^8.13.1", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "task-master-ai": "^0.12.1", "zod": "^3.24.1"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "overrides": {"strtok3": "8.0.1"}, "resolutions": {"strtok3": "8.0.1"}, "pnpm": {"overrides": {"strtok3": "8.0.1"}}, "prettier": "@adonisjs/prettier-config"}