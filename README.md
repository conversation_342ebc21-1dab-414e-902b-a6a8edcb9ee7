# Skedai Backend

Backend API for the Skedai application, built with AdonisJS.

## Table of Contents

- [Setup](#setup)
- [Environment Variables](#environment-variables)
- [Running the Application](#running-the-application)
- [Authentication](#authentication)
- [API Documentation](#api-documentation)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)

## Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/skedai-adonisjs.git
cd skedai-adonisjs

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env file with your local settings

# Run database migrations
node ace migration:run

# Optional: Seed the database
node ace db:seed
```

## Environment Variables

Key environment variables:

- `PORT` - The port on which the server runs
- `HOST` - The host address
- `NODE_ENV` - Environment (development, testing, production)
- `APP_KEY` - Application encryption key
- `DB_CONNECTION` - Database connection type
- `POSTGRES_HOST` - PostgreSQL host
- `POSTGRES_PORT` - PostgreSQL port
- `POSTGRES_USER` - PostgreSQL username
- `POSTGRES_PASSWORD` - PostgreSQL password
- `POSTGRES_DB_NAME` - PostgreSQL database name

## Running the Application

```bash
# Development mode
npm run dev

# Production mode
npm run build
npm start
```

## Authentication

The Skedai backend implements a comprehensive JWT authentication system with refresh tokens. 

### Authentication Endpoints

| Endpoint | Method | Description | Authentication |
|----------|--------|-------------|----------------|
| `/api/v1/auth/register` | POST | Register a new user | No |
| `/api/v1/auth/login` | POST | Login and get tokens | No |
| `/api/v1/auth/refresh` | POST | Refresh the access token | No |
| `/api/v1/auth/logout` | POST | Logout and invalidate tokens | Yes |

### Making Authenticated Requests

1. Obtain an access token through login:
   ```bash
   curl -X POST http://localhost:3333/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "yourpassword"}'
   ```

2. Use the token in subsequent requests:
   ```bash
   curl -X GET http://localhost:3333/api/v1/profile \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
   ```

3. Refresh an expired token:
   ```bash
   curl -X POST http://localhost:3333/api/v1/auth/refresh \
     -H "Content-Type: application/json" \
     -d '{"refreshToken": "YOUR_REFRESH_TOKEN"}'
   ```

### Token Expiration

- Access tokens expire after 15 minutes
- Refresh tokens expire after 7 days

For detailed documentation on the authentication system, see [Authentication Documentation](docs/authentication.md).

## API Documentation

API documentation is available in OpenAPI (Swagger) format:

- [OpenAPI Specification](docs/openapi.yaml)

You can view and interact with the API documentation:

1. Visit [Swagger Editor](https://editor.swagger.io/)
2. Import the OpenAPI specification
3. Explore and test the API endpoints

## Development

```bash
# Run in development mode with hot reloading
npm run dev

# Lint the code
npm run lint

# Format the code
npm run format
```

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:cov
```

## Deployment

The application is configured for Docker deployment:

```bash
# Build and run with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f api
```

For more information about deployment, see the deployment documentation. 