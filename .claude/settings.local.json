{"permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "Bash(ls:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm test)", "<PERSON><PERSON>(node ace test:*)", "<PERSON><PERSON>(pkill:*)", "Bash(node ace migration:run:*)", "Bash(NODE_ENV=test node ace migration:run)", "Bash(NODE_ENV=test node ace test --files \"app/features/session/tests/functional/user-registration.spec.ts\" --grep \"registration creates both AuthUser\")", "Bash(node ace migration:fresh:*)", "Bash(NODE_ENV=test npm test)", "Bash(NODE_ENV=test npm test -- --grep \"Authentication Basic\")", "Bash(NODE_ENV=test npm test -- --grep \"task filtering\")", "Bash(NODE_ENV=test npm test -- --grep \"Profile Basic\")", "<PERSON><PERSON>(echo:*)", "Bash(NODE_ENV=test node ace test)", "Bash(NODE_ENV=test node ace test -- --grep \"Task Basic\")", "Bash(NODE_ENV=test node ace test -- --grep \"task endpoints respond correctly\")", "Bash(NODE_ENV=test node ace test app/features/task/tests/functional/task-basic.spec.ts)", "Bash(NODE_ENV=test node ace test -- --files=\"**/task-basic.spec.ts\")", "Bash(NODE_ENV=test node ace test -- --grep \"user can list tasks\")", "Bash(git add:*)", "Bash(npm test:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(npx eslint:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(node ace migration:status:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}