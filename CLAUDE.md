# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build the application for production
npm start           # Start production server
```

### Code Quality
```bash
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run typecheck    # Run TypeScript type checking
```

### Testing
```bash
npm test            # Run all tests
node ace test       # Alternative test command
```

### Database
```bash
node ace migration:run    # Run database migrations
node ace db:seed         # Seed the database
```

## Architecture

### **CRITICAL: Polymorphic Activity Architecture**
**Key Discovery (2025-01-25):** The project uses a **polymorphic relationship pattern**, NOT flattened activities or unified activities table.

**Database Design:**
- **Separate tables**: `tasks` and `events` with all activity fields included directly
- **Polymorphic references**: Related features use `activityType` + `activityId` pattern
- **No unified activities table**: Activity model exists but is non-functional (transitional artifact)

**Evidence:**
```sql
-- privacy_policy_assignments table (IMPLEMENTED)
activity_type ENUM('task', 'event')  -- Discriminator 
activity_id UUID                     -- Polymorphic foreign key to tasks.id OR events.id

-- Future tables follow same pattern:
-- commitments, activity_tags, mentions_to_task, activity_log
```

**Implementation Status: ~85% Complete**
- ✅ 23 database tables implemented (vs ~10 originally planned)
- ✅ Advanced privacy system with template-based rules
- ✅ Complete contact and group management
- ✅ AI integration with LangchainJS (Vercel AI SDK migration completed)
- ❌ 9 tables remaining (commitments, tagging, availability, audit)

### Feature-Based Structure
The application follows a feature-based architecture under `app/features/`:

- **session/**: Authentication and JWT token management ✅
- **profile/**: User profiles and profile settings ✅
- **contact/**: Contact management including lists and requests ✅
- **group/**: Group management with members and invitations ✅
- **task/**: AI task management with multiple AI service integrations ✅
- **event/**: Event management ✅
- **privacy-policy/**: Privacy policy templates, categories, and rules ✅
- **user/**: User model and related functionality ✅

### Key Patterns
- **Controllers**: Handle HTTP requests, delegate to services
- **Services**: Business logic layer (e.g., `contact_service.ts`, `session_service.ts`)
- **Models**: Database models with relationships and hooks
- **Validators**: Input validation using VineJS
- **Types**: TypeScript type definitions organized by feature

### Import Aliases
The project uses import aliases defined in `package.json`:
- `#controllers/*` → `./app/controllers/*.js`
- `#features/*` → `./app/features/*.js`
- `#models/*` → `./app/models/*.js`
- `#services/*` → `./app/services/*.js`
- `#types/*` → `./app/types/*.js`
- `#utils/*` → `./app/utils/*.js`
- `#validators/*` → `./app/validators/*.js`

### Authentication System
- JWT-based authentication with refresh tokens
- Access tokens expire in 15 minutes, refresh tokens in 7 days
- Auth middleware protects routes under `/api/v1/` (except auth endpoints)
- Session management in `app/features/session/`

### Database
- PostgreSQL with Lucid ORM
- Migrations in `database/migrations/`
- Models use soft deletes via `adonis-lucid-soft-deletes`
- UUID primary keys for most entities

**Current Implementation (23 tables):**
```
✅ Authentication: auth_users, users, profiles, profile_settings, auth_access_tokens, refresh_tokens
✅ Activities: tasks, events (polymorphic architecture)
✅ Privacy System: policy_categories, privacy_policy_templates, policy_template_rules, privacy_policy_assignments
✅ Contact System: user_contacts, user_contact_lists, contact_list_members
✅ Group System: user_groups, group_members, group_invitation_requests
```

**Missing Tables (9 remaining):**
```
❌ Collaboration: commitments, activity_log, mentions_to_task
❌ Organization: tag, activity_tag, user_availability_slot
```

### AI Integration
**Migration Status: ✅ COMPLETED** - LangchainJS is primary implementation
- ✅ **LangChain** (`langchain_ai_service.ts`) - ACTIVE implementation with structured outputs
- ⚠️ Vercel AI SDK (`vercel_ai_service.ts`) - Legacy, still present but LangChain is primary
- ✅ Task Master AI (`task-master-ai` package)
- **AI Endpoint**: `POST /api/v1/ai/prompt` for natural language task creation

### Testing
- Unit tests: `tests/unit/` and `app/features/*/tests/unit/`
- Functional tests: `tests/functional/` and `app/features/*/tests/functional/`
- Test configuration in `adonisrc.ts`

### Privacy Policy System
Complex privacy management system with:
- Policy templates and categories
- Rule-based access control
- Assignment system for user privacy settings
- Performance optimization strategies documented in `docs/`

## Development Notes

### Code Style
- Follow AdonisJS best practices from `cursor-rules/adonisjs-best-practices.md`
- Use explicit TypeScript types
- Keep controllers slim, move business logic to services
- Use dependency injection for services
- Implement proper error handling with custom exceptions

### Security
- Rate limiting on API routes
- CORS configuration
- Input validation and sanitization
- Environment-based configuration

## Missing Features & Development Roadmap

### **Priority 1: Collaboration System (2-3 weeks)**
**Critical missing feature for multi-user activities**

**Required Migration:**
```sql
CREATE TABLE commitments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activity_type activity_type_enum NOT NULL,  -- 'task' or 'event'
  activity_id UUID NOT NULL,                  -- Polymorphic reference
  host_user_id UUID REFERENCES users(id),
  invitee_user_id UUID REFERENCES users(id),
  invitee_status engagement_status_enum DEFAULT 'pending',
  host_status engagement_status_enum DEFAULT 'accepted',
  message TEXT,
  is_auto_accepted BOOLEAN DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP
);
```

**Missing API Endpoints:**
```typescript
POST   /api/v1/invitations        // Send invitation (polymorphic)
GET    /api/v1/invitations/received
PUT    /api/v1/invitations/:id    // Accept/decline
DELETE /api/v1/invitations/:id    // Cancel
```

### **Priority 2: Organization Tools (1-2 weeks)**
**Tagging and timer functionality**

**Required Migrations:**
```sql
-- Polymorphic tagging system
CREATE TABLE tags (...);
CREATE TABLE activity_tags (
  activity_type activity_type_enum NOT NULL,
  activity_id UUID NOT NULL,  -- Polymorphic reference
  tag_id UUID REFERENCES tags(id)
);
```

**Missing API Endpoints:**
```typescript
POST   /api/v1/tags
POST   /api/v1/activities/tags   // Polymorphic tagging
POST   /api/v1/tasks/:id/timer/start
GET    /api/v1/timer/active
```

### **Priority 3: Availability Management (2-3 weeks)**
**User availability slots and scheduling**

**Required Migration:**
```sql
CREATE TABLE user_availability_slots (
  user_id UUID REFERENCES users(id),
  visibility_scope availability_scope_enum,
  -- Complex availability definition fields
);
```

### **Architecture Patterns to Follow**
When implementing missing features:

1. **Continue Polymorphic Pattern**: Use `activityType` + `activityId` for all activity references
2. **Feature-based Structure**: Place new features under `app/features/[feature]/`
3. **Service Layer**: Business logic in services, controllers only handle HTTP
4. **Comprehensive Testing**: Unit + functional tests for all new features
5. **Soft Deletes**: Include `deletedAt` for all user-facing entities

### **Performance Considerations**
- Add indexes for polymorphic queries: `(activity_type, activity_id)`
- Consider denormalization for high-frequency availability queries
- Implement caching for privacy policy rule evaluation