# Task ID: 3
# Title: Develop Core Task Management
# Status: done
# Dependencies: 2
# Priority: high
# Description: Implement CRUD endpoints and service logic for the Task model, which already exists in @task/task_model.ts. The model schema is defined, but endpoints for creating, reading, updating, and deleting tasks are not yet implemented. Focus on CRUD, validation, and user-task relationships. Task-related notifications and event handling will be addressed in a later phase.
# Details:
The Task model is already defined in @task/task_model.ts with the following properties:
- id: string (primary key, UUID)
- user: HasOne<User> (relation)
- title: string (required)
- description: string | null (optional)
- status: 'pending' | 'in_progress' | 'completed'
- priority: string | null (optional)
- dueDate: DateTime | null (optional)
- createdAt: DateTime (auto-generated)
- updatedAt: DateTime (auto-updated)
- deletedAt: DateTime | null (soft delete)

The next steps are:
1. Implement CRUD endpoints for Task (create, read, update, delete) in the controller.
2. Add request validation for task creation and updates.
3. Ensure user-task relationship is enforced (tasks are always associated with a user).
4. Implement filtering and pagination for task listing.
5. Write unit and integration tests for all endpoints and model logic.

Note: Task-related notifications and event handling are deferred to a later phase.

# Test Strategy:
Write unit tests for Task model and relationships. Create integration tests for each CRUD endpoint. Test filtering and pagination functionality.

# Subtasks:
## 1. Create Task model and database migration [done]
### Dependencies: None
### Description: Define the Task model with all necessary fields and create a database migration to establish the tasks table with proper relationships to users.
### Details:
Note: The Task model already exists in @task/task_model.ts with the following properties:
- id: string (primary key, UUID)
- user: HasOne<User> (relation)
- title: string (required)
- description: string | null (optional)
- status: 'pending' | 'in_progress' | 'completed'
- priority: string | null (optional)
- dueDate: DateTime | null (optional)
- createdAt: DateTime (auto-generated)
- updatedAt: DateTime (auto-updated)
- deletedAt: DateTime | null (soft delete)

For this subtask:
1. Review the existing Task model to ensure it meets all requirements
2. Verify that the relationship between User and Task models is properly defined
3. Check if any database migrations are needed to align with the existing model
4. Confirm that appropriate indexes exist for frequently queried columns

Testing approach:
- Verify the database schema matches the model definition
- Confirm relationships are properly established
- Test creating and retrieving task records
<info added on 2025-05-17T11:12:52.397Z>
The Task model already exists in app/features/task/task_model.ts with the following properties:
- id: string (primary key, UUID)
- user: HasOne<User> (relation)
- title: string (required)
- description: string | null (optional)
- status: 'pending' | 'in_progress' | 'completed'
- priority: string | null (optional)
- dueDate: DateTime | null (optional)
- createdAt: DateTime (auto-generated)
- updatedAt: DateTime (auto-updated)
- deletedAt: DateTime | null (soft delete)

For this subtask:
1. Review the existing Task model to ensure it meets all requirements:
   - Verify all necessary fields are present
   - Check that field types are appropriate
   - Ensure validation rules are properly defined
   - Confirm that optional/required constraints are correctly set

2. Verify that the relationship between User and Task models is properly defined:
   - Check the User-Task relationship (one-to-many)
   - Ensure foreign key constraints are properly set
   - Verify cascade behaviors for updates/deletes

3. Check if any database migrations are needed:
   - Compare the existing database schema with the model definition
   - Identify any discrepancies that require migration
   - Create migration files if necessary to align the database with the model

4. Confirm that appropriate indexes exist for frequently queried columns:
   - Add indexes for columns used in WHERE clauses (status, userId, dueDate)
   - Consider composite indexes for common query patterns
   - Ensure the primary key is properly indexed

Testing approach:
- Verify the database schema matches the model definition
- Confirm relationships are properly established
- Test creating and retrieving task records
- Validate that indexes are being utilized in common queries
</info added on 2025-05-17T11:12:52.397Z>
<info added on 2025-05-17T11:16:11.987Z>
After reviewing the Task model and its relationship with the User model, I've identified several issues that need to be addressed:

1. The relationship between Task and User is incorrectly defined. Currently, it's using a HasOne relationship when it should be a BelongsTo relationship from Task to User.

2. The Task model is missing a userId column which is necessary for the foreign key relationship.

3. The User model needs a hasMany relationship to Task.

4. The database needs appropriate indexes for frequently queried columns.

Implementation plan:

1. Modify Task model (app/features/task/task_model.ts):
   - Import BelongsTo and belongsTo from @adonisjs/lucid/orm
   - Add userId column definition: @column() declare userId: string
   - Change the relationship from @hasOne to @belongsTo(() => User, { foreignKey: 'userId' })
   - Verify User import is correct (from #models/user)

2. Update User model (app/models/user.ts):
   - Import Task from #features/task/task_model
   - Add tasks relationship: @hasMany(() => Task, { foreignKey: 'userId' }) declare tasks: HasMany<typeof Task>
   - Ensure HasMany and hasMany are imported from @adonisjs/lucid/orm

3. Create migration file:
   - Filename: [timestamp]_add_user_id_and_indexes_to_tasks.ts
   - Use schema.alterTable('tasks', (table) => {...})
   - In up() method:
     - Add user_id column with foreign key constraint
     - Add indexes for user_id, status, and due_date
   - In down() method:
     - Drop indexes and user_id column

This implementation will correct the relationship between User and Task models and ensure proper database structure with appropriate indexes for performance optimization.
</info added on 2025-05-17T11:16:11.987Z>
<info added on 2025-05-17T11:19:05.159Z>
After reviewing the TaskSchema in app/features/task/services/task_service.ts, I've discovered that the Task model is missing several important fields that are needed for AI extraction and user input functionality. These fields are defined in the TaskSchema but not yet implemented in the Task model or database schema.

The following fields need to be added to the Task model:
- location: string[] | null - For storing location information related to tasks
- remindBefore: string[] | null - For storing reminder timing preferences
- tags: string[] | null - For categorizing tasks with user-defined tags
- suggestedTags: string[] | null - For AI-suggested tags based on task content
- people: string[] | null - For storing people associated with the task

Implementation plan:

1. Update Task model (app/features/task/task_model.ts):
   - Add the following column definitions:
     ```typescript
     @column({ columnName: 'location' }) declare location: string[] | null
     @column({ columnName: 'remind_before' }) declare remindBefore: string[] | null
     @column() declare tags: string[] | null
     @column({ columnName: 'suggested_tags' }) declare suggestedTags: string[] | null
     @column() declare people: string[] | null
     ```
   - Ensure proper imports are in place
   - Update any type definitions or interfaces as needed

2. Create a new migration file:
   - Filename: [timestamp]_add_ai_fields_to_tasks.ts
   - Use schema.alterTable('tasks', (table) => {...})
   - In up() method:
     - Add location column: table.jsonb('location').nullable()
     - Add remind_before column: table.jsonb('remind_before').nullable()
     - Add tags column: table.jsonb('tags').nullable()
     - Add suggested_tags column: table.jsonb('suggested_tags').nullable()
     - Add people column: table.jsonb('people').nullable()
   - In down() method:
     - Drop all newly added columns

3. Test the migration:
   - Run the migration and verify the columns are added correctly
   - Test serialization/deserialization of JSON arrays to these columns
   - Verify that the model can properly read/write these fields

This implementation will ensure that the Task model and database schema align with the TaskSchema defined in the service layer, allowing for proper storage of AI-extracted and user-provided data.
</info added on 2025-05-17T11:19:05.159Z>

## 2. Implement Task repository and service layer [done]
### Dependencies: 3.1
### Description: Create a repository pattern implementation for task data access and a service layer to encapsulate business logic for task management. [Updated: 5/17/2025]
### Details:
1. Create a TaskRepository class/interface that handles data access operations:
   - findById(id)
   - findByUserId(userId, options)
   - create(taskData)
   - update(id, taskData)
   - delete(id)
   - findAll(filters, pagination)

2. Implement a TaskService class that uses the repository and contains business logic:
   - getTasks(userId, filters, pagination)
   - getTaskById(id, userId)
   - createTask(taskData, userId)
   - updateTask(id, taskData, userId)
   - deleteTask(id, userId)
   - markTaskComplete(id, userId)

3. Add validation logic in the service layer for task creation/updates

4. Implement error handling for common scenarios (task not found, unauthorized access)

Testing approach:
- Unit test repository methods with mock database
- Test service layer with mocked repository
- Verify business rules are correctly enforced
<info added on 2025-05-17T14:57:47.176Z>
1. Create a TaskRepository class/interface that handles data access operations:\n   - findById(id)\n   - findByUserId(userId, options)\n   - create(taskData)\n   - update(id, taskData)\n   - delete(id)\n   - findAll(filters, pagination)\n\n2. Implement a TaskService class that uses the repository and contains business logic:\n   - getTasks(userId, filters, pagination)\n   - getTaskById(id, userId)\n   - createTask(taskData, userId)\n   - updateTask(id, taskData, userId)\n   - deleteTask(id, userId)\n   - markTaskComplete(id, userId)\n\n3. Add validation logic in the service layer for task creation/updates\n\n4. Implement error handling for common scenarios (task not found, unauthorized access)\n\nTesting approach:\n- Unit test repository methods with mock database\n- Test service layer with mocked repository\n- Verify business rules are correctly enforced\n\nNote: When implementing the repository and service layer, ensure they align with AdonisJS resource controller conventions. The service methods should map to standard resource controller actions:\n- getTasks → index\n- getTaskById → show\n- createTask → store\n- updateTask → update\n- deleteTask → destroy\n\nThis will ensure smooth integration with the next subtask (3.3) where we'll be using AdonisJS resource controllers instead of custom controller names.
</info added on 2025-05-17T14:57:47.176Z>
<info added on 2025-05-17T14:59:58.847Z>
The implementation plan for the Task Repository and Service Layer is structured in four phases:

**Phase 1: Repository Layer (`app/features/task/task_repository.ts`)**
1. Create the `TaskRepository` class with dependency injection for the `Task` model
2. Implement core data access methods:
   - `findById(id: string)`: Retrieves a single task by ID using `Task.find(id)`
   - `findByUserId(userId: string, options?)`: Builds queries with filters and pagination
   - `create(taskData: Partial<Task>, userId: string)`: Creates new tasks with proper user association
   - `update(id: string, taskData: Partial<Task>, userId: string)`: Updates tasks with ownership verification
   - `delete(id: string, userId: string)`: Performs soft deletes with proper authorization checks
   - `findAll(filters, pagination)`: Retrieves tasks with flexible filtering options

**Phase 2: Service Layer (`app/features/task/task_service.ts`)**
1. Create the `TaskService` class with `TaskRepository` injection
2. Implement business logic methods that map to controller actions:
   - `getTasks(userId, filters, pagination)`: Maps to index action
   - `getTaskById(id, userId)`: Maps to show action with ownership verification
   - `createTask(taskData, userId)`: Maps to store action with validation
   - `updateTask(id, taskData, userId)`: Maps to update action
   - `deleteTask(id, userId)`: Maps to destroy action
   - `markTaskComplete(id, userId)`: Special business logic for task completion

**Phase 3: Error Handling & Validation**
1. Define custom exceptions in `app/exceptions/` directory:
   - `TaskNotFoundException`
   - `UnauthorizedTaskAccessException`
2. Implement validation logic for task creation/updates
3. Add comprehensive error handling for common scenarios

**Phase 4: Directory Structure**
- Organize code in `app/features/task/` directory
- Follow AdonisJS conventions for smooth integration with controllers
- Ensure service methods align with resource controller actions

This implementation will ensure proper separation of concerns, with data access logic isolated in the repository and business rules in the service layer. The structure aligns with AdonisJS resource controller conventions to facilitate integration with the upcoming Task controller development in subtask 3.3.
</info added on 2025-05-17T14:59:58.847Z>

## 3. Develop Task controller with CRUD endpoints [done]
### Dependencies: 3.2
### Description: Create a controller that exposes REST API endpoints for task management operations, including proper request/response handling and validation.
### Details:
1. Create a TaskController with the following endpoints:
   - GET /tasks (list tasks with filtering and pagination)
   - GET /tasks/:id (get a specific task)
   - POST /tasks (create a new task)
   - PUT /tasks/:id (update a task)
   - DELETE /tasks/:id (delete a task)
   - PATCH /tasks/:id/status (update task status)

2. Implement request validation using appropriate validation middleware:
   - Required fields validation (title required)
   - Date format validation for dueDate
   - Enum validation for status ('pending' | 'in_progress' | 'completed')
   - Validation for priority if provided

3. Add proper HTTP status codes and error responses:
   - 200/201 for successful operations
   - 400 for validation errors
   - 404 for task not found
   - 403 for unauthorized access

4. Ensure all endpoints extract user_id from authenticated request and pass to service layer

Testing approach:
- Integration tests for each endpoint
- Test validation error cases
- Verify authentication and authorization

## 4. Implement filtering, sorting, and pagination for tasks [done]
### Dependencies: 3.3
### Description: Add advanced querying capabilities to the task listing functionality, allowing users to filter by various criteria, sort results, and paginate through large datasets.
### Details:
1. Enhance the repository layer to support filtering by:
   - status ('pending' | 'in_progress' | 'completed')
   - dueDate range (before/after specific dates)
   - priority levels
   - search by title/description

2. Add sorting capabilities for:
   - dueDate (ascending/descending)
   - priority
   - createdAt
   - title (alphabetical)

3. Implement pagination with:
   - Limit/offset or page/size parameters
   - Total count in response metadata
   - Links to next/previous pages

4. Update the controller to accept and validate query parameters for these features

5. Add appropriate database indexes to support efficient filtering and sorting

Testing approach:
- Test various filter combinations
- Verify sorting works correctly
- Test pagination with large datasets
- Benchmark query performance
<info added on 2025-05-30T13:51:54.373Z>
Implementation Progress:

I've started implementing the filtering, sorting, and pagination features for tasks. Here's my current analysis and implementation plan:

Code Locations:
- Task model: app/features/task/task_model.ts
- TaskService: app/features/task/task_service.ts
- Controller: AITaskController in app/features/task/tasks_controller.ts
- Database: Tasks table has indexes for user_id, status, and due_date

Implementation Steps:
1. Creating TaskQueryParams interface with:
   - filters: status, dueDate range, priority, search text
   - sorting: field and direction
   - pagination: page/size or offset/limit

2. Enhancing TaskService.getTasksByProfileId() to:
   - Accept TaskQueryParams
   - Build dynamic query with Prisma
   - Apply filters, sorting, and pagination

3. Updating AITaskController.showAll() to:
   - Parse and validate query parameters
   - Pass structured params to service
   - Return paginated response with metadata

4. Adding response metadata including:
   - Total count of matching records
   - Current page information
   - Links to next/previous pages

Implementation priorities align with the original requirements, focusing first on status filtering, due date range, and priority filtering.
</info added on 2025-05-30T13:51:54.373Z>
<info added on 2025-05-30T13:57:35.352Z>
## Implementation Completed

All requirements have been successfully implemented with the following components:

### Type Definitions
- Created `app/features/task/types/task_query_types.ts` with interfaces for:
  - TaskFilters (status, priority, date range, search, tags)
  - TaskSort (field and direction)
  - TaskPagination (page/limit)
  - PaginatedResponse (structured API responses)

### Validation
- Implemented `app/features/task/task_query_validator.ts` using VineJS
- Validates all query parameters including enum values and pagination limits

### Service Layer
- Enhanced `app/features/task/task_service.ts` with:
  - `getTasksWithQuery()` method for filtering, sorting, and pagination
  - `applyFilters()` method supporting all required filter types
  - `applySorting()` method with custom priority sorting
  - Pagination with comprehensive metadata

### Controller & Routes
- Updated `app/features/task/tasks_controller.ts` to handle query parameters
- Fixed route configuration in `start/routes.ts`

### Testing
- Created comprehensive test suite in `app/features/task/tests/functional/task_query.spec.ts`
- 12 test cases covering all functionality

### Database Optimization
- Leveraged existing indexes on user_id, status, and due_date
- Implemented JSON superset queries for tag filtering
- Optimized query building with proper WHERE clauses

All requirements have been met with a production-ready implementation following AdonisJS best practices.
</info added on 2025-05-30T13:57:35.352Z>

