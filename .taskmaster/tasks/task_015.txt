# Task ID: 15
# Title: Implement Group Management System
# Status: pending
# Dependencies: 14
# Priority: medium
# Description: Create a comprehensive group management system with models, relationships, and workflows for managing user groups, including group membership, invitations, and role-based permissions.
# Details:
This task involves implementing the complete group management system as defined in the ERD:

1. Create Models:
   - UserGroup model with fields:
     * id (UUID, primary key)
     * ownerUserId (foreign key to User)
     * name (string, required)
     * description (string, nullable)
     * createdAt, updatedAt timestamps
     * deletedAt (for soft deletes)
   
   - GroupMember model with fields:
     * id (UUID, primary key)
     * groupId (foreign key to UserGroup)
     * userId (foreign key to User)
     * role (enum: GroupMemberRole - 'owner', 'admin', 'member')
     * createdAt, updatedAt timestamps
     * deletedAt (for soft deletes)
   
   - GroupInvitationRequest model with fields:
     * id (UUID, primary key)
     * groupId (foreign key to UserGroup)
     * userId (foreign key to User)
     * type (enum: GroupInvitationRequestType - 'invitation', 'request')
     * status (enum: 'pending', 'accepted', 'declined')
     * initiatorUserId (foreign key to User)
     * message (string, nullable)
     * createdAt, updatedAt timestamps
     * deletedAt (for soft deletes)

2. Define Relationships:
   - UserGroup -> User (owner): belongsTo relationship
   - GroupMember -> UserGroup: belongsTo relationship
   - GroupMember -> User: belongsTo relationship
   - GroupInvitationRequest -> UserGroup: belongsTo relationship
   - GroupInvitationRequest -> User: belongsTo relationship (invitee/requestor)
   - GroupInvitationRequest -> User: belongsTo relationship (initiator)

3. Create Database Migrations:
   - user_group table migration with ownerUserId field
   - group_member table migration with userId field
   - group_invitation_request table migration with userId and initiatorUserId fields
   - Add appropriate indexes and foreign key constraints

4. Implement GroupService with methods for:
   - createGroup(ownerUserId, name, description)
   - updateGroup(groupId, data)
   - deleteGroup(groupId)
   - getGroupById(groupId)
   - getGroupsByUserId(userId)
   - addMember(groupId, userId, role)
   - updateMemberRole(groupId, userId, newRole)
   - removeMember(groupId, userId)
   - createInvitation(groupId, userId, initiatorUserId, message)
   - createJoinRequest(groupId, userId, message)
   - respondToInvitation(invitationId, status)
   - respondToJoinRequest(requestId, status)

5. Implement Validation Logic:
   - Validate group ownership for sensitive operations
   - Enforce member limits if applicable
   - Implement role-based permission checks
   - Prevent duplicate invitations/requests
   - Validate role transitions (e.g., can't demote the owner)

6. Create GroupController with API Endpoints:
   - POST /api/groups - Create a new group
   - GET /api/groups - List groups (with filtering options)
   - GET /api/groups/:id - Get group details
   - PUT /api/groups/:id - Update group details
   - DELETE /api/groups/:id - Delete a group
   - GET /api/groups/:id/members - List group members
   - POST /api/groups/:id/members - Add a member
   - PUT /api/groups/:id/members/:userId - Update member role
   - DELETE /api/groups/:id/members/:userId - Remove a member
   - POST /api/groups/:id/invitations - Create an invitation
   - GET /api/groups/:id/invitations - List invitations
   - POST /api/groups/:id/join-requests - Create a join request
   - GET /api/groups/:id/join-requests - List join requests
   - PUT /api/groups/invitations/:id - Respond to invitation
   - PUT /api/groups/join-requests/:id - Respond to join request

7. Implement Authorization Middleware:
   - Create middleware to check if user is group owner
   - Create middleware to check if user is group admin
   - Create middleware to check if user is group member

8. Add Comprehensive Documentation:
   - Document all models, relationships, and endpoints
   - Include examples of common group management workflows
   - Document role-based permissions and restrictions

# Test Strategy:
1. Unit Tests:
   - Test all GroupService methods with various inputs and edge cases
   - Test model validations and constraints
   - Test relationship integrity between models
   - Test role-based permission logic
   - Test invitation and request workflows

2. Integration Tests:
   - Test the complete group creation workflow
   - Test member invitation and acceptance flow
   - Test join request and approval flow
   - Test role management and permissions
   - Test group deletion and cleanup
   - Test error handling and validation responses

3. API Tests:
   - Test all API endpoints with valid inputs
   - Test API endpoints with invalid inputs to verify error handling
   - Test authorization checks for each endpoint
   - Test pagination and filtering for list endpoints

4. Specific Test Cases:
   - Create a group and verify all fields are saved correctly
   - Attempt to create a group with invalid data and verify validation errors
   - Add members to a group with different roles and verify role assignments
   - Test that only group owners can delete groups
   - Test that only owners/admins can invite new members
   - Test that members can leave groups
   - Test that owners cannot leave groups without transferring ownership
   - Test invitation expiration if implemented
   - Test duplicate invitation prevention
   - Test cascading deletions when a group is removed
   - Test soft delete functionality for all models
   - Verify that foreign key references to userId work correctly
   - Test that relationships between User and group-related models function properly

5. Performance Tests:
   - Test group listing with large numbers of groups
   - Test member listing with large numbers of members
   - Test invitation/request listing with many pending items

6. Security Tests:
   - Verify that users cannot access groups they don't belong to
   - Verify that regular members cannot perform admin actions
   - Verify that non-members cannot view private group details
   - Ensure proper authorization checks using userId instead of profileId

# Subtasks:
## 1. Design and Implement Data Models and Relationships [pending]
### Dependencies: None
### Description: Define and implement the UserGroup, GroupMember, and GroupInvitationRequest models with all specified fields and establish the required relationships between models as per the ERD.
### Details:
Create the data models with all necessary fields, enums, and timestamps. Set up belongsTo relationships among UserGroup, GroupMember, GroupInvitationRequest, and User entities as described.

## 2. Create Database Migrations and Constraints [pending]
### Dependencies: 15.1
### Description: Develop database migration scripts for all group management tables, including indexes and foreign key constraints to enforce data integrity.
### Details:
Write migration scripts for user_group, group_member, and group_invitation_request tables. Ensure all foreign keys, indexes, and soft delete columns are included.

## 3. Develop Group Management Service Logic [pending]
### Dependencies: 15.2
### Description: Implement the GroupService with methods for group creation, updates, member management, invitation handling, and join request workflows.
### Details:
Code all service methods for managing groups, members, invitations, and requests, ensuring business logic aligns with requirements.

## 4. Implement API Endpoints and Authorization Middleware [pending]
### Dependencies: 15.3
### Description: Build the GroupController with all required REST API endpoints and integrate middleware for role-based authorization and group membership checks.
### Details:
Expose endpoints for group, member, invitation, and join request management. Add middleware to enforce owner, admin, and member permissions.

## 5. Add Validation Logic and Documentation [pending]
### Dependencies: 15.4
### Description: Implement validation for group ownership, role transitions, duplicate requests, and member limits. Document all models, endpoints, workflows, and permission rules.
### Details:
Add validation checks to service and controller layers. Write comprehensive documentation covering data models, API usage, workflows, and permission structures.

