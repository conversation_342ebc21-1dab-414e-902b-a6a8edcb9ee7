# Task ID: 18
# Title: Implement Commitment and Engagement System
# Status: pending
# Dependencies: 17, 11
# Priority: medium
# Description: Create the activity commitment system for managing invitations and responses, including models, database migrations, relationships, and API endpoints for handling activity sharing and collaborative scheduling.
# Details:
This task involves implementing a comprehensive commitment and engagement system:

1. Create Commitment Model:
   - Define the Commitment model with fields:
     * id (UUID, primary key)
     * activityType (string, for polymorphic relationship)
     * activityId (UUID, for polymorphic relationship)
     * hostUserId (foreign key to User)
     * inviteeUserId (foreign key to User)
     * inviteeStatus (EngagementStatus enum: 'pending', 'accepted', 'declined', 'maybe')
     * hostStatus (EngagementStatus enum: 'pending', 'accepted', 'declined', 'maybe')
     * message (string, nullable)
     * isAutoAccepted (boolean)
     * createdAt, updatedAt timestamps
     * deletedAt (for soft deletes)

2. Database Migration:
   - Create a migration file for the commitment table
   - Include all fields defined in the model
   - Set up appropriate indexes for performance optimization
   - Add foreign key constraints for hostUserId and inviteeUserId to users table
   - Set up polymorphic indexes for activityType + activityId

3. Model Relationships:
   - Add polymorphic relationships to Task and Event models for commitments
   - Add hasMany relationships to User model for:
     * hostCommitments (as host)
     * inviteeCommitments (as invitee)
   - Implement eager loading options for these relationships

4. Commitment Workflow Implementation:
   - Create methods for:
     * createInvitation(activityType, activityId, hostUserId, inviteeUserId, message)
     * acceptInvitation(commitmentId, userId)
     * declineInvitation(commitmentId, userId)
     * setMaybeResponse(commitmentId, userId)
     * cancelInvitation(commitmentId, userId)
   - Implement proper state transitions and validation

5. Auto-Acceptance Support:
   - Integrate with UserSettings to check auto-acceptance preferences
   - Implement policy rule evaluation using the PolicyEvaluationService
   - Add logic to automatically accept invitations based on settings and rules

6. CommitmentService Implementation:
   - Create CommitmentService class with methods for all commitment operations
   - Implement transaction handling for data integrity
   - Add logging for important state changes
   - Include error handling and appropriate exception types

7. API Endpoints:
   - Create RESTful endpoints for:
     * POST /api/commitments (create invitation)
     * GET /api/commitments (list commitments with filtering)
     * GET /api/commitments/:id (get specific commitment)
     * PUT /api/commitments/:id/respond (respond to invitation)
     * DELETE /api/commitments/:id (cancel/delete commitment)

8. CommitmentController:
   - Implement CommitmentController with proper request validation
   - Add authorization checks using middleware
   - Ensure proper access control (host can modify host status, invitee can modify invitee status)
   - Return appropriate HTTP status codes and response formats

9. Notification System:
   - Integrate with the application's notification system
   - Create notification templates for:
     * New invitation received
     * Invitation accepted/declined/maybe
     * Invitation canceled
   - Trigger notifications on commitment status changes

10. Validation and Business Rules:
    - Implement validation for engagement status transitions
    - Add business rules such as:
      * Only host can invite users to an activity
      * Users can only respond to invitations addressed to them
      * Validate activity exists and is active
      * Check for duplicate invitations
    - Ensure proper handling of polymorphic relationships for different activity types

11. Testing:
    - Create unit tests for the Commitment model and CommitmentService
    - Implement integration tests for the API endpoints
    - Add specific tests for edge cases and business rule validation
    - Test notification triggers and auto-acceptance functionality
    - Test polymorphic relationships with different activity types

References:
- Follow the commitment table structure in docs/database_design/skedai_erd.dbml
- Refer to docs/database_design/README.md for the Commitment & Engagement System section
- Implement polymorphic relationships for the flattened activity structure

# Test Strategy:
1. Unit Testing:
   - Test Commitment model validation rules
   - Test CommitmentService methods with mocked dependencies
   - Verify proper state transitions for all commitment workflows
   - Test auto-acceptance logic with various user settings
   - Verify policy rule integration for commitment visibility
   - Test polymorphic relationship handling for different activity types

2. Integration Testing:
   - Test database migrations to ensure proper table creation
   - Verify relationships between Commitment, User, Task, and Event models
   - Test API endpoints with authenticated requests
   - Verify proper authorization checks for different user roles
   - Test transaction handling and rollback scenarios
   - Verify polymorphic queries work correctly across activity types

3. End-to-End Testing:
   - Create a complete workflow test from invitation to acceptance
   - Test the full notification flow when commitment status changes
   - Verify proper UI updates when commitment statuses change
   - Test commitments for both task and event activity types

4. Edge Case Testing:
   - Test handling of duplicate invitations
   - Verify behavior when an activity is deleted (cascade or soft delete)
   - Test concurrent modifications to the same commitment
   - Verify handling of invalid state transitions
   - Test performance with a large number of commitments
   - Test polymorphic relationships with non-existent activity types

5. Acceptance Testing:
   - Verify that users can successfully invite others to tasks and events
   - Confirm that invitees can respond with accept/decline/maybe
   - Test that auto-acceptance works based on user settings
   - Verify that notifications are received for all commitment actions
   - Confirm that commitment lists show the correct status for all parties

6. Regression Testing:
   - Ensure existing task and event functionality works with the new commitment system
   - Verify that policy rules still function correctly with commitments
   - Test that user availability is properly considered in the commitment workflow
   - Verify that the system works correctly after migration from profile-based to user-based ownership
