# Task ID: 16
# Title: Implement User Availability System
# Status: pending
# Dependencies: 15
# Priority: medium
# Description: Create a comprehensive availability management system with models, database migrations, and APIs for tracking user availability slots, including recurring and specific time slots with visibility controls.
# Details:
This task involves implementing the complete user availability system as defined in the ERD:

1. Create UserAvailabilitySlot model with the following fields:
   - id (UUID, primary key)
   - userId (foreign key to User)
   - title (string, descriptive name for the slot)
   - dayOfWeek (enum: 0-6 representing Sunday-Saturday)
   - startTime (time)
   - endTime (time)
   - recurringStartDate (date, nullable)
   - recurringEndDate (date, nullable)
   - specificStartDate (datetime, nullable)
   - specificEndDate (datetime, nullable)
   - isAvailable (boolean)
   - visibilityScope (enum: AvailabilityScopeType - 'public', 'private', 'contacts', 'group', 'contact_list')
   - visibilityTargetUserId (foreign key to User, nullable)
   - visibilityTargetGroupId (foreign key to UserGroup, nullable)
   - visibilityTargetContactListId (foreign key to UserContactList, nullable)
   - timestamps (createdAt, updatedAt)
   - deletedAt (for soft deletes)

2. Create database migration for user_availability_slot table:
   - Define all fields with appropriate data types
   - Set up foreign key constraints to users table
   - Add indexes for efficient querying
   - Configure soft delete functionality

3. Add relationships to the model:
   - User: belongsTo relationship
   - UserGroup: belongsTo relationship for visibility targeting
   - UserContactList: belongsTo relationship for visibility targeting

4. Implement AvailabilitySlotService with the following functionality:
   - createAvailabilitySlot(data): Create new availability slot with validation
   - updateAvailabilitySlot(id, data): Update existing slot with validation
   - deleteAvailabilitySlot(id): Soft delete a slot
   - getAvailabilitySlots(userId, filters): Get slots with optional filtering
   - checkAvailability(userId, startDate, endDate): Check if a user is available during a specific time period
   - resolveOverlappingSlots(userId, slots): Detect and resolve conflicting availability slots
   - calculateEffectiveAvailability(userId, startDate, endDate): Calculate effective availability considering both recurring and specific slots

5. Implement validation logic:
   - Ensure startTime is before endTime
   - Validate that either recurring or specific date fields are used appropriately
   - Validate that visibilityTargetUserId, visibilityTargetGroupId, and visibilityTargetContactListId are set according to the visibilityScope
   - Prevent invalid combinations of fields

6. Create AvailabilitySlotController with the following endpoints:
   - POST /api/availability-slots: Create new availability slot
   - GET /api/availability-slots: List availability slots with filtering options
   - GET /api/availability-slots/:id: Get specific availability slot
   - PUT /api/availability-slots/:id: Update availability slot
   - DELETE /api/availability-slots/:id: Delete availability slot
   - GET /api/users/:userId/availability: Check availability for a user in a date range

7. Implement authorization middleware:
   - Ensure users can only manage their own availability slots
   - Implement visibility scope checking for viewing other users' availability

8. Implement availability calculation logic:
   - Handle recurring slots (weekly patterns)
   - Handle specific date-time slots
   - Implement precedence rules (specific slots override recurring slots)
   - Calculate effective availability for a given time period

9. Add support for overlapping slot resolution:
   - Detect conflicting availability slots
   - Implement conflict resolution strategies
   - Provide warnings or errors for conflicting slots

10. Create utility functions for availability operations:
    - convertToTimeZone(): Handle time zone conversions
    - formatAvailabilityResponse(): Format availability data for API responses
    - validateAvailabilityRequest(): Validate incoming availability requests

# Test Strategy:
1. Unit Tests:
   - Test UserAvailabilitySlot model validation:
     * Verify that startTime must be before endTime
     * Test validation of visibilityScope against target fields
     * Test date range validation for recurring and specific slots
   - Test AvailabilitySlotService methods:
     * Test slot creation with valid and invalid data
     * Test slot updates with various field combinations
     * Test conflict detection with overlapping slots
     * Test availability calculation with different scenarios

2. Integration Tests:
   - Test database migrations:
     * Verify all fields are created with correct types
     * Test foreign key constraints to users table
     * Test indexes for query performance
   - Test API endpoints:
     * Test CRUD operations through the API
     * Test authorization rules for different users
     * Test filtering and pagination of availability slots

3. Functional Tests:
   - Test availability calculation scenarios:
     * Test recurring weekly slots
     * Test specific date-time slots
     * Test combination of recurring and specific slots
     * Test slots with different visibility scopes
   - Test edge cases:
     * Slots spanning midnight
     * Overlapping slots with different isAvailable values
     * Slots with and without end dates for recurring patterns

4. Performance Tests:
   - Test availability calculation performance with large numbers of slots
   - Test query performance for filtering availability slots

5. Acceptance Tests:
   - Verify that a user can create, view, update, and delete availability slots
   - Verify that availability is correctly calculated for different time periods
   - Verify that visibility scopes correctly control who can see availability
   - Verify that conflicts are properly detected and reported

6. Documentation:
   - Document the API endpoints with example requests and responses
   - Document the availability calculation logic and rules
   - Document the visibility scope system and how it integrates with groups and contacts
