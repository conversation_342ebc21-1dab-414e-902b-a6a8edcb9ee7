# Task ID: 2
# Title: Implement User and Profile Models with Authentication
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create Profile model with relationship to existing User model and implement profile management endpoints
# Details:
1. Create Profile model with specified fields (profile_id, first_name, last_name, birth_date)
2. Define relationship between existing User model and new Profile model
3. Create migrations for the Profile table
4. Implement profile management endpoints (create, view, update)
5. Update existing user endpoints to include profile information

# Test Strategy:
Write unit tests for the Profile model and its relationship with the User model. Create integration tests for profile management endpoints. Test that existing user endpoints correctly handle profile information.

# Subtasks:
## 1. Create User and Profile Database Models with Relationships [done]
### Dependencies: None
### Description: Define the Profile model with proper fields and establish its relationship with the existing User model in the database schema
### Details:
Implementation steps:
1. Create Profile model with fields: profile_id (UUID primary key), first_name, last_name, birth_date
2. Define one-to-one relationship between existing User model and new Profile model
3. Update User model to reference profile_id as a foreign key if needed
4. Set up cascade deletion so that when a User is deleted, the associated Profile is also deleted
5. Add appropriate indexes for query optimization
6. Create database migration scripts for the Profile table
7. Test the models by creating sample records and verifying relationships

Testing approach:
- Write unit tests to verify model creation and relationship integrity
- Test cascade operations
- Verify constraints and relationships

<info added on 2025-05-03T06:19:23.671Z>
Here's the additional information for the subtask:

```
Implementation considerations:

1. Examine the existing User model first:
   - Check if User model uses UUID or integer primary keys
   - Identify authentication fields (username, email, password) vs profile data
   - Note any existing relationships that might be affected

2. For the Profile model relationship:
   - Recommended approach: Profile should reference User (not vice versa)
   - Add user_id foreign key to Profile with unique constraint
   - This avoids modifying the existing User table structure

3. Additional Profile fields to consider:
   - profile_picture (URL or file reference)
   - bio/about text
   - location/address
   - phone_number
   - social media handles
   - preferences (JSON field)

4. Migration strategy:
   - Create a non-destructive migration that won't affect existing User data
   - Include a data migration plan for any users created before Profile existed
   - Consider adding a transaction wrapper for rollback safety

5. ORM relationship configuration:
   ```python
   # Example relationship in SQLAlchemy
   class Profile(Base):
       __tablename__ = 'profiles'
       id = Column(UUID, primary_key=True, default=uuid.uuid4)
       user_id = Column(UUID, ForeignKey('users.id', ondelete='CASCADE'), unique=True)
       # other fields
       user = relationship("User", back_populates="profile")
   
   # Add to existing User model
   class User(Base):
       # existing fields
       profile = relationship("Profile", back_populates="user", uselist=False, cascade="all, delete-orphan")
   ```

6. Testing specifics:
   - Test querying in both directions (User→Profile, Profile→User)
   - Verify lazy loading vs eager loading behavior
   - Test performance with larger datasets to validate indexing
```
</info added on 2025-05-03T06:19:23.671Z>

<info added on 2025-05-03T06:19:39.604Z>
```
Updated implementation steps:
1. Examine the existing User model structure to understand how to properly link the Profile model
   - Review authentication fields vs profile data separation
   - Check primary key type (UUID vs integer) for consistency
   - Note any existing relationships that might be affected

2. Create Profile model with fields:
   - profile_id (UUID primary key)
   - user_id (foreign key to User with unique constraint)
   - first_name, last_name, birth_date
   - Optional: profile_picture, bio, location, phone_number, social_media_handles, preferences (JSON)

3. Define the relationship approach:
   - Recommended: Profile references User (not vice versa)
   - This minimizes changes to existing User table and data
   - Example with SQLAlchemy:
     ```python
     class Profile(Base):
         __tablename__ = 'profiles'
         profile_id = Column(UUID, primary_key=True, default=uuid.uuid4)
         user_id = Column(UUID, ForeignKey('users.id', ondelete='CASCADE'), unique=True)
         first_name = Column(String(100))
         last_name = Column(String(100))
         birth_date = Column(Date)
         # other fields
         user = relationship("User", back_populates="profile")
     
     # Add to existing User model
     class User(Base):
         # existing fields
         profile = relationship("Profile", back_populates="user", uselist=False, 
                               cascade="all, delete-orphan")
     ```

4. Create non-destructive migration script:
   - Ensure it won't affect existing User data
   - Include data migration plan for existing users if needed
   - Use transaction wrapper for rollback safety

5. Test relationship thoroughly:
   - Verify bidirectional navigation (User→Profile, Profile→User)
   - Test lazy vs eager loading behavior
   - Confirm cascade deletion works as expected
   - Validate performance with larger datasets to verify index effectiveness
   - Test creation of profiles for existing users
```
</info added on 2025-05-03T06:19:39.604Z>

<info added on 2025-05-03T06:24:01.639Z>
```
Implementation validation and optimization notes:

1. Database constraint considerations:
   - Ensure the user_id foreign key in the Profile model has a NOT NULL constraint
   - Add CHECK constraints for data validation (e.g., birth_date < current_date)
   - Consider adding a unique index on (user_id, deleted_at IS NULL) to handle soft deletes properly

2. TypeScript type improvements:
   ```typescript
   // Add proper typing for the relationship
   interface ProfileAttributes {
     profileId: string;
     userId: string;
     firstName: string;
     lastName: string;
     birthDate?: Date;
     // other fields
   }

   interface ProfileCreationAttributes extends Optional<ProfileAttributes, 'profileId'> {}

   // Ensure proper typing for eager loading
   interface UserWithProfile extends User {
     profile?: Profile;
   }
   ```

3. Optimized query patterns:
   ```typescript
   // Efficient eager loading
   const userWithProfile = await User.findByPk(userId, {
     include: [{ model: Profile, as: 'profile' }]
   });

   // Efficient creation with transaction
   const transaction = await sequelize.transaction();
   try {
     const user = await User.create({ email, password }, { transaction });
     await Profile.create({ 
       userId: user.id, 
       firstName, 
       lastName 
     }, { transaction });
     await transaction.commit();
     return user;
   } catch (error) {
     await transaction.rollback();
     throw error;
   }
   ```

4. Performance considerations:
   - Add composite indexes for common query patterns (e.g., lastName + firstName)
   - Consider adding a full-text search index if profile searching will be implemented
   - For large datasets, implement pagination when querying profiles

5. Migration rollback strategy:
   - Ensure your migration has a proper down() method to revert changes
   - Test the rollback process to verify it works correctly
   - Consider data backup before running in production
```
</info added on 2025-05-03T06:24:01.639Z>

## 6. Implement Profile Creation Endpoint [done]
### Dependencies: 2.1
### Description: Create an endpoint to create and associate a profile with an existing user
### Details:
Implementation steps:
1. Create an endpoint that accepts profile information (first_name, last_name, birth_date)
2. Implement input validation for profile data
3. Associate the profile with the authenticated user
4. Handle cases where a user already has a profile (update vs. create)
5. Return appropriate success/error responses

Testing approach:
- Test profile creation with valid and invalid inputs
- Verify database entries after successful profile creation
- Test profile creation for users who already have profiles
- Test authorization (users should only be able to create/update their own profiles)

<info added on 2025-05-03T06:28:43.979Z>
Here's additional information to enhance the subtask:

```typescript
// Implementation details for profile validation
// app/features/profile/validators/profile_validator.ts
export const profileSchema = schema.create({
  firstName: schema.string({ trim: true }, [
    rules.required(),
    rules.minLength(2),
    rules.maxLength(50),
  ]),
  lastName: schema.string({ trim: true }, [
    rules.required(),
    rules.minLength(2),
    rules.maxLength(50),
  ]),
  birthDate: schema.date.optional({
    format: 'yyyy-MM-dd',
  }),
  profilePicture: schema.string.optional({ trim: true }, [
    rules.url(),
  ]),
  bio: schema.string.optional({ trim: true }, [
    rules.maxLength(500),
  ]),
  location: schema.string.optional({ trim: true }, [
    rules.maxLength(100),
  ]),
  phoneNumber: schema.string.optional({ trim: true }, [
    rules.mobile(),
  ]),
})

// Service implementation with transaction handling
// app/features/profile/services/profile_service.ts
public async createOrUpdateProfile(userId: number, profileData: ProfileData): Promise<Profile> {
  const user = await User.findOrFail(userId)
  
  return Database.transaction(async (trx) => {
    const existingProfile = await Profile.query()
      .where('user_id', userId)
      .first()
    
    if (existingProfile) {
      existingProfile.merge(profileData)
      await existingProfile.save()
      return existingProfile
    } else {
      const profile = new Profile()
      profile.fill({
        ...profileData,
        userId,
      })
      await profile.save()
      return profile
    }
  })
}

// Controller implementation with proper error handling
// app/features/profile/profile_controller.ts
public async createOrUpdate({ request, auth, response }: HttpContextContract) {
  try {
    const user = auth.user!
    const payload = await request.validate({
      schema: profileSchema,
    })
    
    const profile = await this.profileService.createOrUpdateProfile(user.id, payload)
    
    return response.status(200).json({
      status: 'success',
      data: profile,
    })
  } catch (error) {
    if (error.code === 'E_VALIDATION_FAILURE') {
      return response.status(422).json({
        status: 'error',
        message: 'Validation failed',
        errors: error.messages,
      })
    }
    
    return response.status(500).json({
      status: 'error',
      message: 'Failed to create or update profile',
    })
  }
}
```

Database schema considerations:
- Use `nullable()` for optional fields in migrations
- Add appropriate indexes for `user_id` (unique)
- Consider adding a `created_at` and `updated_at` timestamp

Security considerations:
- Sanitize user inputs to prevent XSS attacks
- Implement rate limiting to prevent abuse
- Consider adding CSRF protection for non-API routes

Performance optimizations:
- Cache frequently accessed profiles
- Use eager loading when retrieving profiles with related user data
</info added on 2025-05-03T06:28:43.979Z>

## 7. Implement Profile Retrieval Endpoint [done]
### Dependencies: 2.6
### Description: Create an endpoint to retrieve profile information for a user
### Details:
Implementation steps:
1. Create an endpoint to retrieve the profile for the authenticated user
2. Optionally implement an admin endpoint to retrieve any user's profile by user_id
3. Include appropriate error handling for missing profiles
4. Ensure proper authorization checks

Testing approach:
- Test profile retrieval for authenticated users
- Test error handling for users without profiles
- Test authorization (users should only be able to view their own profiles unless they have admin privileges)
- If applicable, test admin functionality for retrieving any profile

## 8. Implement Profile Update Endpoint [done]
### Dependencies: 2.6, 2.7
### Description: Create an endpoint to update profile information
### Details:
Implementation steps:
1. Create an endpoint that accepts updated profile information
2. Implement input validation for the updated data
3. Update the profile record in the database
4. Handle cases where a profile doesn't exist yet (create vs. update)
5. Return appropriate success/error responses

Testing approach:
- Test profile updates with valid and invalid inputs
- Verify database entries after successful updates
- Test authorization (users should only be able to update their own profiles)
- Test partial updates (updating only some fields)

## 9. Update Existing User Endpoints to Include Profile Information [done]
### Dependencies: 2.1, 2.7
### Description: Modify existing user-related endpoints to include profile information where appropriate
### Details:
Implementation steps:
1. Identify user endpoints that should include profile information (e.g., user details, user listing)
2. Update these endpoints to join with the Profile table and include relevant profile fields
3. Ensure proper handling of users without profiles
4. Update response schemas and documentation

Testing approach:
- Test modified endpoints to verify profile information is correctly included
- Test scenarios with users who don't have profiles
- Verify performance impact of including profile data
<info added on 2025-05-17T09:19:13.571Z>
Implementation steps:
1. Identify user endpoints that should include profile information (e.g., user details, user listing)
2. Update these endpoints to join with the Profile table and include relevant profile fields
3. Ensure proper handling of users without profiles
4. Update response schemas and documentation

Testing approach:
- Test modified endpoints to verify profile information is correctly included
- Test scenarios with users who don't have profiles
- Verify performance impact of including profile data

Detailed Implementation Plan:

1. Locate the `SessionController` at `app/features/session/session_controller.ts`
2. Update the `login` method:
   - After verifying credentials and before creating the token, load the profile relation with `await user.load('profile')`
   - Destructure core user fields: `id`, `username`, `email`, `createdAt`
   - Add a new `profile` field to the returned user object:
     - If profile exists: map properties (firstName, lastName, birthDate, profilePicture, countryCode)
     - If no profile: return null
   - Ensure proper serialization of birthDate field using `.toISODate()` if present

3. Update the `register` method:
   - After creating the new user, append a `profile: null` property to the response payload

4. Implementation considerations:
   - Maintain null-safety throughout implementation
   - No breaking changes to token generation
   - Preserve existing response structure, just adding the profile field
   - No new imports required for the `load` method

5. Testing updates:
   - Add/update unit tests to verify profile field presence in responses
   - Test both null and populated profile scenarios
   - Verify correct serialization of date fields
</info added on 2025-05-17T09:19:13.571Z>

## 10. Document Completed Authentication Components [done]
### Dependencies: None
### Description: Document the existing authentication components that are already implemented
### Details:
Documentation steps:
1. Document the existing User model structure
2. Document the authentication endpoints (registration, login)
3. Document the JWT token generation process
4. Document the refresh token mechanism
5. Document the authentication middleware
6. Update API documentation to reflect the complete authentication and profile system

Deliverable:
- Comprehensive documentation of the authentication system including both existing components and new profile functionality
<info added on 2025-05-17T10:20:13.393Z>
Documentation steps:
1. Document the existing User model structure
2. Document the authentication endpoints (registration, login)
3. Document the JWT token generation process
4. Document the refresh token mechanism
5. Document the authentication middleware
6. Update API documentation to reflect the complete authentication and profile system

Deliverable:
- Comprehensive documentation of the authentication system including both existing components and new profile functionality

Implementation Plan for documenting the authentication system:

1. **User Model Documentation**
   - File: `app/models/user.ts`
   - Document all columns: `id`, `username`, `email`, `passwordHash` (hashed), `createdAt`, `updatedAt`, `deletedAt` (soft delete).
   - Document relationships: `hasMany(refreshTokens)` and `hasOne(profile)`.
   - Include code snippets showing decorators and type declarations.

2. **Authentication Validators**
   - File: `app/features/session/auth_validator.ts`
   - Document `createUserValidator`, `loginValidator`, and `refreshTokenValidator` schemas: each field, rules, and error messages.
   - Provide example request bodies matching schemas.

3. **SessionController Endpoints**
   - File: `app/features/session/session_controller.ts`
   - Endpoints:
     • `POST /api/v1/auth/register`
       - Describe request validation (fields, types).
       - Describe response payload: `id`, `username`, `email`, `profile` (null).
     • `POST /api/v1/auth/login`
       - Describe request payload.
       - Describe response: `user` object (with loaded `profile`), `token`, `refreshToken`, `expiresIn`.
     • `POST /api/v1/auth/refresh`
       - Describe request payload (`refreshToken`, optional `rotateRefreshToken`).
       - Describe responses for both rotation and non-rotation cases.
     • `POST /api/v1/auth/logout`
       - Describe optional request body (`refreshToken`).
       - Describe response messages.

4. **RefreshToken Model**
   - File: `app/features/session/refresh_token_model.ts`
   - Document fields: `id`, `userId`, `token`, `revoked`, `expiresAt`, `createdAt`, `updatedAt`.
   - Document static methods: `createForUser`, `findValid`, `revokeAllForUser`; and instance method: `revoke`.
   - Show relationship to `User` via `belongsTo`.

5. **Authentication Middleware**
   - File: `app/middleware/auth_middleware.ts`
   - Document how `AuthMiddleware.handle` uses `ctx.auth.authenticateUsing`, fallback behavior for JSON vs HTML.

6. **Routes Configuration**
   - File: `start/routes.ts`
   - Document route groups and prefixes:
     - Public: `/auth/register`, `/auth/login`, `/auth/refresh`
     - Protected: `/auth/logout`, `/api/v1/profile`, `/api/v1/tasks`.
   - Include information on middleware used (`auth`, `throttle`).

7. **API Documentation Files**
   - Create or update `docs/authentication.md`:
     - Consolidate all above information in one markdown document.
     - Include code snippets, example requests/responses.
   - Create or update `docs/openapi.yaml` (or `openapi.json`):
     - Define OpenAPI spec for all auth endpoints with schemas referencing validators.
   - Update `README.md`:
     - Add an "Authentication" section summarizing endpoints and how to authenticate when calling the API.

**Deliverables:**
- `docs/authentication.md`
- Updated `docs/openapi.yaml`/`openapi.json`
- Updated `README.md` with Authentication section
- Examples of request/response JSON in docs
</info added on 2025-05-17T10:20:13.393Z>

