# Task ID: 19
# Title: Implement Unified Tagging System
# Status: pending
# Dependencies: 18, 11
# Priority: low
# Description: Create a comprehensive tagging system for all activity types, including tag models, relationships, and functionality for categorizing and organizing activities across the platform.
# Details:
This task involves implementing a complete tagging system that works across all activity types:

1. Create Tag model with the following fields:
   - id (UUID, primary key)
   - name (string, unique)
   - userId (foreign key to User, nullable for global tags)
   - createdAt, updatedAt timestamps
   - deletedAt (for soft deletes)

2. Create ActivityTag model with the following fields:
   - id (UUID, primary key)
   - activityType (string, indicates 'task' or 'event')
   - activityId (UUID, references the specific activity by type)
   - tagId (foreign key to Tag)
   - createdAt, updatedAt timestamps

3. Create MentionsToTask model with the following fields:
   - id (UUID, primary key)
   - activityType (string, indicates 'task' or 'event')
   - activityId (UUID, references the specific activity by type)
   - userId (foreign key to User)
   - createdAt, updatedAt timestamps
   - deletedAt (for soft deletes)

4. Create database migrations for:
   - tags table
   - activity_tags table (with polymorphic structure)
   - mentions_to_task table (with polymorphic structure)
   - Add appropriate indexes for performance optimization

5. Implement model relationships:
   - ActivityTag -> Tag (belongsTo)
   - Tag -> ActivityTag (hasMany)
   - Tag -> User (belongsTo)
   - User -> Tag (hasMany)
   - Task -> ActivityTag (hasMany, with polymorphic constraint)
   - Event -> ActivityTag (hasMany, with polymorphic constraint)
   - Task -> Tag (belongsToMany through ActivityTag with polymorphic constraint)
   - Event -> Tag (belongsToMany through ActivityTag with polymorphic constraint)
   - MentionsToTask -> User (belongsTo)
   - Task -> MentionsToTask (hasMany, with polymorphic constraint)
   - Event -> MentionsToTask (hasMany, with polymorphic constraint)
   - User -> MentionsToTask (hasMany)

6. Create TagService class for managing tag operations:
   - createTag(name, userId = null)
   - assignTagToActivity(tagId, activityType, activityId)
   - removeTagFromActivity(tagId, activityType, activityId)
   - searchTags(query, userId = null)
   - getTagSuggestions(activityType, activityId)
   - getPopularTags(userId = null, limit = 10)
   - Methods to handle both user-defined and global tags

7. Implement mention functionality:
   - createMention(activityType, activityId, userId)
   - removeMention(mentionId)
   - getActivityMentions(activityType, activityId)
   - getUserMentions(userId)

8. Create TagController with the following endpoints:
   - GET /api/tags - List tags (with filtering options)
   - POST /api/tags - Create new tag
   - GET /api/tags/{id} - Get tag details
   - PUT /api/tags/{id} - Update tag
   - DELETE /api/tags/{id} - Delete tag
   - GET /api/tags/search - Search tags
   - GET /api/tags/suggestions - Get tag suggestions
   - POST /api/{activityType}/{id}/tags - Assign tag to activity
   - DELETE /api/{activityType}/{id}/tags/{tagId} - Remove tag from activity
   - GET /api/{activityType}/{id}/tags - Get activity tags

9. Create MentionController with the following endpoints:
   - POST /api/{activityType}/{id}/mentions - Create mention
   - DELETE /api/{activityType}/{id}/mentions/{mentionId} - Remove mention
   - GET /api/{activityType}/{id}/mentions - Get activity mentions
   - GET /api/users/{id}/mentions - Get user mentions

10. Implement tag-based activity filtering:
    - Add tag filtering to TaskController and EventController
    - Support filtering activities by multiple tags
    - Implement proper authorization checks

11. Update Task and Event models to support tagging and mentions:
    - Add tag-related methods
    - Add mention-related methods
    - Update serialization to include tags and mentions

12. Implement proper authorization policies:
    - Ensure users can only manage their own tags
    - Allow viewing of global tags
    - Restrict mention operations based on visibility and permissions

13. Update API documentation to include tag and mention endpoints

Reference the updated ERD in docs/database_design/skedai_erd.dbml for the tag, activity_tag, and mentions_to_task tables, and docs/database_design/README.md for the Organization & Audit section.

# Test Strategy:
1. Unit Tests:
   - Test Tag model validation rules
   - Test ActivityTag model validation rules with polymorphic relationships
   - Test MentionsToTask model validation rules with polymorphic relationships
   - Test all relationships between models
   - Test TagService methods with various scenarios
   - Test authorization policies for tag operations

2. Integration Tests:
   - Test database migrations to ensure proper table creation
   - Test tag creation, updating, and deletion flows
   - Test assigning and removing tags from tasks and events
   - Test creating and removing mentions
   - Test tag search functionality with various queries
   - Test tag suggestions based on activity content
   - Test filtering tasks and events by tags

3. API Tests:
   - Test all TagController endpoints:
     * Verify proper response codes
     * Verify data validation
     * Test pagination and filtering
     * Test search functionality
   - Test all MentionController endpoints
   - Test tag-based filtering in TaskController and EventController

4. Authorization Tests:
   - Verify users can only manage their own tags
   - Verify global tags are accessible to all users
   - Verify proper authorization for mention operations
   - Test edge cases with deleted users/activities

5. Performance Tests:
   - Test tag search performance with large datasets
   - Test activity filtering by tags with large datasets
   - Verify proper indexing for performance optimization

6. UI Tests (if applicable):
   - Test tag input and autocomplete functionality
   - Test tag selection and filtering in task and event lists
   - Test mention functionality in task and event descriptions

7. Regression Tests:
   - Verify existing task and event functionality works with tags
   - Ensure backward compatibility with existing API consumers
   - Test polymorphic relationships work correctly across activity types
