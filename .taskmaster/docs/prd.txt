Skedai Backend Product Requirements Document (PRD)

1. Introduction
Skedai is a backend server for a comprehensive task management and event scheduling application. It aims to merge the functionalities of a traditional todo list app with appointment scheduling capabilities similar to Calendly. The primary goal is to empower users to organize their day or week using the timeboxing method, allocating specific durations to tasks for efficient planning.

2. Project Goals
- Deliver a robust backend solution for task management and scheduling.
- Enable users to manage tasks with detailed metadata (title, description, locations, people mentioned, tags, notes, due date, remind time).
- Support timeboxing for daily or weekly planning.
- Integrate AI for natural language task creation and information retrieval.
- Ensure scalability, maintainability, and security through clean architecture and best practices.

3. Technology Stack
- Backend Framework: AdonisJS (v6) with TypeScript
- Database: PostgreSQL
- ORM: AdonisJS Lucid
- LLM Integration: LangchainJS
- AI Provider: OpenAI API
- Testing: AdonisJS inbuilt testing suite (Japa)

4. Core Features and Modules
4.1 Foundation & Authentication
- Setup AdonisJS project structure.
- Implement User model with fields: user_id (UUID), username, email, password_hash, created_at, updated_at, deleted_at, profile_id.
- Implement Profile model with fields: profile_id, first_name, last_name, birth_date.
- User registration and login endpoints (email/password, potential OAuth later).
- Authentication middleware for securing routes.

4.2 Core Task Management
- Task model with fields: title, description, due_date, completed_status.
- CRUD API endpoints for tasks (Create, Read, Update, Delete).
- Task listing and retrieval for authenticated users.

4.3 Task Metadata Expansion
- Extend Task model to include sub-tasks (self-referencing), tags, notes, remind_time.
- Implement logic for parent-child task relationships.

4.4 Timeboxing & Scheduling
- Add duration and scheduled_start_time fields to Task model.
- Develop API endpoints for viewing tasks on a calendar/timeline to support timeboxing visualization.
- Future integration with external calendar APIs.

4.5 AI Integration - Natural Language Task Creation
- Setup LangchainJS and OpenAI integration.
- Develop a service to parse natural language input into structured task data (e.g., "Create a task 'Buy milk' for tomorrow afternoon").
- Integrate with a dedicated API endpoint.
- Focus on robust prompt engineering for accurate entity extraction.

4.6 AI Integration - Information Retrieval
- Develop services using LangchainJS/OpenAI to answer user queries about tasks (e.g., "What are my tasks for today?").
- Implement retrieval mechanisms using embeddings or structured data querying guided by the LLM.

4.7 Collaboration Features
- Implement task sharing/invitations via secure link or user accounts.
- Define permissions and access control for shared tasks.
- Add fields for assigned_users, mentioned_people, locations.

4.8 Pomodoro Timer
- Implement backend logic for starting, stopping, and tracking Pomodoro sessions associated with tasks.
- API endpoints for timer state management.

5. Architectural Principles
- Follow Clean Architecture with separation of concerns.
- Use feature-slicing architecture for modularity (e.g., separate folders for tasks, authentication).
- Design RESTful API endpoints adhering to conventions.
- Implement security measures: secure password hashing, authentication tokens, data validation.

6. Development Approach
- Iterative development with focus on delivering functional feature slices in each iteration.
- Test-Driven Development (TDD) for all new features.
- Comprehensive testing: unit tests for services, integration tests for API endpoints.
- Regular updates to project documentation (deliverables.md, findings.md).

7. Future Considerations
- Define Minimum Viable Product (MVP) for initial user feedback.
- Maintain OpenAPI (Swagger) specification for API documentation.
- Setup CI/CD pipeline for automated testing and deployment.
- Implement monitoring and structured logging for performance and error tracking.
- Configure separate environments (development, testing, staging, production).
- Regularly review security best practices and scalability needs.

This PRD serves as the foundation for generating initial tasks in Task Master to guide the development of the Skedai backend. 