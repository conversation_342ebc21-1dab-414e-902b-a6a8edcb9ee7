/*
|--------------------------------------------------------------------------
| Define HTTP limiters
|--------------------------------------------------------------------------
|
| The "limiter.define" method creates an HTTP middleware to apply rate
| limits on a route or a group of routes. Feel free to define as many
| throttle middleware as needed.
|
*/

import limiter from '@adonisjs/limiter/services/main'
import type { HttpContext } from '@adonisjs/core/http'
import { NextFn } from '@adonisjs/core/types/http'

// Define the throttle logic using built-in rate limiter
const throttleFn = limiter.define('global', () => {
  return limiter.allowRequests(10).every('1 minute')
})

/**
 * Middleware wrapper for the throttle function
 */
export default class ThrottleMiddleware {
  public async handle(ctx: HttpContext, next: NextFn) {
    return throttleFn(ctx, next)
  }
}
