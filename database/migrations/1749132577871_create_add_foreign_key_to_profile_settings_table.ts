import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'profile_settings'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .foreign('default_policy_template_id')
        .references('id')
        .inTable('privacy_policy_templates')
        .onDelete('SET NULL')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign(['default_policy_template_id'])
    })
  }
}
