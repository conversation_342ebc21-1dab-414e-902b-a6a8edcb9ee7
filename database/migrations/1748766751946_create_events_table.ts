import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'events'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key (UUID)
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Ownership - changed from profileId to userId
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE').notNullable()

      // Core activity fields (flattened from activities table)
      table.string('title').notNullable()
      table.text('description').nullable()
      table.timestamp('start_date').nullable()
      table.timestamp('end_date').nullable()
      table.jsonb('location').nullable()

      // Event-specific fields
      table.enum('status', ['active', 'cancelled', 'postponed', 'delayed']).defaultTo('active')
      table.integer('max_attendees').nullable()

      // Standard timestamps
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()
      table.timestamp('deleted_at').nullable() // For soft deletes
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('user_id', 'idx_events_user_id')
      table.index('status', 'idx_events_status')
      table.index('start_date', 'idx_events_start_date')
      table.index('end_date', 'idx_events_end_date')
      table.index('deleted_at', 'idx_events_deleted_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
