import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'policy_categories'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // profileId can be null for system-wide contexts
      table.uuid('user_id').nullable().references('id').inTable('users').onDelete('CASCADE')

      table.string('key').notNullable() // "work_hours", "personal_time", etc.
      table.string('label').notNullable() // "Work Hours", "Personal Time"
      table.text('description').nullable()
      table.jsonb('suggested_keywords').nullable() // ["meeting", "call", "presentation"]
      table.boolean('is_system_context').defaultTo(false)
      table.boolean('is_active').defaultTo(true)
      table.integer('display_order').defaultTo(0) // For UI ordering

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Unique constraint on user_id + key (but allow multiple null user_id for system contexts)
      table.unique(['user_id', 'key'])
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('user_id')
      table.index('key')
      table.index('is_system_context')
      table.index('is_active')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
