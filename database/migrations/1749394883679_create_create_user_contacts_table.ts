import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_contact'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('requester_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      table
        .uuid('addressee_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      table
        .enum('status', [
          'pending',
          'accepted',
          'declined',
          'blockedByRequester',
          'blockedByAddressee',
        ])
        .notNullable()
        .defaultTo('pending')

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Indexes for performance
      table.index(['requester_user_id'], 'idx_user_contact_requester_user_id')
      table.index(['addressee_user_id'], 'idx_user_contact_addressee_user_id')
      table.index(['status'], 'idx_user_contact_status')
      table.index(['deleted_at'], 'idx_user_contact_deleted_at')

      // Unique constraint to prevent duplicate contact requests
      table.unique(['requester_user_id', 'addressee_user_id'], 'unique_user_contact_pair')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
