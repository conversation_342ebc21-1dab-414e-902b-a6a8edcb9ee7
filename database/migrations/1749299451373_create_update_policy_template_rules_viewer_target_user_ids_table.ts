import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'policy_template_rules'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Drop the existing foreign key constraint and index
      table.dropForeign(['viewer_target_profile_id'])
      table.dropIndex(['viewer_target_profile_id'])

      // Rename the column
      table.renameColumn('viewer_target_profile_id', 'viewer_target_user_id')
    })

    // Add the new foreign key constraint and index in a separate alter
    this.schema.alterTable(this.tableName, (table) => {
      table.foreign('viewer_target_user_id').references('id').inTable('users').onDelete('CASCADE')

      table.index('viewer_target_user_id')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Drop the new foreign key constraint and index
      table.dropForeign(['viewer_target_user_id'])
      table.dropIndex(['viewer_target_user_id'])

      // Rename the column back
      table.renameColumn('viewer_target_user_id', 'viewer_target_profile_id')
    })

    // Add back the original foreign key constraint and index
    this.schema.alterTable(this.tableName, (table) => {
      table
        .foreign('viewer_target_profile_id')
        .references('id')
        .inTable('profiles')
        .onDelete('CASCADE')

      table.index('viewer_target_profile_id')
    })
  }
}
