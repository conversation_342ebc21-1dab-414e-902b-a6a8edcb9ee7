import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'policy_template_rules'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)
      table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')

      table
        .uuid('template_id')
        .notNullable()
        .references('id')
        .inTable('privacy_policy_templates')
        .onDelete('CASCADE')

      table
        .enum('viewer_scope_type', [
          'public',
          'all_contacts',
          'specific_contact',
          'specific_group',
          'specific_contact_list',
        ])
        .notNullable()

      // Nullable target references based on viewer_scope_type
      table
        .uuid('viewer_target_profile_id')
        .nullable()
        .references('id')
        .inTable('profiles')
        .onDelete('CASCADE')

      // TODO: Add these foreign key constraints when user_groups and user_contact_lists tables are created
      table.uuid('viewer_target_group_id').nullable()
      table.uuid('viewer_target_contact_list_id').nullable()

      table.boolean('blocks_scheduling').defaultTo(true) // Renamed from isAccountedFor
      table
        .enum('detail_visibility', ['hidden', 'busy_only', 'title_only', 'full_details'])
        .defaultTo('busy_only')
      table.text('custom_message').nullable()
      table.integer('priority').defaultTo(0) // For conflict resolution

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('template_id')
      table.index('viewer_scope_type')
      table.index('viewer_target_profile_id')
      table.index('viewer_target_group_id')
      table.index('viewer_target_contact_list_id')
      table.index('priority')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
