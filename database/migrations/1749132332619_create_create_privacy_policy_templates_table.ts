import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'privacy_policy_templates'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')

      table.string('name').notNullable() // "Work Privacy", "Family Friendly", "Personal Time"
      table.text('description').nullable()

      table
        .uuid('category_id')
        .nullable()
        .references('id')
        .inTable('policy_categories')
        .onDelete('SET NULL')

      table.boolean('is_default').defaultTo(false)
      table.boolean('is_system_template').defaultTo(false)

      // Default behaviors for this template
      table.boolean('blocks_scheduling').defaultTo(true) // Renamed from isAccountedFor
      table
        .enum('default_detail_visibility', ['hidden', 'busy_only', 'title_only', 'full_details'])
        .defaultTo('busy_only')
      table.text('default_custom_message').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('user_id')
      table.index('category_id')
      table.index('is_default')
      table.index('is_system_template')
      table.index('blocks_scheduling')
      table.index('deleted_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
