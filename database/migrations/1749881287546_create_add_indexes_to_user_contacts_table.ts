import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_contact'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add indexes for columns used in WHERE clauses and joins
      table.index(['requester_user_id'], 'idx_requester_user_id')
      table.index(['addressee_user_id'], 'idx_addressee_user_id')
      table.index(['status'], 'idx_status')

      // Consider a composite index if you frequently filter by requester/addressee AND status
      // table.index(['requester_user_id', 'status'], 'idx_requester_status')
      // table.index(['addressee_user_id', 'status'], 'idx_addressee_status')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['requester_user_id'], 'idx_requester_user_id')
      table.dropIndex(['addressee_user_id'], 'idx_addressee_user_id')
      table.dropIndex(['status'], 'idx_status')

      // Drop composite indexes if added
      // table.dropIndex(['requester_user_id', 'status'], 'idx_requester_status')
      // table.dropIndex(['addressee_user_id', 'status'], 'idx_addressee_status')
    })
  }
}
