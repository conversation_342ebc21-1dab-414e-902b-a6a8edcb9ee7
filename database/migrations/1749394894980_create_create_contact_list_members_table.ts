import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'contact_list_member'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('list_id')
        .notNullable()
        .references('id')
        .inTable('user_contact_list')
        .onDelete('CASCADE')

      table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())

      // Indexes for performance
      table.index(['list_id'], 'idx_contact_list_member_list_id')
      table.index(['user_id'], 'idx_contact_list_member_user_id')

      // Unique constraint to prevent duplicate members in the same list
      table.unique(['list_id', 'user_id'], 'unique_contact_list_member_per_list')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
