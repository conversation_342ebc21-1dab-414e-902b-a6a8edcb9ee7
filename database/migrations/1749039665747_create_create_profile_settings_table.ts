import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'profile_settings'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)
      table
        .uuid('profile_id')
        .notNullable()
        .unique()
        .references('id')
        .inTable('profiles')
        .onDelete('CASCADE')
      table.boolean('default_auto_accept_invitations').defaultTo(false)
      table.text('global_busy_message').nullable()
      table.uuid('default_policy_template_id').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('profile_id')
      table.index('default_policy_template_id')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
