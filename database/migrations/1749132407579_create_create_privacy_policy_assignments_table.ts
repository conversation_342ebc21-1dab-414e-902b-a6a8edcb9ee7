import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'privacy_policy_assignments'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Polymorphic relationship to tasks or events
      table.uuid('activity_id').notNullable() // References either task.id or event.id
      table.enum('activity_type', ['task', 'event']).notNullable() // Specifies which table

      table
        .uuid('template_id')
        .notNullable()
        .references('id')
        .inTable('privacy_policy_templates')
        .onDelete('CASCADE')

      // Override fields (optional, inherits from template if null)
      table.boolean('override_blocks_scheduling').nullable() // Renamed from overrideIsAccountedFor
      table
        .enum('override_detail_visibility', ['hidden', 'busy_only', 'title_only', 'full_details'])
        .nullable()
      table.text('override_custom_message').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())

      // Unique constraint to prevent duplicate assignments
      table.unique(['activity_id', 'activity_type', 'template_id'])
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index(['activity_id', 'activity_type'])
      table.index('template_id')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
