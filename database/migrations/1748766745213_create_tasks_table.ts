import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'tasks'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key (UUID)
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Ownership - changed from profileId to userId
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE').notNullable()

      // Core activity fields (flattened from activities table)
      table.string('title').notNullable()
      table.text('description').nullable()
      table.timestamp('start_date').nullable()
      table.timestamp('end_date').nullable()
      table.jsonb('location').nullable()

      // Task status with updated enum values
      table.enum('status', ['pending', 'in_progress', 'completed', 'deferred']).defaultTo('pending')

      // Task-specific fields
      table.string('priority').nullable()
      table.jsonb('remind_before').nullable()
      table.jsonb('tags').nullable()
      table.jsonb('people').nullable()
      table.jsonb('locations').nullable()
      table.jsonb('metadata').nullable()
      table.enum('entity_type', ['user_save', 'ai_generated']).defaultTo('user_save')
      table.uuid('parent_task_id').nullable()

      // Standard timestamps
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()
      table.timestamp('deleted_at').nullable() // For soft deletes
    })

    // Add foreign key constraint for parent tasks (self-referencing)
    this.schema.alterTable(this.tableName, (table) => {
      table.foreign('parent_task_id').references('id').inTable('tasks').onDelete('SET NULL')
    })

    // Add indexes for performance
    this.schema.alterTable(this.tableName, (table) => {
      table.index('user_id', 'idx_tasks_user_id')
      table.index('status', 'idx_tasks_status')
      table.index('start_date', 'idx_tasks_start_date')
      table.index('end_date', 'idx_tasks_end_date')
      table.index('parent_task_id', 'idx_tasks_parent_task_id')
      table.index('entity_type', 'idx_tasks_entity_type')
      table.index('deleted_at', 'idx_tasks_deleted_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
