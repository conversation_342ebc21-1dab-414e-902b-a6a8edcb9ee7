import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_contact_list'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('owner_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      table.string('name').notNullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Indexes for performance
      table.index(['owner_user_id'], 'idx_user_contact_list_owner_user_id')
      table.index(['name'], 'idx_user_contact_list_name')
      table.index(['deleted_at'], 'idx_user_contact_list_deleted_at')

      // Unique constraint to prevent duplicate list names per user
      table.unique(['owner_user_id', 'name'], 'unique_user_contact_list_name_per_owner')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
