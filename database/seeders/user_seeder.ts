import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import SessionService from '#features/session/session_service'
import { Profile } from '#features/profile/index'

export default class extends BaseSeeder {
  async run() {
    const sessionService = new SessionService()

    const testUsers = [
      {
        username: 'alice_smith',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        firstName: 'Alice',
        lastName: '<PERSON>',
        birthDate: DateTime.fromISO('1992-03-15'),
        countryCode: 'US',
      },
      {
        username: 'bob_johnson',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        birthDate: DateTime.fromISO('1988-07-22'),
        countryCode: 'CA',
      },
      {
        username: 'charlie_brown',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        birthDate: DateTime.fromISO('1995-11-08'),
        countryCode: 'UK',
      },
      {
        username: 'diana_wilson',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        firstName: 'Diana',
        lastName: 'Wilson',
        birthDate: DateTime.fromISO('1990-12-03'),
        countryCode: 'AU',
      },
    ]

    console.log('Creating test users with profiles...')

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await sessionService.checkUserExists(userData.email)
        if (existingUser) {
          console.log(`User ${userData.email} already exists, skipping...`)
          continue
        }

        // Use SessionService to register the user
        const { authUser, user } = await sessionService.registerUser({
          username: userData.username,
          email: userData.email,
          password: userData.password,
          password_confirmation: userData.password_confirmation,
        })

        // Create profile for the user
        const profile = await Profile.create({
          userId: user.id,
          firstName: userData.firstName,
          lastName: userData.lastName,
          birthDate: userData.birthDate,
          countryCode: userData.countryCode,
          profilePicture: null,
        })

        console.log(
          `✅ Created user: ${authUser.email} (ID: ${authUser.id}, User ID: ${user.id}, Profile ID: ${profile.id})`
        )
      } catch (error) {
        console.error(`❌ Failed to create user ${userData.email}:`, error.message)
      }
    }

    console.log('✅ User seeding with profiles completed!')
  }
}
