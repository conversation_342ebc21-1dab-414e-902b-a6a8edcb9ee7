import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import type { Authenticators } from '@adonisjs/auth/types'

/**
 * Auth middleware is used authenticate HTTP requests and deny
 * access to unauthenticated users.
 */
export default class AuthMiddleware {
  /**
   * The URL to redirect to, when authentication fails
   */
  redirectTo = '/login'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    // For API requests, return JSON response instead of redirecting
    try {
      await ctx.auth.authenticateUsing(options.guards || ['api'], {
        loginRoute: this.redirectTo,
      })
      if (ctx.auth.user) {
        await ctx.auth.user.getUserId()
      }
      return next()
    } catch (error) {
      if (ctx.request.accepts(['html', 'json']) === 'json') {
        return ctx.response.unauthorized({ error: 'Authentication required' })
      }

      return ctx.response.redirect(this.redirectTo)
    }
  }
}
