import vine from '@vinejs/vine'

export interface QueryValidatorOptions {
  statusOptions: string[]
  sortFields: string[]
  additionalFields?: Record<string, any>
  maxLimit?: number
}

/**
 * Factory function to create consistent query validators across all endpoints
 */
export const createQueryValidator = ({
  statusOptions,
  sortFields,
  additionalFields = {},
  maxLimit = 100,
}: QueryValidatorOptions) => {
  return vine.compile(
    vine.object({
      // Common filter fields
      status: vine.string().in(statusOptions).optional(),
      search: vine.string().optional(),
      startDate: vine.string().optional(),
      endDate: vine.string().optional(),

      // Sorting parameters
      sortBy: vine.string().in(sortFields).optional(),
      sortDirection: vine.string().in(['asc', 'desc']).optional(),

      // Pagination parameters
      page: vine.number().min(1).optional(),
      limit: vine.number().min(1).max(maxLimit).optional(),

      // Additional custom fields for specific endpoints
      ...additionalFields,
    })
  )
}

/**
 * Common base fields for most query validators
 */
export const baseQueryFields = {
  search: vine.string().optional(),
  startDate: vine.string().optional(),
  endDate: vine.string().optional(),
  sortBy: vine.string().optional(),
  sortDirection: vine.string().in(['asc', 'desc']).optional(),
  page: vine.number().min(1).optional(),
  limit: vine.number().min(1).max(100).optional(),
}
