import type { Pagination } from '#types/pagination_types'

export interface SuccessResponse<T> {
  status: 'success'
  message?: string
  data?: T
  pagination?: Pagination
}

export function createSuccessResponse<T>({
  data,
  message,
  pagination,
}: {
  data?: T
  message?: string
  pagination?: Pagination
}): SuccessResponse<T> {
  const response: SuccessResponse<T> = {
    status: 'success',
    data,
    pagination,
  }
  if (message) {
    response.message = message
  }
  return response
}
