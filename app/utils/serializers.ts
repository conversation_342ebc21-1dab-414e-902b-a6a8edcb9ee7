import { DateTime } from 'luxon'

/**
 * Serializer for date-time fields
 */
export const dateTimeColumn = {
  prepare: (value: DateTime | string | null) =>
    value
      ? typeof value === 'string'
        ? DateTime.fromISO(value).toUTC().toSQL()
        : value.toUTC().toSQL()
      : null,
  consume: (value: string | null) => (value ? DateTime.fromSQL(value).toUTC() : null),
}

/**
 * Serializer for array fields stored as JSON
 */
export const jsonArrayColumn = {
  prepare: (value: string[] | null) => (value ? JSON.stringify(value) : null),
}
