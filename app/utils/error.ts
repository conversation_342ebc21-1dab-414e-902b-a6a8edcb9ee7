import { Exception } from '@adonisjs/core/exceptions'

export enum ErrorCodes {
  NoValidDataProvided = 'E_NO_VALID_DATA_PROVIDED',
  Unauthorized = 'E_UNAUTHORIZED',
  NotFound = 'E_NOT_FOUND',
  ValidationFailure = 'E_VALIDATION_FAILURE',
  BadRequest = 'E_BAD_REQUEST',
  // Add other future error codes here
}

export function parseException(exception: Exception) {
  return {
    status: exception.status,
    code: exception.code,
    message: exception.message,
  }
}

/**
 * Creates an exception for when validation results in no data from a non-empty request
 */
export function noDataInputException(message?: string): Exception {
  const defaultMessage =
    'No valid fields provided. Ensure field names are correct and at least one valid field is present.'
  const exception = new Exception(message || defaultMessage, {
    status: 422,
    code: ErrorCodes.NoValidDataProvided,
  })
  return exception
}

/**
 * Creates an exception for authorization failures
 */
export function authorizationException(message?: string): Exception {
  const exception = new Exception(message || 'You are not authorized to perform this action', {
    status: 403,
    code: ErrorCodes.Unauthorized,
  })

  return exception
}

/**
 * Creates an exception for not found resources.
 * Now accepts an optional message and has a default.
 */
export function notFoundException(message?: string): Exception {
  const defaultMessage = 'The requested resource was not found.'
  const exception = new Exception(message || defaultMessage, {
    status: 404,
    code: ErrorCodes.NotFound,
  })
  return exception
}

/**
 * Creates an exception for validation failures
 */
export function validationException(message: string): Exception {
  const exception = new Exception(message, {
    status: 422,
    code: ErrorCodes.ValidationFailure,
  })

  return exception
}

/**
 * Creates an exception for general bad requests
 */
export function badRequestException(message: string): Exception {
  const exception = new Exception(message, {
    status: 400,
    code: ErrorCodes.BadRequest,
  })

  return exception
}
