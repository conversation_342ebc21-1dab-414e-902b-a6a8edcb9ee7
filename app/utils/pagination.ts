import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type { PaginatedResponse, PaginationMeta } from '#types/base_query_types'

/**
 * Generic pagination utility for Lucid queries
 */
export class PaginationHelper {
  /**
   * Apply pagination to a Lucid query and return standardized response
   */
  static async paginate<T>(
    query: ModelQueryBuilderContract<any>,
    options: {
      page?: number
      limit?: number
      paginate?: boolean
    } = {}
  ): Promise<PaginatedResponse<T>> {
    const { page = 1, limit = 20, paginate = true } = options

    if (!paginate) {
      const data = await query
      return {
        data: data as T[],
        pagination: undefined,
      }
    }

    const paginatedResult = await query.paginate(page, limit)
    const data = paginatedResult.all()

    return {
      data: data as T[],
      pagination: {
        currentPage: paginatedResult.currentPage,
        totalPages: paginatedResult.lastPage,
        totalCount: paginatedResult.total,
        hasNextPage: paginatedResult.hasMorePages,
        hasPreviousPage: paginatedResult.currentPage > 1,
      },
    }
  }

  /**
   * Create empty pagination response
   */
  static emptyResponse<T>(): PaginatedResponse<T> {
    return {
      data: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    }
  }

  /**
   * Extract pagination parameters from query params
   */
  static extractPaginationParams(queryParams: any): {
    page: number
    limit: number
    paginate: boolean
  } {
    return {
      page: queryParams?.pagination?.page || 1,
      limit: queryParams?.pagination?.limit || 20,
      paginate: queryParams?.paginate !== false,
    }
  }
}
