/**
 * Enum definitions for the Policy Template System
 * These enums control availability scope and activity detail visibility
 */

/**
 * Defines the scope of availability/visibility for profiles, activities, and policies
 */
export enum AvailabilityScopeType {
  PUBLIC = 'public',
  ALL_CONTACTS = 'all_contacts',
  SPECIFIC_CONTACT = 'specific_contact',
  SPECIFIC_GROUP = 'specific_group',
  SPECIFIC_CONTACT_LIST = 'specific_contact_list',
}

/**
 * Defines levels of activity detail visibility for different viewers
 */
export enum ActivityDetailVisibilityLevel {
  HIDDEN = 'hidden', // Activity completely hidden from viewer
  BUSY_ONLY = 'busy_only', // Show as "Busy" with no details
  TITLE_ONLY = 'title_only', // Show activity title but no description
  FULL_DETAILS = 'full_details', // Show all activity details
}

/**
 * Type guards for enum validation
 */
export const isValidAvailabilityScopeType = (value: any): value is AvailabilityScopeType => {
  return Object.values(AvailabilityScopeType).includes(value)
}

export const isValidActivityDetailVisibilityLevel = (
  value: any
): value is ActivityDetailVisibilityLevel => {
  return Object.values(ActivityDetailVisibilityLevel).includes(value)
}

/**
 * Helper arrays for validation in other parts of the application
 */
export const AVAILABILITY_SCOPE_TYPES = Object.values(AvailabilityScopeType)
export const ACTIVITY_DETAIL_VISIBILITY_LEVELS = Object.values(ActivityDetailVisibilityLevel)
