/**
 * Group Management System Types
 *
 * Defines enums and types for the group management system
 * including group member roles, invitation types, and related type definitions.
 */

/**
 * Role enum for group members
 * Maps to GroupMemberRole enum in the ERD
 */
export enum GroupMemberRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
}

/**
 * Type enum for group invitation requests
 * Maps to GroupInvitationRequestType enum in the ERD
 */
export enum GroupInvitationRequestType {
  INVITATION = 'invitation', // Group admin/owner invites a user
  REQUEST = 'request', // User requests to join a group
}

/**
 * Status enum for group invitation requests
 */
export enum GroupInvitationRequestStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
}

/**
 * Type guards to check if values are valid enums
 */
export const isGroupMemberRole = (value: string): value is GroupMemberRole => {
  return Object.values(GroupMemberRole).includes(value as GroupMemberRole)
}

export const isGroupInvitationRequestType = (value: string): value is GroupInvitationRequestType => {
  return Object.values(GroupInvitationRequestType).includes(value as GroupInvitationRequestType)
}

export const isGroupInvitationRequestStatus = (value: string): value is GroupInvitationRequestStatus => {
  return Object.values(GroupInvitationRequestStatus).includes(value as GroupInvitationRequestStatus)
}

/**
 * Helper arrays containing all enum values for validation
 */
export const GROUP_MEMBER_ROLE_VALUES = Object.values(GroupMemberRole)
export const GROUP_INVITATION_REQUEST_TYPE_VALUES = Object.values(GroupInvitationRequestType)
export const GROUP_INVITATION_REQUEST_STATUS_VALUES = Object.values(GroupInvitationRequestStatus)

/**
 * Interface for group creation
 */
export interface CreateGroupData {
  name: string
  description?: string
}

/**
 * Interface for updating group details
 */
export interface UpdateGroupData {
  name?: string
  description?: string
}

/**
 * Interface for adding a member to a group
 */
export interface AddGroupMemberData {
  userId: string
  role: GroupMemberRole
}

/**
 * Interface for updating a member's role
 */
export interface UpdateGroupMemberData {
  role: GroupMemberRole
}

/**
 * Interface for creating a group invitation
 */
export interface CreateGroupInvitationData {
  userId: string
  message?: string
}

/**
 * Interface for creating a join request
 */
export interface CreateJoinRequestData {
  message?: string
}

/**
 * Interface for responding to invitations/requests
 */
export interface RespondToInvitationData {
  status: GroupInvitationRequestStatus.ACCEPTED | GroupInvitationRequestStatus.DECLINED
}

// ===== Extended Types for Group Querying and Pagination =====

import {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  GetResourcesOptions,
} from '#types/base_query_types'

/**
 * Filters available when querying groups
 */
export interface GroupFilters extends BaseFilters {
  ownerId?: string
  memberUserId?: string
  role?: GroupMemberRole
}

/**
 * Sort fields for groups
 */
export interface GroupSort extends BaseSort {
  field: 'createdAt' | 'updatedAt' | 'name'
}

/**
 * Filters for group members
 */
export interface GroupMemberFilters extends BaseFilters {
  role?: GroupMemberRole
}

/**
 * Sort fields for group members
 */
export interface GroupMemberSort extends BaseSort {
  field: 'createdAt' | 'updatedAt' | 'role'
}

/**
 * Filters for group invitations/requests
 */
export interface GroupInvitationFilters extends BaseFilters {
  type?: GroupInvitationRequestType
  status?: GroupInvitationRequestStatus
  userId?: string
  initiatorUserId?: string
}

/**
 * Sort fields for group invitations/requests
 */
export interface GroupInvitationSort extends BaseSort {
  field: 'createdAt' | 'updatedAt' | 'status' | 'type'
}

export type GroupQueryParams = BaseQueryParams<GroupFilters, GroupSort>
export type GroupMemberQueryParams = BaseQueryParams<GroupMemberFilters, GroupMemberSort>
export type GroupInvitationQueryParams = BaseQueryParams<GroupInvitationFilters, GroupInvitationSort>

export type GetGroupsOptions = GetResourcesOptions<GroupFilters, GroupSort>
export type GetGroupMembersOptions = GetResourcesOptions<GroupMemberFilters, GroupMemberSort>
export type GetGroupInvitationsOptions = GetResourcesOptions<GroupInvitationFilters, GroupInvitationSort>