/**
 * Defines a union of common model field names that are typically not directly updatable
 * via patch or update operations (e.g., primary keys, foreign keys managed by context,
 * and automatically managed timestamps).
 */
export type NonPatchableModelFields =
  | 'id' // Primary key
  | 'userId' // Often a foreign key managed by auth context, not direct patch
  | 'createdAt' // Auto-created timestamp
  | 'deletedAt' // Managed by soft delete operations
