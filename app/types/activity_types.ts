export enum ActivityType {
  TASK = 'task',
  EVENT = 'event',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  DEFERRED = 'deferred',
}

export enum EventStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  POSTPONED = 'postponed',
  DELAYED = 'delayed',
}

export enum TaskEntityType {
  USER_SAVE = 'user_save',
  AI_GENERATED = 'ai_generated',
}
