// Base interfaces for query parameters, pagination, and filtering

export interface BaseFilters {
  search?: string
  startDate?: string
  endDate?: string
}

export interface BaseSort {
  field: string
  direction: 'asc' | 'desc'
}

export interface BasePagination {
  page: number
  limit: number
}

export interface BaseQueryParams<
  TFilters extends BaseFilters = BaseFilters,
  TSort extends BaseSort = BaseSort,
> {
  filters?: TFilters
  sort?: TSort
  pagination?: BasePagination
}

export interface PaginationMeta {
  totalCount: number
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage?: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination?: PaginationMeta
}

export interface GetResourcesOptions<
  TFilters extends BaseFilters = BaseFilters,
  TSort extends BaseSort = BaseSort,
> {
  queryParams?: BaseQueryParams<TFilters, TSort>
  paginate?: boolean
}

// Generic resource interface for ownership validation
export interface ResourceWithUser {
  userId: string
  [key: string]: any
}
