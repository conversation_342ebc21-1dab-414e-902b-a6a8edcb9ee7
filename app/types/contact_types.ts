/**
 * Contact Management System Types
 *
 * Defines enums and types for the contact management system
 * including contact request statuses and related type definitions.
 */

/**
 * Status enum for contact requests
 * Maps to ContactRequestStatus enum in the ERD
 */
export enum ContactRequestStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  BLOCKED_BY_REQUESTER = 'blockedByRequester',
  BLOCKED_BY_ADDRESSEE = 'blockedByAddressee',
}

/**
 * Type guard to check if a value is a valid ContactRequestStatus
 */
export const isContactRequestStatus = (value: string): value is ContactRequestStatus => {
  return Object.values(ContactRequestStatus).includes(value as ContactRequestStatus)
}

/**
 * Helper array containing all ContactRequestStatus values for validation
 */
export const CONTACT_REQUEST_STATUS_VALUES = Object.values(ContactRequestStatus)

/**
 * Interface for contact request creation
 */
export interface CreateContactRequestData {
  addresseeUserId: string
}

/**
 * Interface for updating contact request status
 */
export interface UpdateContactRequestData {
  status: ContactRequestStatus
}

/**
 * Interface for contact list creation
 */
export interface CreateContactListData {
  name: string
}

/**
 * Interface for updating contact list
 */
export interface UpdateContactListData {
  name: string
}

/**
 * Interface for adding member to contact list
 */
export interface AddContactListMemberData {
  userId: string
}

// ===== Extended Types for Contact Querying and Pagination =====

import {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  GetResourcesOptions,
} from '#types/base_query_types'

/**
 * Filters available when querying user contacts
 */
export interface ContactFilters extends BaseFilters {
  status?: ContactRequestStatus
  includeRequests?: boolean
  includeReceived?: boolean
}

/**
 * Sort fields for contacts. Currently limited but can be extended.
 */
export interface ContactSort extends BaseSort {
  field: 'createdAt' | 'updatedAt' | 'status'
}

export type ContactQueryParams = BaseQueryParams<ContactFilters, ContactSort>

export type GetContactsOptions = GetResourcesOptions<ContactFilters, ContactSort>
