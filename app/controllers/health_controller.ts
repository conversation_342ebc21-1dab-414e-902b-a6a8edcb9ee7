import type { HttpContext } from '@adonisjs/core/http'
import { checkDatabaseConnection } from '#utils/database'

export default class HealthController {
  public async check({ response }: HttpContext) {
    const isDatabaseConnected = await checkDatabaseConnection()
    if (isDatabaseConnected) {
      return response.status(200).json({ status: 'OK', message: 'Database connection is healthy' })
    } else {
      return response.status(503).json({ status: 'Error', message: 'Database connection failed' })
    }
  }
}
