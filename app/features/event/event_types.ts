import {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  GetResourcesOptions,
} from '#types/base_query_types'

export interface EventFilters extends BaseFilters {
  status?: 'active' | 'cancelled' | 'postponed' | 'delayed'
  maxAttendees?: number
}

export interface EventSort extends BaseSort {
  field: 'startDate' | 'endDate' | 'createdAt' | 'updatedAt' | 'title' | 'status'
}

export type EventQueryParams = BaseQueryParams<EventFilters, EventSort>

export type GetEventsOptions = GetResourcesOptions<EventFilters, EventSort>
