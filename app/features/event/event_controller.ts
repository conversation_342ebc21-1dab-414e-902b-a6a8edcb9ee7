import { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import { EventStatus } from '#types/activity_types'
import EventService from '#features/event/event_service'
import { eventCreateValidator } from '#features/event/event_validator'

export default class EventController {
  private eventService = new EventService()

  /**
   * Validate UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(uuid)
  }

  /**
   * Create a new event
   */
  async store({ request, response, auth }: HttpContext) {
    try {
      const user = auth.user!
      const userId = await user.getUserId()

      if (!userId) {
        return response.unauthorized({ error: 'User not found' })
      }

      const payload = await request.validateUsing(eventCreateValidator)
      const { title, description, location, startDate, endDate, status, maxAttendees } = payload

      const event = await this.eventService.createEvent({
        userId,
        title,
        description,
        location,
        startDate: startDate ? DateTime.fromISO(startDate) : undefined,
        endDate: endDate ? DateTime.fromISO(endDate) : undefined,
        status: (status as EventStatus) || EventStatus.ACTIVE,
        maxAttendees: maxAttendees ? Number(maxAttendees) : undefined,
      })

      return response.created({
        status: 'success',
        data: {
          id: event.id,
          title: event.title,
          description: event.description,
          location: event.location,
          startDate: event.startDate,
          endDate: event.endDate,
          status: event.status,
          maxAttendees: event.maxAttendees,
          createdAt: event.createdAt,
          updatedAt: event.updatedAt,
        },
      })
    } catch (error) {
      console.error('Error creating event:', error)
      // Handle validation errors properly
      if (error.code === 'E_VALIDATION_ERROR') {
        return response.unprocessableEntity({ 
          error: 'Validation failed',
          messages: error.messages 
        })
      }
      return response.internalServerError({ error: 'Failed to create event' })
    }
  }

  /**
   * Get a specific event by ID
   */
  async show({ params, response, auth }: HttpContext) {
    try {
      const user = auth.user!
      const userId = await user.getUserId()

      if (!userId) {
        return response.unauthorized({ error: 'User not found' })
      }

      const eventId = params.id

      if (!eventId || typeof eventId !== 'string' || !this.isValidUUID(eventId)) {
        return response.badRequest({ error: 'Invalid event ID' })
      }

      const event = await this.eventService.getEventById(eventId)

      if (!event) {
        return response.notFound({ error: 'Event not found' })
      }

      // Check ownership
      if (event.userId !== userId) {
        return response.forbidden({ error: 'Access denied' })
      }

      return response.ok({
        status: 'success',
        data: {
          id: event.id,
          title: event.title,
          description: event.description,
          location: event.location,
          startDate: event.startDate,
          endDate: event.endDate,
          status: event.status,
          maxAttendees: event.maxAttendees,
          createdAt: event.createdAt,
          updatedAt: event.updatedAt,
        },
      })
    } catch (error) {
      console.error('Error fetching event:', error)
      return response.internalServerError({ error: 'Failed to fetch event' })
    }
  }

  /**
   * List all events for the authenticated user
   */
  async index({ request, response, auth }: HttpContext) {
    try {
      const user = auth.user!
      const userId = await user.getUserId()

      if (!userId) {
        return response.unauthorized({ error: 'User not found' })
      }

      const {
        status,
        search,
        startDate,
        endDate,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortDirection = 'desc',
      } = request.qs()

      const result = await this.eventService.getEventsByUserId(userId, {
        status,
        search,
        startDate,
        endDate,
        page: Number.parseInt(page),
        limit: Number.parseInt(limit),
        sortBy,
        sortDirection,
      })

      // Transform events for response
      const transformedEvents = result.data.map((event) => ({
        id: event.id,
        title: event.title,
        description: event.description,
        location: event.location,
        startDate: event.startDate,
        endDate: event.endDate,
        status: event.status,
        maxAttendees: event.maxAttendees,
        createdAt: event.createdAt,
        updatedAt: event.updatedAt,
      }))

      return response.ok({
        status: 'success',
        data: {
          data: transformedEvents,
          pagination: result.pagination,
        },
      })
    } catch (error) {
      console.error('Error fetching events:', error)
      return response.internalServerError({ error: 'Failed to fetch events' })
    }
  }

  /**
   * Update an event
   */
  async update({ params, request, response, auth }: HttpContext) {
    try {
      const user = auth.user!
      const userId = await user.getUserId()

      if (!userId) {
        return response.unauthorized({ error: 'User not found' })
      }

      const eventId = params.id

      if (!eventId || typeof eventId !== 'string' || !this.isValidUUID(eventId)) {
        return response.badRequest({ error: 'Invalid event ID' })
      }

      const { title, description, location, startDate, endDate, status, maxAttendees } =
        request.body()

      const event = await this.eventService.updateEvent(eventId, userId, {
        title,
        description,
        location,
        startDate: startDate ? DateTime.fromISO(startDate) : undefined,
        endDate: endDate ? DateTime.fromISO(endDate) : undefined,
        status,
        maxAttendees: maxAttendees ? Number(maxAttendees) : undefined,
      })

      if (!event) {
        return response.notFound({ error: 'Event not found or access denied' })
      }

      return response.ok({
        status: 'success',
        data: {
          id: event.id,
          title: event.title,
          description: event.description,
          location: event.location,
          startDate: event.startDate,
          endDate: event.endDate,
          status: event.status,
          maxAttendees: event.maxAttendees,
          createdAt: event.createdAt,
          updatedAt: event.updatedAt,
        },
      })
    } catch (error) {
      console.error('Error updating event:', error)
      return response.internalServerError({ error: 'Failed to update event' })
    }
  }

  /**
   * Delete an event (soft delete)
   */
  async destroy({ params, response, auth }: HttpContext) {
    try {
      const user = auth.user!
      const userId = await user.getUserId()

      if (!userId) {
        return response.unauthorized({ error: 'User not found' })
      }

      const eventId = params.id

      if (!eventId || typeof eventId !== 'string' || !this.isValidUUID(eventId)) {
        return response.badRequest({ error: 'Invalid event ID' })
      }

      const deleted = await this.eventService.deleteEvent(eventId, userId)

      if (!deleted) {
        return response.notFound({ error: 'Event not found or access denied' })
      }

      return response.ok({
        status: 'success',
        message: 'Event deleted successfully',
      })
    } catch (error) {
      console.error('Error deleting event:', error)
      return response.internalServerError({ error: 'Failed to delete event' })
    }
  }
}
