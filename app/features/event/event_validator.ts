import vine from '@vinejs/vine'

// Validator for creating events with flattened structure
export const eventCreateValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255),
    description: vine.string().trim().maxLength(1000).optional(),
    location: vine.any().optional(), // Can be an object with address, coordinates, etc.
    startDate: vine.string().optional(), // ISO date string
    endDate: vine.string().optional(), // ISO date string
    status: vine.string().in(['active', 'cancelled', 'postponed', 'delayed']).optional(),
    maxAttendees: vine.number().min(1).optional(),
  })
)

// Validator for updating events with flattened structure
export const eventUpdateValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255).optional(),
    description: vine.string().trim().maxLength(1000).optional(),
    location: vine.any().optional(),
    startDate: vine.string().optional(),
    endDate: vine.string().optional(),
    status: vine.string().in(['active', 'cancelled', 'postponed', 'delayed']).optional(),
    maxAttendees: vine.number().min(1).optional(),
  })
)

import { createQueryValidator } from '#validators/base_validators'

// Validator for querying events
export const eventQueryValidator = createQueryValidator({
  statusOptions: ['active', 'cancelled', 'postponed', 'delayed'],
  sortFields: ['startDate', 'endDate', 'createdAt', 'updatedAt', 'title', 'status'],
  additionalFields: {
    maxAttendees: vine.number().min(1).optional(),
  },
})
