import Event from '#features/event/event_model'
import { DateTime } from 'luxon'
import { EventStatus } from '#types/activity_types'

export default class EventService {
  /**
   * Create a new event with flattened structure
   */
  async createEvent(data: {
    userId: string
    title: string
    description?: string
    location?: object
    startDate?: DateTime
    endDate?: DateTime
    status?: EventStatus
    maxAttendees?: number
  }): Promise<Event> {
    // Create Event record directly with all fields (flattened structure)
    const event = await Event.create({
      userId: data.userId,
      title: data.title,
      description: data.description,
      location: data.location,
      startDate: data.startDate,
      endDate: data.endDate,
      status: data.status || EventStatus.ACTIVE,
      maxAttendees: data.maxAttendees || null,
    })

    return event
  }

  /**
   * Get event by ID (using primary key)
   */
  async getEventById(eventId: string): Promise<Event | null> {
    const event = await Event.query().where('id', eventId).first()
    return event
  }

  /**
   * Get events for a specific user with filtering and pagination
   */
  async getEventsByUserId(
    userId: string,
    filters: {
      status?: string
      search?: string
      startDate?: string
      endDate?: string
      page?: number
      limit?: number
      sortBy?: string
      sortDirection?: 'asc' | 'desc'
    } = {}
  ): Promise<{
    data: Event[]
    pagination: {
      totalCount: number
      currentPage: number
      totalPages: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
  }> {
    const {
      status,
      search,
      startDate,
      endDate,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'desc',
    } = filters

    let query = Event.query().where('user_id', userId).whereNull('deleted_at')

    // Apply status filter
    if (status) {
      query = query.where('status', status)
    }

    // Apply search filter on title and description
    if (search) {
      query = query.where((subQuery) => {
        subQuery.whereILike('title', `%${search}%`).orWhereILike('description', `%${search}%`)
      })
    }

    // Apply date range filters
    if (startDate) {
      const startDateTime = DateTime.fromISO(startDate).toSQL()
      if (startDateTime) {
        query = query.where('start_date', '>=', startDateTime)
      }
    }

    if (endDate) {
      const endDateTime = DateTime.fromISO(endDate).toSQL()
      if (endDateTime) {
        query = query.where('end_date', '<=', endDateTime)
      }
    }

    // Apply sorting
    switch (sortBy) {
      case 'title':
        query = query.orderBy('title', sortDirection)
        break
      case 'startDate':
        query = query.orderBy('start_date', sortDirection)
        break
      case 'endDate':
        query = query.orderBy('end_date', sortDirection)
        break
      case 'status':
        query = query.orderBy('status', sortDirection)
        break
      case 'createdAt':
      default:
        query = query.orderBy('created_at', sortDirection)
        break
    }

    // Get total count before pagination (without ordering for count)
    const countQuery = Event.query().where('user_id', userId).whereNull('deleted_at')
    
    // Apply the same filters to count query
    if (status) {
      countQuery.where('status', status)
    }
    
    if (search) {
      countQuery.where((subQuery) => {
        subQuery.whereILike('title', `%${search}%`).orWhereILike('description', `%${search}%`)
      })
    }
    
    if (startDate) {
      const startDateTime = DateTime.fromISO(startDate).toSQL()
      if (startDateTime) {
        countQuery.where('start_date', '>=', startDateTime)
      }
    }
    
    if (endDate) {
      const endDateTime = DateTime.fromISO(endDate).toSQL()
      if (endDateTime) {
        countQuery.where('end_date', '<=', endDateTime)
      }
    }
    
    const totalCount = await countQuery.count('* as total')
    const total = Array.isArray(totalCount)
      ? Number(totalCount[0].$extras.total)
      : Number(totalCount)

    // Apply pagination
    const events = await query.offset((page - 1) * limit).limit(limit)

    const totalPages = Math.ceil(total / limit)

    return {
      data: events,
      pagination: {
        totalCount: total,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    }
  }

  /**
   * Update an event
   */
  async updateEvent(
    eventId: string,
    userId: string,
    data: {
      title?: string
      description?: string
      location?: object
      startDate?: DateTime
      endDate?: DateTime
      status?: EventStatus
      maxAttendees?: number
    }
  ): Promise<Event | null> {
    const event = await this.getEventById(eventId)

    if (!event || event.userId !== userId) {
      return null
    }

    // Update Event fields directly
    const updates: any = {}
    if (data.title !== undefined) updates.title = data.title
    if (data.description !== undefined) updates.description = data.description
    if (data.location !== undefined) updates.location = data.location
    if (data.startDate !== undefined) updates.startDate = data.startDate
    if (data.endDate !== undefined) updates.endDate = data.endDate
    if (data.status !== undefined) updates.status = data.status
    if (data.maxAttendees !== undefined) updates.maxAttendees = data.maxAttendees

    if (Object.keys(updates).length > 0) {
      await event.merge(updates).save()
    }

    return event
  }

  /**
   * Delete an event (soft delete)
   */
  async deleteEvent(eventId: string, userId: string): Promise<boolean> {
    const event = await this.getEventById(eventId)

    if (!event || event.userId !== userId) {
      return false
    }

    // Soft delete using the Event model's soft delete capability
    await event.delete()
    return true
  }

  /**
   * Validate event ownership for authorization
   */
  async validateEventOwnership(eventId: string, userId: string): Promise<boolean> {
    const event = await Event.query()
      .where('id', eventId)
      .where('user_id', userId)
      .whereNull('deleted_at')
      .first()

    return !!event
  }
}
