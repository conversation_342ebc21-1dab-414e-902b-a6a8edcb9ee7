import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Event Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  // Helper function to create a test event
  async function createTestEvent(client: ApiClient, authToken: string, eventData = {}) {
    const defaultEventData = {
      title: 'Test Event',
      description: 'Test event description',
      location: { address: 'Test Location' },
      startDate: '2024-12-01T10:00:00.000Z',
      endDate: '2024-12-01T12:00:00.000Z',
      status: 'active',
      maxAttendees: 10,
      ...eventData,
    }

    const response = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json(defaultEventData)

    return { response, eventData: defaultEventData }
  }

  test('user can create event', async ({ client, assert }) => {
    const { authToken } = await createTestUser('eventuser')

    const eventData = {
      title: 'Team Meeting',
      description: 'Weekly team sync meeting',
      location: { address: '123 Main St, Conference Room A' },
      startDate: '2024-12-01T10:00:00.000Z',
      endDate: '2024-12-01T11:00:00.000Z',
      status: 'active',
      maxAttendees: 5,
    }

    const response = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json(eventData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.title, eventData.title)
    assert.equal(body.data.description, eventData.description)
    assert.deepEqual(body.data.location, eventData.location)
    assert.equal(body.data.status, eventData.status)
    assert.equal(body.data.maxAttendees, eventData.maxAttendees)
    assert.exists(body.data.id)
    assert.exists(body.data.createdAt)
    assert.exists(body.data.updatedAt)
  }).timeout(10000)

  test('user can create minimal event with only title', async ({ client, assert }) => {
    const { authToken } = await createTestUser('minimaluser')

    const eventData = {
      title: 'Minimal Event',
    }

    const response = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json(eventData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.title, eventData.title)
    assert.equal(body.data.status, 'active') // Default status
    assert.isTrue(body.data.description === null || body.data.description === undefined)
    assert.isTrue(body.data.location === null || body.data.location === undefined)
    assert.isTrue(body.data.maxAttendees === null || body.data.maxAttendees === undefined)
  }).timeout(10000)

  test('event creation validates required fields', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validationuser')

    // Test missing title
    const response1 = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json({})

    response1.assertStatus(422) // Validation error should be 422

    // Test title too long
    const response2 = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json({
        title: 'a'.repeat(256), // Exceeds 255 character limit
      })

    response2.assertStatus(422)

    // Test invalid status
    const response3 = await client
      .post('/api/v1/event')
      .bearerToken(authToken)
      .json({
        title: 'Valid Title',
        status: 'invalid_status',
      })

    response3.assertStatus(422)
  }).timeout(10000)

  test('user can get specific event by ID', async ({ client, assert }) => {
    const { authToken } = await createTestUser('getuser')

    // Create an event first
    const { response: createResponse } = await createTestEvent(client, authToken)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // Get the event
    const response = await client
      .get(`/api/v1/event/${eventId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.id, eventId)
    assert.equal(body.data.title, 'Test Event')
    assert.equal(body.data.description, 'Test event description')
  }).timeout(10000)

  test('user cannot access other users events', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('user1')
    const { authToken: user2Token } = await createTestUser('user2')

    // User 1 creates an event
    const { response: createResponse } = await createTestEvent(client, user1Token)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // User 2 tries to access User 1's event
    const response = await client
      .get(`/api/v1/event/${eventId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden
    const body = response.body()
    assert.equal(body.error, 'Access denied')
  }).timeout(10000)

  test('user can list their events', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listuser')

    // Create multiple events
    await createTestEvent(client, authToken, { title: 'Event 1' })
    await createTestEvent(client, authToken, { title: 'Event 2' })
    await createTestEvent(client, authToken, { title: 'Event 3' })

    const response = await client.get('/api/v1/events').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.exists(body.data.data)
    assert.exists(body.data.pagination)
    assert.isArray(body.data.data)
    assert.isAtLeast(body.data.data.length, 3)
  }).timeout(15000)

  test('event listing supports pagination', async ({ client, assert }) => {
    const { authToken } = await createTestUser('paginationuser')

    // Create multiple events
    for (let i = 1; i <= 5; i++) {
      await createTestEvent(client, authToken, { title: `Event ${i}` })
    }

    // Test pagination
    const response = await client
      .get('/api/v1/events')
      .bearerToken(authToken)
      .qs({ page: 1, limit: 2 })

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.data.length, 2)
    assert.exists(body.data.pagination)
    assert.equal(body.data.pagination.currentPage, 1)
    assert.isAtLeast(body.data.pagination.totalCount, 5)
  }).timeout(20000)

  test('event listing supports status filtering', async ({ client, assert }) => {
    const { authToken } = await createTestUser('filteruser')

    // Create events with different statuses
    await createTestEvent(client, authToken, { title: 'Active Event', status: 'active' })
    await createTestEvent(client, authToken, { title: 'Cancelled Event', status: 'cancelled' })
    await createTestEvent(client, authToken, { title: 'Postponed Event', status: 'postponed' })

    // Filter by status
    const response = await client
      .get('/api/v1/events')
      .bearerToken(authToken)
      .qs({ status: 'active' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.data.length, 1)
    // All events should have active status
    body.data.data.forEach((event: any) => {
      assert.equal(event.status, 'active')
    })
  }).timeout(15000)

  test('event listing supports search', async ({ client, assert }) => {
    const { authToken } = await createTestUser('searchuser')

    // Create events with searchable content
    await createTestEvent(client, authToken, { 
      title: 'Team Meeting', 
      description: 'Weekly sync' 
    })
    await createTestEvent(client, authToken, { 
      title: 'Client Call', 
      description: 'Project discussion' 
    })

    // Search by title
    const response = await client
      .get('/api/v1/events')
      .bearerToken(authToken)
      .qs({ search: 'Team' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.data.length, 1)
    // Should find the team meeting
    const foundEvent = body.data.data.find((event: any) => event.title.includes('Team'))
    assert.exists(foundEvent)
  }).timeout(15000)

  test('user can update event', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updateuser')

    // Create an event first
    const { response: createResponse } = await createTestEvent(client, authToken)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // Update the event
    const updateData = {
      title: 'Updated Event Title',
      description: 'Updated description',
      status: 'postponed',
      maxAttendees: 20,
    }

    const response = await client
      .put(`/api/v1/event/${eventId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.title, updateData.title)
    assert.equal(body.data.description, updateData.description)
    assert.equal(body.data.status, updateData.status)
    assert.equal(body.data.maxAttendees, updateData.maxAttendees)
    assert.equal(body.data.id, eventId)
  }).timeout(10000)

  test('user can partially update event', async ({ client, assert }) => {
    const { authToken } = await createTestUser('partialupdateuser')

    // Create an event first
    const { response: createResponse, eventData } = await createTestEvent(client, authToken)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // Partial update - only title
    const updateData = {
      title: 'Partially Updated Title',
    }

    const response = await client
      .put(`/api/v1/event/${eventId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.title, updateData.title)
    // Other fields should remain unchanged
    assert.equal(body.data.description, eventData.description)
    assert.equal(body.data.status, eventData.status)
  }).timeout(10000)

  test('user cannot update other users events', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('updateuser1')
    const { authToken: user2Token } = await createTestUser('updateuser2')

    // User 1 creates an event
    const { response: createResponse } = await createTestEvent(client, user1Token)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // User 2 tries to update User 1's event
    const response = await client
      .put(`/api/v1/event/${eventId}`)
      .bearerToken(user2Token)
      .json({ title: 'Hacked Title' })

    response.assertStatus(404) // Not found (due to ownership check design in Event controller)
  }).timeout(10000)

  test('user can delete event', async ({ client, assert }) => {
    const { authToken } = await createTestUser('deleteuser')

    // Create an event first
    const { response: createResponse } = await createTestEvent(client, authToken)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // Delete the event
    const response = await client
      .delete(`/api/v1/event/${eventId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.message, 'Event deleted successfully')

    // Verify event is deleted (should return 404)
    const getResponse = await client
      .get(`/api/v1/event/${eventId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(404)
  }).timeout(10000)

  test('user cannot delete other users events', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('deleteuser1')
    const { authToken: user2Token } = await createTestUser('deleteuser2')

    // User 1 creates an event
    const { response: createResponse } = await createTestEvent(client, user1Token)
    createResponse.assertStatus(201)
    const eventId = createResponse.body().data.id

    // User 2 tries to delete User 1's event
    const response = await client
      .delete(`/api/v1/event/${eventId}`)
      .bearerToken(user2Token)

    response.assertStatus(404) // Not found (due to ownership check design in Event controller)
  }).timeout(10000)

  test('event operations require authentication', async ({ client, assert }) => {
    // Test all endpoints without authentication
    const response1 = await client.get('/api/v1/events')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/event').json({ title: 'Test' })
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/event/123')
    response3.assertStatus(401)

    const response4 = await client.put('/api/v1/event/123').json({ title: 'Test' })
    response4.assertStatus(401)

    const response5 = await client.delete('/api/v1/event/123')
    response5.assertStatus(401)
  }).timeout(10000)

  test('invalid event ID returns 400', async ({ client, assert }) => {
    const { authToken } = await createTestUser('invaliduser')

    const invalidIds = ['', 'invalid-id', '123']

    for (const invalidId of invalidIds) {
      const getResponse = await client
        .get(`/api/v1/event/${invalidId}`)
        .bearerToken(authToken)

      const updateResponse = await client
        .put(`/api/v1/event/${invalidId}`)
        .bearerToken(authToken)
        .json({ title: 'Test' })

      const deleteResponse = await client
        .delete(`/api/v1/event/${invalidId}`)
        .bearerToken(authToken)

      // Should return error status for invalid/non-existent IDs
      assert.oneOf(getResponse.response.status, [400, 404, 500])
      assert.oneOf(updateResponse.response.status, [400, 404, 500])
      assert.oneOf(deleteResponse.response.status, [400, 404, 500])
    }
  }).timeout(15000)
})