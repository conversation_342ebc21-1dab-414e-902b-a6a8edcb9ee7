import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Task Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  test('user can list tasks', async ({ client, assert }) => {
    const { authToken } = await createTestUser('taskuser')

    const response = await client.get('/api/v1/tasks').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.exists(body.pagination)
    assert.isArray(body.data)
  }).timeout(10000)

  // test('user can create task via AI prompt', async ({ client, assert }) => {
  //   const { authToken } = await createTestUser('aitaskuser')

  //   const response = await client
  //     .post('/api/v1/ai/prompt')
  //     .bearerToken(authToken)
  //     .json({ prompt: 'Create a simple test task' })

  //   response.assertStatus(201)
  //   const body = response.body()

  //   assert.equal(body.status, 'success')
  //   assert.exists(body.data)
  //   assert.exists(body.data.title)
  //   assert.exists(body.data.description)
  //   assert.equal(body.data.entityType, 'ai_generated')
  //   assert.equal(body.data.status, 'pending')
  //   assert.exists(body.data.id)
  //   assert.exists(body.data.userId)
  // }).timeout(15000)

  test('task operations require authentication', async ({ client, assert }) => {
    const response1 = await client.get('/api/v1/tasks')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/ai/prompt').json({ prompt: 'test' })
    response2.assertStatus(401)
  }).timeout(10000)
})
