import type { HttpContext } from '@adonisjs/core/http'
import { TaskService } from '#features/task/task_service'
import {
  taskPromptValidator,
  taskQueryValidator,
  taskUpdateValidator,
} from '#features/task/task_validator'
import { createSuccessResponse } from '#utils/response'
import { TaskFilters } from '#features/task/task_types'
import { TaskStatus, TaskEntityType } from '#types/activity_types'
import { DateTime } from 'luxon'

export default class AITaskController {
  private service = new TaskService()
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.service,
      resourceName: 'task',
      validateQueryParams: async (request: any) => await request.validateUsing(taskQueryValidator),
      getStatusMapping: () => ({
        pending: 'pending',
        in_progress: 'in_progress',
        completed: 'completed',
        deferred: 'deferred',
      }),
      getSortMapping: () => ({
        startDate: 'startDate',
        endDate: 'endDate',
        priority: 'priority',
        createdAt: 'createdAt',
        title: 'title',
        status: 'status',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<TaskFilters>) => {
        if (queryParams.priority) {
          filters.priority = queryParams.priority
        }
        if (queryParams.tags) {
          filters.tags = queryParams.tags
        }
      },
    }
  }

  /**
   * List all tasks for the authenticated user with advanced filtering, sorting, and pagination
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Get a specific task by ID
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete a task (soft delete)
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Store a new task (AI-generated)
   */
  async store({ request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'task')

      const payload = await request.validateUsing(taskPromptValidator)
      const { prompt } = payload

      if (!prompt) {
        throw new Error('No prompt provided')
      }

      const aiResult = await this.service.aiPrompt({ prompt })

      // Prepare task data with flattened structure
      const taskData: Parameters<TaskService['createTask']>[1] = {
        title: aiResult.title || 'AI Generated Task',
        description: aiResult.description || null,
        startDate: aiResult.startDate ? DateTime.fromISO(aiResult.startDate) : null,
        endDate: aiResult.endDate ? DateTime.fromISO(aiResult.endDate) : null,
        status: TaskStatus.PENDING,
        priority: aiResult.priority || 'medium',
        remindBefore: aiResult.remindBefore || null,
        people: aiResult.people || null,
        entityType: TaskEntityType.AI_GENERATED,
        location: aiResult.locations || null, // location field in DB is JSONB
        metadata: {
          daysCountdown: aiResult.daysCountdown,
          dueTime: aiResult.dueTime,
          duePartOfDay: aiResult.duePartOfDay,
          tags: aiResult.tags,
          suggestedTags: aiResult.suggestedTags,
        },
      }

      // If metadata is an empty object, set it to null
      if (Object.keys(taskData.metadata as object).length === 0) {
        taskData.metadata = null
      }

      const createdTask = await this.service.createTask(userId, taskData)
      return response
        .status(201)
        .json(createSuccessResponse({ data: createdTask, message: 'Task created successfully' }))
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Update a task
   */
  async update({ params, request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'task')

      const { id: taskId } = params
      if (!taskId) {
        throw new Error('Task ID is required')
      }

      await base.validateResourceOwnership(taskId, userId, this.service, 'task')
      const payload = await request.validateUsing(taskUpdateValidator)

      // Prepare update data with proper DateTime conversion and metadata handling
      const updateData: Partial<Parameters<TaskService['updateTask']>[1]> = {
        ...payload,
        startDate: payload.startDate ? DateTime.fromISO(payload.startDate) : undefined,
        endDate: payload.endDate ? DateTime.fromISO(payload.endDate) : undefined,
        status: payload.status
          ? TaskStatus[payload.status.toUpperCase() as keyof typeof TaskStatus]
          : undefined,
        entityType: payload.entityType
          ? TaskEntityType[payload.entityType.toUpperCase() as keyof typeof TaskEntityType]
          : undefined,
      }

      // If metadata exists and is an empty object, set it to null
      if (updateData.metadata && Object.keys(updateData.metadata).length === 0) {
        updateData.metadata = null
      }

      const updatedTask = await this.service.updateTask(taskId, updateData)
      return response
        .status(200)
        .json(createSuccessResponse({ data: updatedTask, message: 'Task updated successfully' }))
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }
}
