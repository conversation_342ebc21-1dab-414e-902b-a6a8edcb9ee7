import Task from '#features/task/task_model'
import { z } from 'zod'
import { DateTime } from 'luxon'
import { AIService } from './services/ai_service.js'
import { LangchainAIService } from './services/langchain_ai_service.js'
import { GetTasksOptions, TaskFilters, TaskSort } from './task_types.js'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import { TaskStatus, TaskEntityType } from '#types/activity_types'
import { PaginatedResponse } from '#types/base_query_types'
import { BaseResourceService } from '#controllers/base_controller'

export class TaskService implements BaseResourceService<Task, TaskFilters, TaskSort> {
  private aiService: AIService = new LangchainAIService()

  async createTask(
    userId: string,
    taskData: {
      title: string
      description?: string | null
      location?: any | null
      startDate?: DateTime | null
      endDate?: DateTime | null
      status?: TaskStatus
      priority?: string | null
      remindBefore?: string[] | null
      people?: string[] | null
      metadata?: Record<string, any> | null
      entityType?: TaskEntityType
      parentTaskId?: string | null
    }
  ) {
    // Create Task directly with all fields
    const task = await Task.create({
      userId,
      title: taskData.title,
      description: taskData.description,
      location: taskData.location,
      startDate: taskData.startDate,
      endDate: taskData.endDate,
      status: taskData.status || TaskStatus.PENDING,
      priority: taskData.priority,
      remindBefore: taskData.remindBefore,
      people: taskData.people,
      metadata: taskData.metadata,
      entityType: taskData.entityType || TaskEntityType.USER_SAVE,
      parentTaskId: taskData.parentTaskId,
    })

    return task
  }

  /**
   * Base interface implementation - create resource
   */
  async createResource(userId: string, data: any): Promise<Task> {
    // Assuming `data` here contains all necessary fields for task creation, matching the `taskData` type
    return this.createTask(userId, data)
  }

  async getTaskById(id: string) {
    const task = await Task.query().where('id', id).whereNull('deleted_at').first()

    return task
  }

  /**
   * Base interface implementation - get resource by ID
   */
  async getResourceById(id: string): Promise<Task | null> {
    return this.getTaskById(id)
  }

  /**
   * Base interface implementation - get resources with pagination
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean }
  ): Promise<PaginatedResponse<Task>> {
    return this.getTasks(userId, options as GetTasksOptions)
  }

  /**
   * Base interface implementation - delete resource
   */
  async deleteResource(id: string, _userId: string): Promise<boolean> {
    try {
      await this.deleteTask(id)
      return true
    } catch {
      return false
    }
  }

  /**
   * Unified method to get tasks with optional filtering, sorting, and pagination
   * @param userId - The user ID to filter tasks for
   * @param options - Optional query parameters and pagination settings
   * @returns Paginated tasks using Lucid's built-in pagination, or all tasks if pagination is disabled
   */
  async getTasks(userId: string, options: GetTasksOptions = {}): Promise<PaginatedResponse<Task>> {
    const { queryParams = {}, paginate = true } = options
    const { filters, sort, pagination } = queryParams

    let query = Task.query().where('user_id', userId).whereNull('deleted_at')

    if (filters) {
      query = this.applyFilters(query, filters)
    }

    if (sort) {
      query = this.applySorting(query, sort)
    } else {
      query = query.orderBy('created_at', 'desc')
    }

    if (!paginate) {
      const tasks = await query
      return {
        data: tasks,
        pagination: undefined,
      }
    }

    const { page = 1, limit = 20 } = pagination || {}
    const paginatedResult = await query.paginate(page, limit)
    const tasks = paginatedResult.all()

    return {
      data: tasks,
      pagination: {
        currentPage: paginatedResult.currentPage,
        totalPages: paginatedResult.lastPage,
        totalCount: paginatedResult.total,
        hasNextPage: paginatedResult.hasMorePages,
      },
    }
  }

  private applyFilters(
    query: ModelQueryBuilderContract<typeof Task>,
    filters: TaskFilters
  ): ModelQueryBuilderContract<typeof Task> {
    if (filters.status) {
      query = query.where('status', filters.status)
    }

    if (filters.priority) {
      query = query.where('priority', filters.priority)
    }

    if (filters.search) {
      query = query.where((subQuery) => {
        subQuery
          .whereILike('title', `%${filters.search}%`)
          .orWhereILike('description', `%${filters.search}%`)
      })
    }

    if (filters.startDate) {
      const startDate = DateTime.fromISO(filters.startDate).toSQL()
      if (startDate) {
        query = query.where('start_date', '>=', startDate)
      }
    }

    if (filters.endDate) {
      const endDate = DateTime.fromISO(filters.endDate).toSQL()
      if (endDate) {
        query = query.where('end_date', '<=', endDate)
      }
    }

    if (filters.tags && filters.tags.length > 0) {
      // Filter by tags stored in the tags JSONB column
      query = query.whereJsonSuperset('tags', filters.tags)
    }

    return query
  }

  private applySorting(
    query: ModelQueryBuilderContract<typeof Task>,
    sort: TaskSort
  ): ModelQueryBuilderContract<typeof Task> {
    const { field, direction = 'asc' } = sort

    switch (field) {
      case 'title':
        return query.orderBy('title', direction)
      case 'status':
        return query.orderBy('status', direction)
      case 'priority':
        return query.orderBy('priority', direction)
      case 'startDate':
        return query.orderBy('start_date', direction)
      case 'endDate':
        return query.orderBy('end_date', direction)
      case 'createdAt':
        return query.orderBy('created_at', direction)
      case 'updatedAt':
        return query.orderBy('updated_at', direction)
      default:
        return query.orderBy('created_at', 'desc')
    }
  }

  async updateTask(id: string, data: Partial<Task>) {
    const task = await Task.findOrFail(id)
    task.merge(data)
    await task.save()
    return task
  }

  /**
   * Base interface implementation - update resource
   */
  async updateResource(id: string, data: any): Promise<Task> {
    // Assuming `data` here contains all necessary fields for task update, matching the `Partial<Task>` type
    return this.updateTask(id, data)
  }

  async deleteTask(id: string) {
    const task = await Task.findOrFail(id)
    await task.delete() // This will use soft delete
    return task
  }

  async aiPrompt({ prompt }: { prompt: string }) {
    const dateTime = new Date().toISOString()
    const genPrompt = `${prompt}. System context: Current datetime is ${dateTime}. Current day is Sunday.`
    const TaskSchema = z.object({
      title: z
        .string()
        .describe('The title of the task. Descriptive but short and concise. Avoid 1 word titles.'),
      description: z
        .string()
        .describe(
          'The description of the task, you can be more descriptive here such that all details are captured. Dont leave out any details.'
        ),
      locations: z
        .array(z.string())
        .describe(
          'The locations mentioned in the prompt. Could be user-defined or suggested by the AI'
        ),
      startDate: z
        .string()
        .nullable()
        .describe(
          'The start date of the task. Extract from the prompt using explicit details like given time, date, day, or hour. Leave null if nothing is extracted, avoid hallucination or guessing.'
        ),
      endDate: z
        .string()
        .nullable()
        .describe(
          'The due date of the task. Extract from the prompt using explicit details like given time, date, day, or hour. Leave null if nothing is extracted, avoid hallucination or guessing.'
        ),
      remindBefore: z
        .array(z.string())
        .describe(
          'The time before the task due date to remind the user, unit is minutes, if user did not provide, use 15mins as the default reminder time'
        ),
      tags: z
        .array(z.string())
        .describe(
          'STRICT: Only extract tags if the user explicitly writes "tags:" followed by tag names in their prompt. If the word "tags:" does not appear in the prompt, this MUST be an empty array. Do NOT generate, infer, suggest, or create any tags from context.'
        ),
      priority: z
        .string()
        .nullable()
        .describe(
          'Priority of the task. Either one of the following: "low", "medium", "high". If user did not specify, try to determine based on prompt sentiment, context and other details. If still cannot determine, use "medium" as the default priority.'
        ),
      suggestedTags: z
        .array(z.string())
        .describe(
          'Generate up to 3 relevant tags or keywords for the task based on the prompt\'s content and implied context. These tags should be distinct from any "tags:" explicitly provided by the user.'
        ),
      people: z
        .array(z.string())
        .describe(
          'The people involved in the task. Could be user-defined by guessing from the user prompt, if the work is likely a human name then add it to the array, if none, leave empty array'
        ),
      daysCountdown: z
        .string()
        .nullable()
        .describe(
          'Number of days from current time/day (from system prompt) the task is being referred to. For context, end of week is Sunday. Only extract if explicitly mentioned in prompt. Do not hallucinate. Dont set value otherwise.'
        ),
      dueTime: z
        .string()
        .describe(
          'Specific time of the day to complete the task. Specific by means of if prompt explicitly contains a numeric time, not guessed by LLM. Otherwise, leave null. Do not infer and avoid defaulting to "00:00".'
        ),
      duePartOfDay: z
        .string()
        .describe(
          'Extract part of the day from the prompt. One of the following: morning, breakfast, lunch, afternoon, evening, night. Leave null if user did not specify.'
        ),
    })

    type TaskType = z.infer<typeof TaskSchema>
    console.log('genPrompt', genPrompt)

    return this.aiService.aiPrompt<TaskType>({
      model: 'gpt-4.1-mini',
      systemPrompt: `You are a task manager assistant. Extract intent and entities per schema. Format dates as standard UTC datetime that can be parsed into database, use current datetime for reference.`,
      prompt: genPrompt,
      schema: TaskSchema,
      temperature: 0,
    })
  }
}
