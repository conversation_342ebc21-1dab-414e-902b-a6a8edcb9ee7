import vine from '@vinejs/vine'

// Common fields for task schema
const commonTaskFields = {
  description: vine.string().trim().maxLength(1000).optional(),
  location: vine.any().optional(), // Can be an object with address, coordinates, etc.
  startDate: vine.string().optional(), // ISO date string
  endDate: vine.string().optional(), // ISO date string
  status: vine.string().in(['pending', 'in_progress', 'completed', 'deferred']).optional(),
  priority: vine.string().in(['low', 'medium', 'high']).optional(),
  remindBefore: vine.array(vine.string()).optional(),
  people: vine.array(vine.string()).optional(),
  metadata: vine.any().optional(),
  entityType: vine.string().optional(),
  tags: vine.array(vine.string()).optional(),
  suggestedTags: vine.array(vine.string()).optional(),
  locations: vine.array(vine.any()).optional(),
  parentTaskId: vine.string().uuid().optional(),
}

// Validator for creating tasks with flattened structure
export const taskCreateValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255),
    ...commonTaskFields,
  })
)

// Validator for updating tasks with flattened structure
export const taskUpdateValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255).optional(),
    ...commonTaskFields,
  })
)

export const taskPromptValidator = vine.compile(
  vine.object({
    prompt: vine.string().trim().maxLength(1000),
  })
)

import { createQueryValidator } from '#validators/base_validators'

export const taskQueryValidator = createQueryValidator({
  statusOptions: ['pending', 'in_progress', 'completed', 'deferred'],
  sortFields: ['startDate', 'endDate', 'priority', 'createdAt', 'updatedAt', 'title', 'status'],
  additionalFields: {
    priority: vine.string().in(['low', 'medium', 'high']).optional(),
    tags: vine.array(vine.string()).optional(),
  },
})
