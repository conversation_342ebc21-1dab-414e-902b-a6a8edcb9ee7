import { generateObject } from 'ai'
import { openai } from '@ai-sdk/openai'
import { AIService } from './ai_service.js'
import { ZodSchema } from 'zod'

export class VercelAIService implements AIService {
  async aiPrompt<T>({
    model,
    systemPrompt,
    prompt,
    schema,
  }: {
    model: string
    systemPrompt?: string
    prompt: string
    schema: ZodSchema<T>
  }): Promise<T> {
    const { object: result } = await generateObject({
      model: openai(model),
      system: systemPrompt ?? '',
      schema,
      prompt,
    })
    return result
  }
}
