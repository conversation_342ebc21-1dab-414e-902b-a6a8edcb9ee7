import { AIService } from './ai_service.js'
import { ZodSchema } from 'zod'
import { ChatOpenAI } from '@langchain/openai'

/* // Manual generate approach imports
import { StructuredOutputParser } from '@langchain/core/output_parsers'
import { HumanMessage } from '@langchain/core/messages'
*/

export class LangchainAIService implements AIService {
  async aiPrompt<T>({
    model,
    systemPrompt,
    prompt,
    schema,
    temperature,
  }: {
    model: string
    systemPrompt?: string
    prompt: string
    schema: ZodSchema<T>
    temperature?: number
  }): Promise<T> {
    // Initialize the ChatOpenAI model
    const llm = new ChatOpenAI({ modelName: model, temperature: temperature ?? 0 })

    /* // Manual generate approach
    const parser = StructuredOutputParser.fromZodSchema(schema)
    const formatInstructions = parser.getFormatInstructions()
    const fullPrompt = `${systemPrompt || ''}\n\n${formatInstructions}\n\n${prompt}`
    const result = await llm.generate([[new HumanMessage(fullPrompt)]])
    console.log('Token usage:', result.llmOutput?.tokenUsage)
    const text = result.generations[0][0].text
    const parsedManual = await parser.parse(text)
    return parsedManual as T
    */

    // ===== StructuredOutput runnable approach =====
    const runnable = llm.withStructuredOutput<any>(schema as any)

    // Combine system prompt and user prompt
    const input = `${systemPrompt || ''}\n\n${prompt}`

    // Run the model and get parsed output
    const parsed = await runnable.invoke(input)

    return parsed as T
  }
}
