import { ZodSchema } from 'zod'

export interface AIService {
  /**
   * Sends a prompt (with optional system instructions & schema) and returns a typed result
   */
  aiPrompt<T>(params: {
    /** The model name or identifier to use */
    model: string
    /** Optional system prompt to provide context */
    systemPrompt?: string
    /** The user's prompt */
    prompt: string
    /** A Zod schema describing the expected shape of the result */
    schema: ZodSchema<T>
    /** Optional temperature for model randomness */
    temperature?: number
  }): Promise<T>
}
