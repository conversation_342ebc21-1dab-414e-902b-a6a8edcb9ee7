import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import { jsonArrayColumn } from '#utils/serializers'
import { TaskStatus, TaskEntityType } from '#types/activity_types'
import User from '#features/user/user_model'

export default class Task extends compose(BaseModel, SoftDeletes) {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  // Activity fields now included directly in Task
  @column()
  declare title: string

  @column()
  declare description: string | null

  @column.dateTime({ columnName: 'start_date' })
  declare startDate: DateTime | null

  @column.dateTime({ columnName: 'end_date' })
  declare endDate: DateTime | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => (value ? JSON.parse(value) : null),
  })
  declare location: Record<string, any> | null

  // Task-specific fields
  @column()
  declare status: TaskStatus

  @column()
  declare priority: string | null

  @column(jsonArrayColumn)
  declare remindBefore: string[] | null

  @column(jsonArrayColumn)
  declare people: string[] | null

  @column(jsonArrayColumn)
  declare locations: string[] | null

  @column(jsonArrayColumn)
  declare tags: string[] | null

  @column(jsonArrayColumn)
  declare suggestedTags: string[] | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: any) => {
      // Handle null, undefined, or non-string values
      if (value === null || value === undefined || typeof value !== 'string') {
        return null
      }

      // Handle empty strings
      if (value.trim() === '') {
        return null
      }

      try {
        return JSON.parse(value)
      } catch (e) {
        console.error('Error parsing metadata JSON:', value, e)
        return null // Return null or a default object if parsing fails
      }
    },
  })
  declare metadata: Record<string, any> | null

  @column({ columnName: 'entity_type' })
  declare entityType: TaskEntityType

  @column({ columnName: 'parent_task_id' })
  declare parentTaskId: string | null

  @belongsTo(() => Task, { foreignKey: 'parentTaskId' })
  declare parentTask: BelongsTo<typeof Task>

  // Standard timestamps and soft delete support
  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  declare deletedAt: DateTime | null
}
