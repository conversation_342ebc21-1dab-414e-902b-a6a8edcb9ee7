import {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  GetResourcesOptions,
} from '#types/base_query_types'

export interface TaskFilters extends BaseFilters {
  status?: 'pending' | 'in_progress' | 'completed' | 'deferred'
  priority?: 'low' | 'medium' | 'high'
  tags?: string[]
}

export interface TaskSort extends BaseSort {
  field: 'startDate' | 'endDate' | 'priority' | 'createdAt' | 'updatedAt' | 'title' | 'status'
}

export type TaskQueryParams = BaseQueryParams<TaskFilters, TaskSort>

export type GetTasksOptions = GetResourcesOptions<TaskFilters, TaskSort>
