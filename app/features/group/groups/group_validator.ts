import vine from '@vinejs/vine'
import { GROUP_MEMBER_ROLE_VALUES } from '#types/group_types'

/**
 * Validator for creating a new group
 */
export const createGroupValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255),
    description: vine.string().trim().maxLength(1000).optional(),
  })
)

/**
 * Validator for updating group details
 */
export const updateGroupValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255).optional(),
    description: vine.string().trim().maxLength(1000).optional(),
  })
)

/**
 * Validator for group query parameters
 */
export const groupQueryValidator = vine.compile(
  vine.object({
    // Filters
    ownerId: vine.string().uuid().optional(),
    memberUserId: vine.string().uuid().optional(),
    role: vine.enum(GROUP_MEMBER_ROLE_VALUES).optional(),
    search: vine.string().trim().optional(),

    // Sorting
    sortBy: vine.enum(['createdAt', 'updatedAt', 'name']).optional(),
    sortDirection: vine.enum(['asc', 'desc']).optional(),

    // Pagination
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)
