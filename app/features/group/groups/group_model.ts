import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
// Using lazy loading to avoid circular imports

/**
 * UserGroup Model
 *
 * Represents a user-created group that can contain multiple members.
 * Groups have an owner and can have multiple members with different roles.
 *
 * Based on the user_group table in the ERD.
 */
export default class UserGroup extends compose(BaseModel, SoftDeletes) {
  static table = 'user_group'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare ownerUserId: string

  @column()
  declare name: string

  @column()
  declare description: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationship to the user who owns this group
   */
  @belongsTo(() => User, { foreignKey: 'ownerUserId' })
  declare owner: BelongsTo<typeof User>

  // Note: Relationships to members and invitations are handled through the GroupService
  // to avoid circular import issues and maintain clean separation of concerns

  /**
   * Check if the group is owned by a specific user
   */
  public isOwnedBy(userId: string): boolean {
    return this.ownerUserId === userId
  }

  // Note: Member and invitation management methods will be implemented in the GroupService
  // to handle the relationships properly without circular imports
}
