import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import {
  createIndexH<PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON>and<PERSON>,
  createUpdate<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON>and<PERSON>,
  type ControllerConfig,
} from '#controllers/base_controller'
import GroupService from './group_service.js'
import {
  createGroupValidator,
  updateGroupValidator,
  groupQueryValidator,
} from './group_validator.js'
import type { GroupFilters, GroupSort } from '#types/group_types'
import { authorizationException } from '#utils/error'

/**
 * GroupController
 *
 * Handles HTTP requests for core group management:
 * - Group CRUD operations
 * - Uses base controller utilities for consistent behavior
 */
@inject()
export default class GroupController {
  constructor(private groupService: GroupService) {}

  /**
   * Configuration for base controller utilities
   */
  private getConfig(): ControllerConfig<any, GroupFilters, GroupSort, any> {
    return {
      service: this.groupService,
      resourceName: 'group',
      validateQueryParams: async (request: any) => await request.validateUsing(groupQueryValidator),
      getSortMapping: () => ({
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
        name: 'name',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<GroupFilters>) => {
        if (queryParams.ownerId) {
          filters.ownerId = queryParams.ownerId
        }
        if (queryParams.memberUserId) {
          filters.memberUserId = queryParams.memberUserId
        }
        if (queryParams.role) {
          filters.role = queryParams.role
        }
      },
      validateCreatePayload: async (request: any) =>
        await request.validateUsing(createGroupValidator),
      validateUpdatePayload: async (request: any) =>
        await request.validateUsing(updateGroupValidator),
    }
  }

  /**
   * Create a new group
   * POST /api/v1/groups
   */
  store = createStoreHandler(this.getConfig())

  /**
   * List groups for the authenticated user
   * GET /api/v1/groups
   */
  index = createIndexHandler(this.getConfig())

  /**
   * Get a specific group by ID with permission check
   * GET /api/v1/groups/:id
   */
  show = async (ctx: HttpContext) => {
    const { auth, params } = ctx
    const user = auth.getUserOrFail()
    const { id } = params

    // Check if user has permission to view this group
    const canView = await this.groupService.checkGroupPermission(id, user.id, 'view')
    if (!canView) {
      throw authorizationException('You do not have permission to view this group')
    }

    const handler = createShowHandler(this.getConfig())
    return handler(ctx)
  }

  /**
   * Update group details
   * PUT /api/v1/groups/:id
   */
  update = createUpdateHandler(this.getConfig())

  /**
   * Delete a group
   * DELETE /api/v1/groups/:id
   */
  destroy = createDestroyHandler(this.getConfig())
}
