import { test } from '@japa/runner'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Group Collaboration', (group) => {
  let hostToken: string
  let inviteeToken: string
  let inviteeUserId: string
  let groupId: string
  let invitationId: string

  // Helper function to create a test user (participates in database transactions)
  const createTestUser = async (prefix: string) => {
    const testUser = await createTestUserWithToken(prefix)
    return {
      token: testUser.token,
      userId: testUser.userId,
    }
  }

  group.setup(async () => {
    await db.beginGlobalTransaction()

    // Create a host user (group owner)
    const hostUser = await createTestUser('host')
    hostToken = hostUser.token

    // Create invitee user
    const inviteeUser = await createTestUser('invitee')
    inviteeToken = inviteeUser.token
    inviteeUserId = inviteeUser.userId

    return () => db.rollbackGlobalTransaction()
  })

  test('host can create group for collaboration testing', async ({ client, assert }) => {
    if (!hostToken) {
      assert.fail('Setup failed - no host auth token available')
      return
    }

    const groupData = {
      name: 'Collaboration Test Group',
      description: 'Testing group collaboration features',
    }

    const response = await client.post('/api/v1/groups').bearerToken(hostToken).json(groupData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.name, groupData.name)

    // Store group ID for collaboration tests
    groupId = body.data.id
  }).timeout(15000)

  test('host can send group invitation to another user', async ({ client, assert }) => {
    if (!hostToken || !inviteeUserId || !groupId) {
      assert.fail('Setup failed - missing host token, invitee user, or group')
      return
    }

    const invitationData = {
      userId: inviteeUserId,
      message: 'Join our collaboration test group!',
    }

    const response = await client
      .post(`/api/v1/groups/${groupId}/invitations`)
      .bearerToken(hostToken)
      .json(invitationData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)

    // Store invitation ID for response testing
    if (body.data.id) {
      invitationId = body.data.id
    }
  }).timeout(15000)

  test('invitee can view received group invitations', async ({ client, assert }) => {
    if (!inviteeToken) {
      assert.fail('Setup failed - no invitee auth token available')
      return
    }

    const response = await client.get('/api/v1/groups/my-invitations').bearerToken(inviteeToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)

    // If there are invitations, check the structure
    if (body.data.length > 0) {
      const invitation = body.data[0]
      assert.exists(invitation.id)

      // Store invitation ID if we don't have it yet
      if (!invitationId && invitation.id) {
        invitationId = invitation.id
      }
    }
  }).timeout(15000)

  test('invitee can respond to group invitation (accept)', async ({ client, assert }) => {
    if (!inviteeToken || !invitationId) {
      // Skip this test if we don't have the required data
      assert.plan(0)
      return
    }

    const responseData = {
      status: 'accepted',
    }

    const response = await client
      .put(`/api/v1/groups/invitations/${invitationId}`)
      .bearerToken(inviteeToken)
      .json(responseData)

    response.assertStatus(200)
    const body = response.body()
    assert.equal(body.status, 'success')
  }).timeout(15000)

  test('invitee can respond to group invitation (decline)', async ({ client, assert }) => {
    // Create a second invitation for decline testing if possible
    if (!inviteeToken) {
      assert.plan(0)
      return
    }

    // For now, just test the endpoint structure
    const responseData = {
      status: 'declined',
    }

    // Use a properly formatted UUID to test the endpoint structure
    const response = await client
      .put('/api/v1/groups/invitations/00000000-0000-0000-0000-000000000000')
      .bearerToken(inviteeToken)
      .json(responseData)

    // Should return 404 for non-existent invitation
    response.assertStatus(404)
  }).timeout(15000)

  test('user can create join request for group', async ({ client, assert }) => {
    if (!groupId) {
      assert.fail('Setup failed - missing group')
      return
    }

    // Create a new user specifically for the join request (not the host or invitee)
    const joinRequester = await createTestUser('joinrequester')

    const requestData = {
      message: 'I would like to join this group',
    }

    const response = await client
      .post(`/api/v1/groups/${groupId}/join-requests`)
      .bearerToken(joinRequester.token)
      .json(requestData)

    // Should succeed since this is a new user requesting to join
    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.message, requestData.message)
    assert.equal(body.data.type, 'request')
    assert.equal(body.data.status, 'pending')
    assert.equal(body.data.userId, joinRequester.userId)
  }).timeout(15000)

  test('host can view join requests for their group', async ({ client, assert }) => {
    if (!hostToken || !groupId) {
      assert.fail('Setup failed - missing host token or group')
      return
    }

    const response = await client
      .get(`/api/v1/groups/${groupId}/invitations`)
      .bearerToken(hostToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(15000)

  test('user can add member to group', async ({ client, assert }) => {
    if (!hostToken || !groupId) {
      assert.fail('Setup failed - missing host token or group')
      return
    }

    // Create a new user specifically for member addition (not the existing invitee)
    const newMember = await createTestUser('newmember')

    const memberData = {
      userId: newMember.userId,
      role: 'member',
    }

    const response = await client
      .post(`/api/v1/groups/${groupId}/members`)
      .bearerToken(hostToken)
      .json(memberData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
  }).timeout(15000)

  test('user can remove member from group', async ({ client, assert }) => {
    if (!hostToken || !inviteeUserId || !groupId) {
      assert.fail('Setup failed - missing host token, invitee user, or group')
      return
    }

    const response = await client
      .delete(`/api/v1/groups/${groupId}/members/${inviteeUserId}`)
      .bearerToken(hostToken)

    response.assertStatus(200)
    const body = response.body()
    assert.equal(body.status, 'success')
  }).timeout(15000)

  test('collaboration operations require authentication', async ({ client }) => {
    const dummyGroupId = '00000000-0000-0000-0000-000000000000'
    const dummyUserId = '00000000-0000-0000-0000-000000000000'

    // Test unauthenticated invitation creation
    const response1 = await client
      .post(`/api/v1/groups/${dummyGroupId}/invitations`)
      .json({ userId: dummyUserId, message: 'test' })
    response1.assertStatus(401)

    // Test unauthenticated invitation response
    const response2 = await client
      .put('/api/v1/groups/invitations/00000000-0000-0000-0000-000000000000')
      .json({ status: 'accepted' })
    response2.assertStatus(401)

    // Test unauthenticated join request
    const response3 = await client
      .post(`/api/v1/groups/${dummyGroupId}/join-requests`)
      .json({ message: 'test' })
    response3.assertStatus(401)

    // Test unauthenticated member addition
    const response4 = await client
      .post(`/api/v1/groups/${dummyGroupId}/members`)
      .json({ userId: dummyUserId, role: 'member' })
    response4.assertStatus(401)
  }).timeout(15000)
})
