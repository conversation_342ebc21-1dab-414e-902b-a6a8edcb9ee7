import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Group Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      user: { userId: testUser.userId },
      authToken: testUser.token,
      userData: { email: testUser.email }
    }
  }

  test('user can create group', async ({ client, assert }) => {
    const { authToken } = await createTestUser('groupcreator')

    const groupData = {
      name: 'Test Group',
      description: 'Test group description'
    }

    const response = await client
      .post('/api/v1/groups')
      .bearerToken(authToken)
      .json(groupData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.name, groupData.name)
    assert.equal(body.data.description, groupData.description)
  }).timeout(10000)

  test('user can view groups', async ({ client, assert }) => {
    const { authToken } = await createTestUser('groupviewer')

    const response = await client.get('/api/v1/groups').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(10000)

  test('user can view group invitations', async ({ client, assert }) => {
    const { authToken } = await createTestUser('invitationviewer')

    const response = await client.get('/api/v1/groups/my-invitations').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(10000)

  test('user can view join requests', async ({ client, assert }) => {
    const { authToken } = await createTestUser('requestviewer')

    const response = await client.get('/api/v1/groups/my-requests').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(10000)

  test('group operations require authentication', async ({ client, assert }) => {
    const groupData = {
      name: 'Test Group',
      description: 'Test description'
    }

    const response1 = await client.post('/api/v1/groups').json(groupData)
    response1.assertStatus(401)

    const response2 = await client.get('/api/v1/groups')
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/groups/my-invitations')
    response3.assertStatus(401)
  }).timeout(10000)

  test('group validation works', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validator')

    // Test missing required fields
    const response = await client
      .post('/api/v1/groups')
      .bearerToken(authToken)
      .json({ description: 'Missing name' })

    response.assertStatus(422)
  }).timeout(10000)

  test('group owner can manage members and invitations', async ({ client, assert }) => {
    const { authToken } = await createTestUser('groupowner')

    // Create a group
    const groupData = {
      name: 'Management Test Group',
      description: 'Group for testing member and invitation management'
    }

    const groupResponse = await client
      .post('/api/v1/groups')
      .bearerToken(authToken)
      .json(groupData)

    groupResponse.assertStatus(201)
    const group = groupResponse.body().data

    // Verify group owner can access group members
    const membersResponse = await client
      .get(`/api/v1/groups/${group.id}/members`)
      .bearerToken(authToken)

    membersResponse.assertStatus(200)
    const members = membersResponse.body().data
    assert.isArray(members)
    assert.isAtLeast(members.length, 1) // Should have at least the owner

    // Verify group owner can create invitations
    const { user: inviteeUser } = await createTestUser('invitee')
    
    const invitationData = {
      userId: inviteeUser.userId,
      message: 'Join our group!'
    }

    const invitationResponse = await client
      .post(`/api/v1/groups/${group.id}/invitations`)
      .bearerToken(authToken)
      .json(invitationData)

    invitationResponse.assertStatus(201)
    const invitation = invitationResponse.body()
    assert.equal(invitation.status, 'success')
    assert.exists(invitation.data)
  }).timeout(20000)

  test('user can invite someone to group', async ({ client, assert }) => {
    const { authToken } = await createTestUser('inviter')

    // First create a group
    const groupData = {
      name: 'Invitation Test Group',
      description: 'Group for testing invitations'
    }

    const groupResponse = await client
      .post('/api/v1/groups')
      .bearerToken(authToken)
      .json(groupData)

    groupResponse.assertStatus(201)
    const group = groupResponse.body().data

    // Create a second user to invite
    const { user: inviteeUser } = await createTestUser('invitee')

    // Send invitation
    const invitationData = {
      userId: inviteeUser.userId,
      message: 'Join our awesome group!'
    }

    const invitationResponse = await client
      .post(`/api/v1/groups/${group.id}/invitations`)
      .bearerToken(authToken)
      .json(invitationData)

    invitationResponse.assertStatus(201)
    const invitation = invitationResponse.body()

    assert.equal(invitation.status, 'success')
    assert.exists(invitation.data)
    assert.equal(invitation.data.userId, inviteeUser.userId)
    assert.equal(invitation.data.type, 'invitation')
    assert.equal(invitation.data.status, 'pending')
  }).timeout(15000)

  test('invited user can respond to invitation', async ({ client, assert }) => {
    const { authToken } = await createTestUser('groupowner')

    // Create a group
    const groupData = {
      name: 'Response Test Group',
      description: 'Group for testing invitation responses'
    }

    const groupResponse = await client
      .post('/api/v1/groups')
      .bearerToken(authToken)
      .json(groupData)

    groupResponse.assertStatus(201)
    const group = groupResponse.body().data

    // Create and login second user
    const { user: inviteeUser, authToken: inviteeToken } = await createTestUser('responder')

    // Send invitation
    const invitationData = {
      userId: inviteeUser.userId,
      message: 'Please join our group!'
    }

    const invitationResponse = await client
      .post(`/api/v1/groups/${group.id}/invitations`)
      .bearerToken(authToken)
      .json(invitationData)

    invitationResponse.assertStatus(201)
    const invitation = invitationResponse.body().data

    // Accept the invitation
    const acceptResponse = await client
      .put(`/api/v1/groups/invitations/${invitation.id}`)
      .bearerToken(inviteeToken)
      .json({ status: 'accepted' })

    acceptResponse.assertStatus(200)
    const acceptedInvitation = acceptResponse.body()

    assert.equal(acceptedInvitation.status, 'success')
    assert.equal(acceptedInvitation.data.status, 'accepted')
    assert.include(acceptedInvitation.message.toLowerCase(), 'accepted')

    // Verify user is now a member of the group
    const membersResponse = await client
      .get(`/api/v1/groups/${group.id}/members`)
      .bearerToken(authToken)

    membersResponse.assertStatus(200)
    const members = membersResponse.body().data
    const inviteeMember = members.find((member: any) => member.userId === inviteeUser.userId)
    assert.exists(inviteeMember)
    assert.equal(inviteeMember.role, 'member')
  }).timeout(20000)
})