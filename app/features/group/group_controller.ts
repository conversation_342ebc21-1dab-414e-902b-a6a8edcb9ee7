import type { HttpContext } from '@adonisjs/core/http'
import GroupService from './group_service.js'
import { createSuccessResponse } from '#utils/response'
import { authorizationException } from '#utils/error'
import {
  createGroupValidator,
  updateGroupValidator,
  addMemberValidator,
  updateMemberRoleValidator,
  createInvitationValidator,
  createJoinRequestValidator,
  respondToInvitationValidator,
  groupQueryValidator,
  groupMemberQueryValidator,
  groupInvitationQueryValidator,
} from './group_validator.js'
import {
  GroupMemberRole,
  UpdateGroupMemberData,
  CreateGroupInvitationData,
  CreateJoinRequestData,
  RespondToInvitationData,
  GroupFilters,
} from '#types/group_types'

/**
 * GroupController
 *
 * Handles HTTP requests for group management including:
 * - Group CRUD operations using base controller utilities
 * - Member management
 * - Invitation and join request workflows
 * - Authorization and permission checks
 */
export default class GroupController {
  private groupService = new GroupService()
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities for groups
   */
  private async getGroupConfig() {
    await this.initBaseController()

    const groupService = this.groupService

    // Adapter wrapping groupService to conform to BaseResourceService interface
    const groupServiceAdapter = {
      // All resource objects must expose userId for the base utils
      mapOwner(group: any): any {
        if (group && !group.userId) group.userId = group.ownerUserId
        return group
      },

      async getResourceById(id: string): Promise<any> {
        const group = await groupService.getGroupById(id)
        return this.mapOwner(group)
      },

      async getResources(userId: string, options: any): Promise<any> {
        const groups = await groupService.getGroupsByUserId(userId, options)
        return {
          data: groups.map((group: any) => this.mapOwner(group)),
          pagination: undefined, // TODO: Implement pagination in service
        }
      },

      async deleteResource(id: string, userId: string): Promise<boolean> {
        await groupService.deleteGroup(id, userId)
        return true
      },

      async createResource(userId: string, data: any): Promise<any> {
        const group = await groupService.createGroup(userId, {
          name: data.name,
          description: data.description,
        })
        return this.mapOwner(group)
      },

      async updateResource(id: string, data: any): Promise<any> {
        const group = await groupService.updateGroup(id, data.__userId, {
          name: data.name,
          description: data.description,
        })
        return this.mapOwner(group)
      },
    }

    return {
      service: groupServiceAdapter,
      resourceName: 'group',
      validateQueryParams: async (request: any) => await request.validateUsing(groupQueryValidator),
      getStatusMapping: () => undefined, // Groups don't have status enum
      getSortMapping: () => ({
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
        name: 'name',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<GroupFilters>) => {
        if (queryParams.ownerId) {
          filters.ownerId = queryParams.ownerId
        }
        if (queryParams.memberUserId) {
          filters.memberUserId = queryParams.memberUserId
        }
        if (queryParams.role) {
          filters.role = queryParams.role
        }
      },
      validateCreatePayload: async (request: any) =>
        await request.validateUsing(createGroupValidator),
      validateUpdatePayload: async (request: any) =>
        await request.validateUsing(updateGroupValidator),
    }
  }

  /**
   * Create a new group using base controller
   * POST /api/v1/groups
   */
  store = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createStoreHandler(await this.getGroupConfig())
    return handler(ctx)
  }

  /**
   * List groups for the authenticated user using base controller
   * GET /api/v1/groups
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getGroupConfig())
    return handler(ctx)
  }

  /**
   * Get a specific group by ID using base controller with permission check
   * GET /api/v1/groups/:id
   */
  show = async (ctx: HttpContext) => {
    const { auth, params } = ctx
    const user = auth.getUserOrFail()
    const { id } = params

    // Check if user has permission to view this group
    const canView = await this.groupService.checkGroupPermission(id, user.id, 'view')
    if (!canView) {
      throw authorizationException('You do not have permission to view this group')
    }

    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getGroupConfig())
    return handler(ctx)
  }

  /**
   * Update group details using base controller
   * PUT /api/v1/groups/:id
   */
  update = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createUpdateHandler(await this.getGroupConfig())
    return handler(ctx)
  }

  /**
   * Delete a group using base controller
   * DELETE /api/v1/groups/:id
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getGroupConfig())
    return handler(ctx)
  }

  // ===== Member Management =====

  /**
   * Get group members
   * GET /api/v1/groups/:id/members
   */
  async getMembers({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id } = params
    const queryParams = await request.validateUsing(groupMemberQueryValidator)

    // Check if user has permission to view group members
    const canView = await this.groupService.checkGroupPermission(id, user.id, 'view')
    if (!canView) {
      throw authorizationException('You do not have permission to view group members')
    }

    const options = {
      queryParams: {
        filters: {
          role: queryParams.role,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'asc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const members = await this.groupService.getGroupMembers(id, options)

    return response.ok(
      createSuccessResponse({
        data: members,
        message: 'Group members retrieved successfully',
      })
    )
  }

  /**
   * Add a member to the group
   * POST /api/v1/groups/:id/members
   */
  async addMember({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id } = params
    const payload = await request.validateUsing(addMemberValidator)

    // Check if user has permission to manage members
    const canManage = await this.groupService.checkGroupPermission(id, user.id, 'manage_members')
    if (!canManage) {
      throw authorizationException('You do not have permission to add members to this group')
    }

    const role = payload.role || GroupMemberRole.MEMBER
    const member = await this.groupService.addMember(id, payload.userId, role)

    return response.created(
      createSuccessResponse({
        data: member,
        message: 'Member added successfully',
      })
    )
  }

  /**
   * Update a member's role
   * PUT /api/v1/groups/:id/members/:userId
   */
  async updateMemberRole({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id, userId } = params
    const payload = await request.validateUsing(updateMemberRoleValidator)

    const data: UpdateGroupMemberData = {
      role: payload.role,
    }

    const member = await this.groupService.updateMemberRole(id, userId, user.id, data)

    return response.ok(
      createSuccessResponse({
        data: member,
        message: 'Member role updated successfully',
      })
    )
  }

  /**
   * Remove a member from the group
   * DELETE /api/v1/groups/:id/members/:userId
   */
  async removeMember({ params, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id, userId } = params

    await this.groupService.removeMember(id, userId, user.id)

    return response.ok(
      createSuccessResponse({
        message: 'Member removed successfully',
      })
    )
  }

  // ===== Invitation Management =====

  /**
   * Get invitations/requests for a group
   * GET /api/v1/groups/:id/invitations
   */
  async getInvitations({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id } = params
    const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    // Check if user has permission to manage members (to see invitations)
    const canManage = await this.groupService.checkGroupPermission(id, user.id, 'manage_members')
    if (!canManage) {
      throw authorizationException('You do not have permission to view group invitations')
    }

    const options = {
      queryParams: {
        filters: {
          type: queryParams.type,
          status: queryParams.status,
          userId: queryParams.userId,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'desc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const invitations = await this.groupService.getGroupInvitations(id, options)

    return response.ok(
      createSuccessResponse({
        data: invitations,
        message: 'Group invitations retrieved successfully',
      })
    )
  }

  /**
   * Create an invitation (admin/owner invites a user)
   * POST /api/v1/groups/:id/invitations
   */
  async createInvitation({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id } = params
    const payload = await request.validateUsing(createInvitationValidator)

    const data: CreateGroupInvitationData = {
      userId: payload.userId,
      message: payload.message,
    }

    const invitation = await this.groupService.createInvitation(id, payload.userId, user.id, data)

    return response.created(
      createSuccessResponse({
        data: invitation,
        message: 'Invitation sent successfully',
      })
    )
  }

  /**
   * Create a join request (user requests to join a group)
   * POST /api/v1/groups/:id/join-requests
   */
  async createJoinRequest({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { id } = params
    const payload = await request.validateUsing(createJoinRequestValidator)

    const data: CreateJoinRequestData = {
      message: payload.message,
    }

    const joinRequest = await this.groupService.createJoinRequest(id, user.id, data)

    return response.created(
      createSuccessResponse({
        data: joinRequest,
        message: 'Join request submitted successfully',
      })
    )
  }

  /**
   * Respond to an invitation or join request
   * PUT /api/v1/groups/invitations/:invitationId
   */
  async respondToInvitation({ params, request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const { invitationId } = params
    const payload = await request.validateUsing(respondToInvitationValidator)

    const data: RespondToInvitationData = {
      status: payload.status,
    }

    const invitation = await this.groupService.respondToInvitation(invitationId, user.id, data)

    const action = payload.status === 'accepted' ? 'accepted' : 'declined'
    const message = invitation.isInvitation()
      ? `Invitation ${action} successfully`
      : `Join request ${action} successfully`

    return response.ok(
      createSuccessResponse({
        data: invitation,
        message,
      })
    )
  }

  // ===== User-specific endpoints =====

  /**
   * Get invitations received by the authenticated user
   * GET /api/v1/groups/my-invitations
   */
  async getMyInvitations({ response }: HttpContext) {
    // TODO: Implement service method to get invitations for a specific user
    // const user = auth.getUserOrFail()
    // const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    return response.ok(
      createSuccessResponse({
        data: [],
        message: 'User invitations retrieved successfully',
      })
    )
  }

  /**
   * Get join requests sent by the authenticated user
   * GET /api/v1/groups/my-requests
   */
  async getMyRequests({ response }: HttpContext) {
    // TODO: Implement service method to get requests for a specific user
    // const user = auth.getUserOrFail()
    // const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    return response.ok(
      createSuccessResponse({
        data: [],
        message: 'User join requests retrieved successfully',
      })
    )
  }
}
