import { inject } from '@adonisjs/core'
import GroupMember from './group_member_model.js'
import UserGroup from '../groups/group_model.js'
import User from '#features/user/user_model'
import { UpdateGroupMemberData, GroupMemberRole, GetGroupMembersOptions } from '#types/group_types'
import { authorizationException, notFoundException, validationException } from '#utils/error'

/**
 * GroupMemberService
 *
 * Handles business logic for group member management:
 * - Adding/removing members
 * - Updating member roles
 * - Permission checks for member operations
 */
@inject()
export default class GroupMemberService {
  /**
   * Add a member to a group
   */
  async addMember(groupId: string, userId: string, role: GroupMemberRole): Promise<GroupMember> {
    // Check if user exists
    const user = await User.find(userId)
    if (!user) {
      throw notFoundException('User not found')
    }

    // Check if group exists
    const group = await UserGroup.find(groupId)
    if (!group) {
      throw notFoundException('Group not found')
    }

    // Check if user is already a member
    const existingMember = await GroupMember.query()
      .where('groupId', groupId)
      .where('userId', userId)
      .first()

    if (existingMember) {
      throw validationException('User is already a member of this group')
    }

    // Create the membership
    return GroupMember.create({
      groupId,
      userId,
      role,
    })
  }

  /**
   * Update a member's role (only admins/owners can do this)
   */
  async updateMemberRole(
    groupId: string,
    targetUserId: string,
    requestingUserId: string,
    data: UpdateGroupMemberData
  ): Promise<GroupMember> {
    // Get the requesting user's membership
    const requestingMember = await this.getMemberByUserId(groupId, requestingUserId)

    // Get the target member
    const targetMember = await this.getMemberByUserId(groupId, targetUserId)

    // Check permissions
    if (!requestingMember.canModifyMember(targetMember.role)) {
      throw authorizationException('Insufficient permissions to modify this member')
    }

    if (!requestingMember.canChangeRoleTo(data.role)) {
      throw authorizationException('Insufficient permissions to assign this role')
    }

    // Update the role
    targetMember.role = data.role
    await targetMember.save()

    return targetMember
  }

  /**
   * Remove a member from a group
   */
  async removeMember(
    groupId: string,
    targetUserId: string,
    requestingUserId: string
  ): Promise<void> {
    // Get the requesting user's membership
    const requestingMember = await this.getMemberByUserId(groupId, requestingUserId)

    // Get the target member
    const targetMember = await this.getMemberByUserId(groupId, targetUserId)

    // Check permissions (owners cannot be removed, members can remove themselves)
    if (targetMember.isOwner()) {
      throw authorizationException('Group owner cannot be removed')
    }

    if (targetUserId !== requestingUserId && !requestingMember.canManageMembers()) {
      throw authorizationException('Insufficient permissions to remove this member')
    }

    // Soft delete the membership
    await targetMember.delete()
  }

  /**
   * Get group members
   */
  async getGroupMembers(
    groupId: string,
    options: GetGroupMembersOptions = {}
  ): Promise<GroupMember[]> {
    const query = GroupMember.query().where('groupId', groupId).preload('user')

    // Apply filters
    if (options.queryParams?.filters?.role) {
      query.where('role', options.queryParams.filters.role)
    }

    // Apply sorting
    if (options.queryParams?.sort?.field) {
      query.orderBy(options.queryParams.sort.field, options.queryParams.sort.direction || 'asc')
    } else {
      query.orderBy('createdAt', 'asc')
    }

    return query.exec()
  }

  /**
   * Get a specific member by user ID
   */
  async getMemberByUserId(groupId: string, userId: string): Promise<GroupMember> {
    const member = await GroupMember.query()
      .where('groupId', groupId)
      .where('userId', userId)
      .first()

    if (!member) {
      throw notFoundException('User is not a member of this group')
    }

    return member
  }

  /**
   * Check if a user can perform an action on group members
   */
  async checkMemberPermission(
    groupId: string,
    userId: string,
    action: 'view' | 'manage_members'
  ): Promise<boolean> {
    try {
      // Check if group exists
      const group = await UserGroup.find(groupId)
      if (!group) {
        return false
      }

      // Owner can do everything
      if (group.isOwnedBy(userId)) {
        return true
      }

      // For other actions, check membership
      const member = await this.getMemberByUserId(groupId, userId)

      switch (action) {
        case 'view':
          return true // Any member can view
        case 'manage_members':
          return member.canManageMembers()
        default:
          return false
      }
    } catch {
      return false
    }
  }
}
