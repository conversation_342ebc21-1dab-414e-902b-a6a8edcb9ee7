import vine from '@vinejs/vine'
import { GROUP_MEMBER_ROLE_VALUES } from '#types/group_types'

/**
 * Validator for adding a member to a group
 */
export const addMemberValidator = vine.compile(
  vine.object({
    userId: vine.string().uuid(),
    role: vine.enum(GROUP_MEMBER_ROLE_VALUES).optional(),
  })
)

/**
 * Validator for updating a member's role
 */
export const updateMemberRoleValidator = vine.compile(
  vine.object({
    role: vine.enum(GROUP_MEMBER_ROLE_VALUES),
  })
)

/**
 * Validator for group member query parameters
 */
export const groupMemberQueryValidator = vine.compile(
  vine.object({
    // Filters
    role: vine.enum(GROUP_MEMBER_ROLE_VALUES).optional(),
    search: vine.string().trim().optional(),

    // Sorting
    sortBy: vine.enum(['createdAt', 'updatedAt', 'role']).optional(),
    sortDirection: vine.enum(['asc', 'desc']).optional(),

    // Pagination
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)
