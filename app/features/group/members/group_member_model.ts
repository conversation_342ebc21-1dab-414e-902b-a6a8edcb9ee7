import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
// Using lazy loading to avoid circular imports
import { GroupMemberRole } from '#types/group_types'

/**
 * GroupMember Model
 *
 * Represents a user's membership in a group with a specific role.
 * Tracks the relationship between users and groups including their permissions level.
 *
 * Based on the group_member table in the ERD.
 */
export default class GroupMember extends compose(BaseModel, SoftDeletes) {
  static table = 'group_member'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare groupId: string

  @column()
  declare userId: string

  @column()
  declare role: GroupMemberRole

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Note: Group relationship will be handled through service layer
  // to avoid circular import issues during initial implementation

  /**
   * Relationship to the user who is a member
   */
  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  /**
   * Check if the member has a specific role
   */
  public hasRole(role: GroupMemberRole): boolean {
    return this.role === role
  }

  /**
   * Check if the member is the owner of the group
   */
  public isOwner(): boolean {
    return this.role === GroupMemberRole.OWNER
  }

  /**
   * Check if the member is an admin (owner or admin role)
   */
  public isAdmin(): boolean {
    return this.role === GroupMemberRole.OWNER || this.role === GroupMemberRole.ADMIN
  }

  /**
   * Check if the member is a regular member (not admin or owner)
   */
  public isMember(): boolean {
    return this.role === GroupMemberRole.MEMBER
  }

  /**
   * Check if the member can manage other members (invite, remove, change roles)
   */
  public canManageMembers(): boolean {
    return this.isAdmin()
  }

  /**
   * Check if the member can delete the group
   */
  public canDeleteGroup(): boolean {
    return this.isOwner()
  }

  /**
   * Check if the member can update group details
   */
  public canUpdateGroup(): boolean {
    return this.isAdmin()
  }

  /**
   * Check if the member can promote/demote another member to a specific role
   */
  public canChangeRoleTo(targetRole: GroupMemberRole): boolean {
    // Only owners can assign owner role
    if (targetRole === GroupMemberRole.OWNER) {
      return this.isOwner()
    }

    // Admins and owners can assign admin and member roles
    if (targetRole === GroupMemberRole.ADMIN || targetRole === GroupMemberRole.MEMBER) {
      return this.isAdmin()
    }

    return false
  }

  /**
   * Check if this member can modify another member's role
   */
  public canModifyMember(targetMemberRole: GroupMemberRole): boolean {
    // Owners can modify anyone except other owners
    if (this.isOwner()) {
      return targetMemberRole !== GroupMemberRole.OWNER
    }

    // Admins can only modify regular members
    if (this.role === GroupMemberRole.ADMIN) {
      return targetMemberRole === GroupMemberRole.MEMBER
    }

    // Regular members cannot modify anyone
    return false
  }
}
