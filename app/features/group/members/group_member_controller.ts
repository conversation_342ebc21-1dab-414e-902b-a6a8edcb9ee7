import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import GroupMemberService from './group_member_service.js'
import { createSuccessResponse } from '#utils/response'
import { authorizationException } from '#utils/error'
import {
  addMemberValidator,
  updateMemberRoleValidator,
  groupMemberQueryValidator,
} from './group_member_validator.js'
import { GroupMemberRole, UpdateGroupMemberData } from '#types/group_types'

/**
 * GroupMemberController
 *
 * Handles HTTP requests for group member management:
 * - Adding/removing members
 * - Updating member roles
 * - Listing group members
 */
@inject()
export default class GroupMemberController {
  constructor(private groupMemberService: GroupMemberService) {}

  /**
   * Get group members
   * GET /api/v1/groups/:id/members
   */
  async index({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { id: groupId } = params
    const queryParams = await request.validateUsing(groupMemberQueryValidator)

    // Check if user has permission to view group members
    const canView = await this.groupMemberService.checkMemberPermission(groupId, userId, 'view')
    if (!canView) {
      throw authorizationException('You do not have permission to view group members')
    }

    const options = {
      queryParams: {
        filters: {
          role: queryParams.role,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'asc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const members = await this.groupMemberService.getGroupMembers(groupId, options)

    return response.ok(
      createSuccessResponse({
        data: members,
        message: 'Group members retrieved successfully',
      })
    )
  }

  /**
   * Add a member to the group
   * POST /api/v1/groups/:id/members
   */
  async store({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { id: groupId } = params
    const payload = await request.validateUsing(addMemberValidator)

    // Check if user has permission to manage members
    const canManage = await this.groupMemberService.checkMemberPermission(
      groupId,
      userId,
      'manage_members'
    )
    if (!canManage) {
      throw authorizationException('You do not have permission to add members to this group')
    }

    const role = payload.role || GroupMemberRole.MEMBER
    const member = await this.groupMemberService.addMember(groupId, payload.userId, role)

    return response.created(
      createSuccessResponse({
        data: member,
        message: 'Member added successfully',
      })
    )
  }

  /**
   * Update a member's role
   * PUT /api/v1/groups/:id/members/:userId
   */
  async update({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const currentUserId = await authUser.getUserId()
    const { id: groupId, userId } = params
    const payload = await request.validateUsing(updateMemberRoleValidator)

    const data: UpdateGroupMemberData = {
      role: payload.role,
    }

    const member = await this.groupMemberService.updateMemberRole(groupId, userId, currentUserId, data)

    return response.ok(
      createSuccessResponse({
        data: member,
        message: 'Member role updated successfully',
      })
    )
  }

  /**
   * Remove a member from the group
   * DELETE /api/v1/groups/:id/members/:userId
   */
  async destroy({ params, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const currentUserId = await authUser.getUserId()
    const { id: groupId, userId } = params

    await this.groupMemberService.removeMember(groupId, userId, currentUserId)

    return response.ok(
      createSuccessResponse({
        message: 'Member removed successfully',
      })
    )
  }
}
