import vine from '@vinejs/vine'
import {
  GroupInvitationRequestStatus,
  GROUP_INVITATION_REQUEST_TYPE_VALUES,
  GROUP_INVITATION_REQUEST_STATUS_VALUES,
} from '#types/group_types'

/**
 * Validator for creating an invitation
 */
export const createInvitationValidator = vine.compile(
  vine.object({
    userId: vine.string().uuid(),
    message: vine.string().trim().maxLength(500).optional(),
  })
)

/**
 * Validator for creating a join request
 */
export const createJoinRequestValidator = vine.compile(
  vine.object({
    message: vine.string().trim().maxLength(500).optional(),
  })
)

/**
 * Validator for responding to invitations/requests
 */
export const respondToInvitationValidator = vine.compile(
  vine.object({
    status: vine.enum([
      GroupInvitationRequestStatus.ACCEPTED,
      GroupInvitationRequestStatus.DECLINED,
    ]),
  })
)

/**
 * Validator for group invitation query parameters
 */
export const groupInvitationQueryValidator = vine.compile(
  vine.object({
    // Filters
    type: vine.enum(GROUP_INVITATION_REQUEST_TYPE_VALUES).optional(),
    status: vine.enum(GROUP_INVITATION_REQUEST_STATUS_VALUES).optional(),
    userId: vine.string().uuid().optional(),
    search: vine.string().trim().optional(),

    // Sorting
    sortBy: vine.enum(['createdAt', 'updatedAt', 'status', 'type']).optional(),
    sortDirection: vine.enum(['asc', 'desc']).optional(),

    // Pagination
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)
