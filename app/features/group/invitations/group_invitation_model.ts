import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
// Using lazy loading to avoid circular imports
import { GroupInvitationRequestType, GroupInvitationRequestStatus } from '#types/group_types'

/**
 * GroupInvitationRequest Model
 *
 * Represents both invitations (group admin/owner invites a user) and
 * join requests (user requests to join a group). The type field distinguishes
 * between these two scenarios.
 *
 * Based on the group_invitation_request table in the ERD.
 */
export default class GroupInvitationRequest extends compose(BaseModel, SoftDeletes) {
  static table = 'group_invitation_request'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare groupId: string

  @column()
  declare userId: string

  @column()
  declare type: GroupInvitationRequestType

  @column()
  declare status: GroupInvitationRequestStatus

  @column()
  declare initiatorUserId: string

  @column()
  declare message: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Note: Group relationship will be handled through service layer
  // to avoid circular import issues during initial implementation

  /**
   * Relationship to the user who is being invited or requesting to join
   */
  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  /**
   * Relationship to the user who initiated this invitation/request
   */
  @belongsTo(() => User, { foreignKey: 'initiatorUserId' })
  declare initiator: BelongsTo<typeof User>

  /**
   * Check if this is an invitation (admin/owner inviting a user)
   */
  public isInvitation(): boolean {
    return this.type === GroupInvitationRequestType.INVITATION
  }

  /**
   * Check if this is a join request (user requesting to join)
   */
  public isJoinRequest(): boolean {
    return this.type === GroupInvitationRequestType.REQUEST
  }

  /**
   * Check if the invitation/request is pending
   */
  public isPending(): boolean {
    return this.status === GroupInvitationRequestStatus.PENDING
  }

  /**
   * Check if the invitation/request was accepted
   */
  public isAccepted(): boolean {
    return this.status === GroupInvitationRequestStatus.ACCEPTED
  }

  /**
   * Check if the invitation/request was declined
   */
  public isDeclined(): boolean {
    return this.status === GroupInvitationRequestStatus.DECLINED
  }

  /**
   * Check if the invitation/request can be responded to
   */
  public canBeRespondedTo(): boolean {
    return this.isPending()
  }

  /**
   * Check if a specific user can respond to this invitation/request
   */
  public canBeRespondedToBy(userId: string): boolean {
    if (!this.canBeRespondedTo()) return false

    // For invitations, the invited user can respond
    if (this.isInvitation()) {
      return this.userId === userId
    }

    // For join requests, group admins/owners can respond
    // Note: This would require loading the group and checking membership
    // For now, we'll handle this logic in the service layer
    return false
  }

  /**
   * Get the target user ID (who the invitation/request is for)
   */
  public getTargetUserId(): string {
    return this.userId
  }

  /**
   * Get the initiator user ID (who created the invitation/request)
   */
  public getInitiatorUserId(): string {
    return this.initiatorUserId
  }

  /**
   * Check if this invitation/request involves a specific user
   * (either as target or initiator)
   */
  public involvesUser(userId: string): boolean {
    return this.userId === userId || this.initiatorUserId === userId
  }

  /**
   * Get a human-readable description of the invitation/request
   */
  public getDescription(): string {
    if (this.isInvitation()) {
      return `Invitation to join group`
    } else {
      return `Request to join group`
    }
  }
}
