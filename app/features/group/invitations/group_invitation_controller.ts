import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import GroupInvitationService from './group_invitation_service.js'
import { createSuccessResponse } from '#utils/response'
import { authorizationException } from '#utils/error'
import {
  createInvitationValidator,
  createJoinRequestValidator,
  respondToInvitationValidator,
  groupInvitationQueryValidator,
} from './group_invitation_validator.js'
import {
  CreateGroupInvitationData,
  CreateJoinRequestData,
  RespondToInvitationData,
} from '#types/group_types'

/**
 * GroupInvitationController
 *
 * Handles HTTP requests for group invitation and join request management:
 * - Creating invitations (admin/owner invites user)
 * - Creating join requests (user requests to join)
 * - Responding to invitations/requests
 * - Listing invitations/requests
 */
@inject()
export default class GroupInvitationController {
  constructor(private groupInvitationService: GroupInvitationService) {}

  /**
   * Get invitations/requests for a group
   * GET /api/v1/groups/:id/invitations
   */
  async index({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { id: groupId } = params
    const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    // Check if user has permission to manage members (to see invitations)
    const canManage = await this.groupInvitationService.checkInvitationPermission(
      groupId,
      userId,
      'manage'
    )
    if (!canManage) {
      throw authorizationException('You do not have permission to view group invitations')
    }

    const options = {
      queryParams: {
        filters: {
          type: queryParams.type,
          status: queryParams.status,
          userId: queryParams.userId,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'desc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const invitations = await this.groupInvitationService.getGroupInvitations(groupId, options)

    return response.ok(
      createSuccessResponse({
        data: invitations,
        message: 'Group invitations retrieved successfully',
      })
    )
  }

  /**
   * Create an invitation (admin/owner invites a user)
   * POST /api/v1/groups/:id/invitations
   */
  async store({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { id: groupId } = params
    const payload = await request.validateUsing(createInvitationValidator)

    const data: CreateGroupInvitationData = {
      userId: payload.userId,
      message: payload.message,
    }

    const invitation = await this.groupInvitationService.createInvitation(
      groupId,
      payload.userId,
      userId,
      data
    )

    return response.created(
      createSuccessResponse({
        data: invitation,
        message: 'Invitation sent successfully',
      })
    )
  }

  /**
   * Create a join request (user requests to join a group)
   * POST /api/v1/groups/:id/join-requests
   */
  async createJoinRequest({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { id: groupId } = params
    const payload = await request.validateUsing(createJoinRequestValidator)

    const data: CreateJoinRequestData = {
      message: payload.message,
    }

    const joinRequest = await this.groupInvitationService.createJoinRequest(groupId, userId, data)

    return response.created(
      createSuccessResponse({
        data: joinRequest,
        message: 'Join request submitted successfully',
      })
    )
  }

  /**
   * Respond to an invitation or join request
   * PUT /api/v1/groups/invitations/:invitationId
   */
  async update({ params, request, response, auth }: HttpContext) {
    const authUser = auth.getUserOrFail()
    const userId = await authUser.getUserId()
    const { invitationId } = params
    const payload = await request.validateUsing(respondToInvitationValidator)

    const data: RespondToInvitationData = {
      status: payload.status,
    }

    const invitation = await this.groupInvitationService.respondToInvitation(
      invitationId,
      userId,
      data
    )

    const action = payload.status === 'accepted' ? 'accepted' : 'declined'
    const message = invitation.isInvitation()
      ? `Invitation ${action} successfully`
      : `Join request ${action} successfully`

    return response.ok(
      createSuccessResponse({
        data: invitation,
        message,
      })
    )
  }

  /**
   * Get invitations received by the authenticated user
   * GET /api/v1/groups/my-invitations
   */
  async getMyInvitations({ request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    const options = {
      queryParams: {
        filters: {
          type: queryParams.type,
          status: queryParams.status,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'desc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const userId = await user.getUserId()
    const invitations = await this.groupInvitationService.getUserInvitations(userId, options)

    return response.ok(
      createSuccessResponse({
        data: invitations,
        message: 'User invitations retrieved successfully',
      })
    )
  }

  /**
   * Get join requests sent by the authenticated user
   * GET /api/v1/groups/my-requests
   */
  async getMyRequests({ request, response, auth }: HttpContext) {
    const user = auth.getUserOrFail()
    const queryParams = await request.validateUsing(groupInvitationQueryValidator)

    const options = {
      queryParams: {
        filters: {
          type: queryParams.type,
          status: queryParams.status,
          search: queryParams.search,
        },
        sort: queryParams.sortBy
          ? {
              field: queryParams.sortBy,
              direction: queryParams.sortDirection || 'desc',
            }
          : undefined,
        pagination: queryParams.page
          ? {
              page: queryParams.page,
              limit: queryParams.limit || 25,
            }
          : undefined,
      },
    }

    const userId = await user.getUserId()
    const requests = await this.groupInvitationService.getUserRequests(userId, options)

    return response.ok(
      createSuccessResponse({
        data: requests,
        message: 'User join requests retrieved successfully',
      })
    )
  }
}
