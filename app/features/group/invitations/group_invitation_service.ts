import { inject } from '@adonisjs/core'
import GroupInvitationRequest from './group_invitation_model.js'
import GroupMember from '../members/group_member_model.js'
import UserGroup from '../groups/group_model.js'
import User from '#features/user/user_model'
import {
  CreateGroupInvitationData,
  CreateJoinRequestData,
  RespondToInvitationData,
  GroupMemberRole,
  GroupInvitationRequestType,
  GroupInvitationRequestStatus,
  GetGroupInvitationsOptions,
} from '#types/group_types'
import { authorizationException, notFoundException, validationException } from '#utils/error'

/**
 * GroupInvitationService
 *
 * Handles business logic for group invitation and join request management:
 * - Creating invitations (admin/owner invites user)
 * - Creating join requests (user requests to join)
 * - Responding to invitations/requests
 * - Permission checks for invitation operations
 */
@inject()
export default class GroupInvitationService {
  /**
   * Create an invitation (admin/owner invites a user)
   */
  async createInvitation(
    groupId: string,
    targetUserId: string,
    initiatorUserId: string,
    data: CreateGroupInvitationData
  ): Promise<GroupInvitationRequest> {
    // Verify group exists
    const group = await UserGroup.find(groupId)
    if (!group) {
      throw notFoundException('Group not found')
    }

    // Check if initiator has permission to invite
    const initiatorMember = await this.getMemberByUserId(groupId, initiatorUserId)
    if (!initiatorMember.canManageMembers()) {
      throw authorizationException('Insufficient permissions to invite members')
    }

    // Check if target user exists
    const targetUser = await User.find(targetUserId)
    if (!targetUser) {
      throw notFoundException('Target user not found')
    }

    // Check if user is already a member
    const existingMember = await GroupMember.query()
      .where('groupId', groupId)
      .where('userId', targetUserId)
      .first()

    if (existingMember) {
      throw validationException('User is already a member of this group')
    }

    // Check for existing pending invitation
    const existingInvitation = await GroupInvitationRequest.query()
      .where('groupId', groupId)
      .where('userId', targetUserId)
      .where('type', GroupInvitationRequestType.INVITATION)
      .where('status', GroupInvitationRequestStatus.PENDING)
      .first()

    if (existingInvitation) {
      throw validationException('User already has a pending invitation')
    }

    // Create the invitation
    return GroupInvitationRequest.create({
      groupId,
      userId: targetUserId,
      type: GroupInvitationRequestType.INVITATION,
      status: GroupInvitationRequestStatus.PENDING,
      initiatorUserId,
      message: data.message || null,
    })
  }

  /**
   * Create a join request (user requests to join a group)
   */
  async createJoinRequest(
    groupId: string,
    requestingUserId: string,
    data: CreateJoinRequestData
  ): Promise<GroupInvitationRequest> {
    // Verify group exists
    const group = await UserGroup.find(groupId)
    if (!group) {
      throw notFoundException('Group not found')
    }

    // Check if user is already a member
    const existingMember = await GroupMember.query()
      .where('groupId', groupId)
      .where('userId', requestingUserId)
      .first()

    if (existingMember) {
      throw validationException('You are already a member of this group')
    }

    // Check for existing pending request
    const existingRequest = await GroupInvitationRequest.query()
      .where('groupId', groupId)
      .where('userId', requestingUserId)
      .where('type', GroupInvitationRequestType.REQUEST)
      .where('status', GroupInvitationRequestStatus.PENDING)
      .first()

    if (existingRequest) {
      throw validationException('You already have a pending join request')
    }

    // Create the join request
    return GroupInvitationRequest.create({
      groupId,
      userId: requestingUserId,
      type: GroupInvitationRequestType.REQUEST,
      status: GroupInvitationRequestStatus.PENDING,
      initiatorUserId: requestingUserId,
      message: data.message || null,
    })
  }

  /**
   * Respond to an invitation or join request
   */
  async respondToInvitation(
    invitationId: string,
    respondingUserId: string,
    data: RespondToInvitationData
  ): Promise<GroupInvitationRequest> {
    const invitation = await GroupInvitationRequest.find(invitationId)
    if (!invitation) {
      throw notFoundException('Invitation/request not found')
    }

    if (!invitation.canBeRespondedTo()) {
      throw validationException('This invitation/request cannot be responded to')
    }

    // Check permissions
    if (invitation.isInvitation()) {
      // For invitations, only the invited user can respond
      if (invitation.userId !== respondingUserId) {
        throw authorizationException('Only the invited user can respond to this invitation')
      }
    } else {
      // For join requests, only group admins/owners can respond
      const respondingMember = await this.getMemberByUserId(invitation.groupId, respondingUserId)
      if (!respondingMember.canManageMembers()) {
        throw authorizationException('Insufficient permissions to respond to this request')
      }
    }

    // Update the status
    invitation.status = data.status
    await invitation.save()

    // If accepted, add the user as a member
    if (data.status === GroupInvitationRequestStatus.ACCEPTED) {
      await GroupMember.create({
        groupId: invitation.groupId,
        userId: invitation.userId,
        role: GroupMemberRole.MEMBER,
      })
    }

    return invitation
  }

  /**
   * Get invitations/requests for a group
   */
  async getGroupInvitations(
    groupId: string,
    options: GetGroupInvitationsOptions = {}
  ): Promise<GroupInvitationRequest[]> {
    const query = GroupInvitationRequest.query()
      .where('groupId', groupId)
      .preload('user')
      .preload('initiator')

    // Apply filters
    if (options.queryParams?.filters?.type) {
      query.where('type', options.queryParams.filters.type)
    }
    if (options.queryParams?.filters?.status) {
      query.where('status', options.queryParams.filters.status)
    }
    if (options.queryParams?.filters?.userId) {
      query.where('userId', options.queryParams.filters.userId)
    }

    // Apply sorting
    if (options.queryParams?.sort?.field) {
      query.orderBy(options.queryParams.sort.field, options.queryParams.sort.direction || 'asc')
    } else {
      query.orderBy('createdAt', 'desc')
    }

    return query.exec()
  }

  /**
   * Get invitations received by a user
   */
  async getUserInvitations(
    userId: string,
    options: GetGroupInvitationsOptions = {}
  ): Promise<GroupInvitationRequest[]> {
    const query = GroupInvitationRequest.query()
      .where('userId', userId)
      .where('type', GroupInvitationRequestType.INVITATION)
      .preload('user')
      .preload('initiator')

    // Apply filters
    if (options.queryParams?.filters?.status) {
      query.where('status', options.queryParams.filters.status)
    }

    // Apply sorting
    if (options.queryParams?.sort?.field) {
      query.orderBy(options.queryParams.sort.field, options.queryParams.sort.direction || 'asc')
    } else {
      query.orderBy('createdAt', 'desc')
    }

    return query.exec()
  }

  /**
   * Get join requests sent by a user
   */
  async getUserRequests(
    userId: string,
    options: GetGroupInvitationsOptions = {}
  ): Promise<GroupInvitationRequest[]> {
    const query = GroupInvitationRequest.query()
      .where('userId', userId)
      .where('type', GroupInvitationRequestType.REQUEST)
      .preload('user')
      .preload('initiator')

    // Apply filters
    if (options.queryParams?.filters?.status) {
      query.where('status', options.queryParams.filters.status)
    }

    // Apply sorting
    if (options.queryParams?.sort?.field) {
      query.orderBy(options.queryParams.sort.field, options.queryParams.sort.direction || 'asc')
    } else {
      query.orderBy('createdAt', 'desc')
    }

    return query.exec()
  }

  /**
   * Check if a user can perform an action on group invitations
   */
  async checkInvitationPermission(
    groupId: string,
    userId: string,
    action: 'manage'
  ): Promise<boolean> {
    try {
      // Check if group exists
      const group = await UserGroup.find(groupId)
      if (!group) {
        return false
      }

      // Owner can do everything
      if (group.isOwnedBy(userId)) {
        return true
      }

      // For other actions, check membership
      const member = await this.getMemberByUserId(groupId, userId)

      switch (action) {
        case 'manage':
          return member.canManageMembers()
        default:
          return false
      }
    } catch {
      return false
    }
  }

  /**
   * Get a specific member by user ID
   */
  private async getMemberByUserId(groupId: string, userId: string): Promise<GroupMember> {
    const member = await GroupMember.query()
      .where('groupId', groupId)
      .where('userId', userId)
      .first()

    if (!member) {
      throw notFoundException('User is not a member of this group')
    }

    return member
  }
}
