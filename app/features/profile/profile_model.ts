import BaseModel from '#models/base_model'
import { column, belongsTo, hasOne, afterCreate } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasOne } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import User from '#features/user/user_model'
import ProfileSettings from './profile_settings_model.js'

export default class Profile extends compose(BaseModel, SoftDeletes) {
  static table = 'profiles'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare firstName: string

  @column()
  declare lastName: string

  @column.dateTime()
  declare birthDate: DateTime | null

  @column()
  declare profilePicture: string | null

  @column()
  declare countryCode: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @hasOne(() => ProfileSettings)
  declare settings: HasOne<typeof ProfileSettings>

  // TODO: Consider if auto-creation of profile settings is desired
  // Currently disabled to allow explicit control in tests and API usage
  // @afterCreate()
  // static async createDefaultSettings(profile: Profile) {
  //   const trx = profile.$trx
  //   const defProfileSettings = {
  //     profileId: profile.id,
  //     defaultAutoAcceptInvitations: false,
  //     globalBusyMessage: null,
  //     defaultPolicyTemplateId: null,
  //   }

  //   if (trx) {
  //     await ProfileSettings.create(defProfileSettings, { client: trx })
  //   } else {
  //     await ProfileSettings.create(defProfileSettings)
  //   }
  // }
}
