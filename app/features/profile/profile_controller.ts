import type { HttpContext } from '@adonisjs/core/http'
import { profileValidator, partialProfileValidator } from './profile_validator.js'
import ProfileService from './profile_service.js'

export default class ProfileController {
  private profileService = new ProfileService()
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig(auth?: any) {
    await this.initBaseController()

    // Adapter wrapping ProfileService to conform to BaseResourceService interface
    const profileServiceAdapter = {
      getResourceById: async (id: string): Promise<any> => {
        const profile = await this.profileService.getProfileByUserId(id)
        return profile ? profile : null
      },

      getResources: async (userId: string): Promise<any> => {
        const profile = await this.profileService.getProfileByUserId(userId)
        return {
          data: profile ? [profile.serialize()] : [],
          pagination: undefined,
        }
      },

      deleteResource: async (id: string, userId: string): Promise<boolean> => {
        await this.profileService.deleteProfile(userId)
        return true
      },

      createResource: async (userId: string, data: any): Promise<any> => {
        const profile = await this.profileService.createOrUpdate(userId, data)
        return profile
      },

      updateResource: async (id: string, data: any): Promise<any> => {
        const profile = await this.profileService.createOrUpdate(id, data)
        return profile
      },
    }

    return {
      service: profileServiceAdapter,
      resourceName: 'profile',
      validateQueryParams: async () => ({}), // Profile doesn't have complex query params
      // No custom status or sort mapping needed for profile
      validateCreatePayload: async (request: any) => await request.validateUsing(profileValidator),
      validateUpdatePayload: async (request: any) =>
        await request.validateUsing(partialProfileValidator),
    }
  }

  /**
   * Create or update profile for the authenticated user
   */
  store = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createStoreHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Get profile for the authenticated user
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig(ctx.auth), {
      isResourceIdOptional: true,
      customResourceId: await ctx?.auth?.user?.getUserId(),
    })
    return handler(ctx)
  }

  /**
   * Update profile for the authenticated user
   */
  update = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createUpdateHandler(await this.getConfig(), {
      customResourceId: ctx?.auth?.user?.getUserId(),
    })
    return handler(ctx)
  }

  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }
}
