import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import Profile from '#features/profile/profile_model'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Profile Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  test('user can create profile', async ({ client, assert }) => {
    const { authToken, userId } = await createTestUser('profileuser')

    const profileData = {
      firstName: 'Test',
      lastName: 'User',
      birthDate: '1990-01-01',
      countryCode: 'PHL',
      bio: 'Test bio',
    }

    const response = await client.post('/api/v1/profile').bearerToken(authToken).json(profileData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.firstName, profileData.firstName)
    assert.equal(body.data.lastName, profileData.lastName)

    // Verify in database
    const dbProfile = await Profile.findBy('userId', userId)
    assert.exists(dbProfile)
    assert.equal(dbProfile!.firstName, profileData.firstName)
  }).timeout(10000)

  test('user can retrieve existing profile', async ({ client, assert }) => {
    const { authToken } = await createTestUser('profileowner')

    // First create a profile
    const profileData = {
      firstName: 'Test',
      lastName: 'User',
      birthDate: '1990-01-01',
      countryCode: 'PHL',
      bio: 'Test bio for retrieval',
    }

    await client.post('/api/v1/profile').bearerToken(authToken).json(profileData)

    // Then retrieve it
    const response = await client.get('/api/v1/profile').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.firstName, profileData.firstName)
    assert.equal(body.data.lastName, profileData.lastName)
  }).timeout(10000)

  test('returns 404 when profile does not exist', async ({ client, assert }) => {
    const { authToken } = await createTestUser('noprofile')

    const response = await client.get('/api/v1/profile').bearerToken(authToken)

    response.assertStatus(404)
  }).timeout(10000)

  test('profile operations require authentication', async ({ client, assert }) => {
    const profileData = {
      firstName: 'Test',
      lastName: 'User',
      birthDate: '1990-01-01',
      countryCode: 'PHL',
    }

    const response1 = await client.post('/api/v1/profile').json(profileData)
    response1.assertStatus(401)

    const response2 = await client.get('/api/v1/profile')
    response2.assertStatus(401)
  }).timeout(10000)

  test('profile validation works', async ({ client, assert }) => {
    const { authToken } = await createTestUser('profilevalidator')

    // Test missing required fields
    const response = await client
      .post('/api/v1/profile')
      .bearerToken(authToken)
      .json({ firstName: 'Test' }) // Missing required fields

    response.assertStatus(422)
  }).timeout(10000)
})
