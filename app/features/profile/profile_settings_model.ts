import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import Profile from './profile_model.js'
import PrivacyPolicyTemplate from '../privacy-policy/template/privacy_policy_template_model.js'

export default class ProfileSettings extends compose(BaseModel, SoftDeletes) {
  static table = 'profile_settings'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare profileId: string

  @column()
  declare defaultAutoAcceptInvitations: boolean

  @column()
  declare globalBusyMessage: string | null

  @column()
  declare defaultPolicyTemplateId: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  @belongsTo(() => Profile)
  declare profile: BelongsTo<typeof Profile>

  @belongsTo(() => PrivacyPolicyTemplate, {
    foreignKey: 'defaultPolicyTemplateId',
  })
  declare defaultPolicyTemplate: BelongsTo<typeof PrivacyPolicyTemplate>
}
