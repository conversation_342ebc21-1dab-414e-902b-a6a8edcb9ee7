import vine from '@vinejs/vine'

/**
 * Validator for creating profile settings
 */
const createProfileSettingsValidator = vine.compile(
  vine.object({
    profileId: vine.string().uuid(),
    defaultAutoAcceptInvitations: vine.boolean().optional(),
    globalBusyMessage: vine.string().maxLength(255).optional(),
    defaultPolicyTemplateId: vine.string().uuid().optional(),
  })
)

/**
 * Validator for updating profile settings
 */
const updateProfileSettingsValidator = vine.compile(
  vine.object({
    defaultAutoAcceptInvitations: vine.boolean().optional(),
    globalBusyMessage: vine.string().maxLength(255).optional(),
    defaultPolicyTemplateId: vine.string().uuid().optional(),
  })
)

export { createProfileSettingsValidator, updateProfileSettingsValidator }
