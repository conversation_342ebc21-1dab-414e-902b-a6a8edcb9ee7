import vine from '@vinejs/vine'
import { DateTime } from 'luxon'

// Base schema definitions for each profile field
const profileFieldSchemas = {
  firstName: vine.string().trim().minLength(2).maxLength(100),
  lastName: vine.string().trim().minLength(2).maxLength(100),
  // Core transform for birthDate: assumes it receives a string that needs validation and conversion.
  // Null/undefined is handled by .optional() or .nullable() before this transform is called.
  birthDate: vine.string().transform((value: string) => {
    const date = DateTime.fromISO(value)
    if (!date.isValid) {
      throw new Error('Invalid date format. Please use ISO format (YYYY-MM-DD).')
    }
    return date.toISODate()
  }),
  profilePicture: vine.string().url(),
  countryCode: vine.string(),
}

/**
 * Validator for profile creation/full update
 */
const profileValidator = vine.compile(
  vine.object({
    firstName: profileFieldSchemas.firstName.clone(),
    lastName: profileFieldSchemas.lastName.clone(),
    birthDate: profileFieldSchemas.birthDate.clone().optional(),
    profilePicture: profileFieldSchemas.profilePicture.clone().optional(),
    countryCode: profileFieldSchemas.countryCode.clone().optional(),
  })
)

/**
 * Validator for partial profile update (all fields optional and nullable)
 */
const partialProfileSchemaObject = Object.fromEntries(
  Object.entries(profileFieldSchemas).map(([key, schema]) => [
    key,
    schema.clone().nullable().optional(),
  ])
) as { [K in keyof typeof profileFieldSchemas]: any } // Cast to make Vine happy, actual type is more specific

const partialProfileObjectSchema = vine.object(partialProfileSchemaObject)

const partialProfileValidator = vine.compile(partialProfileObjectSchema)

export { profileValidator, partialProfileValidator }
