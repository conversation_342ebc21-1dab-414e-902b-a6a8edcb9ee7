import BaseModel from '#models/base_model'
import { column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import User from '../../user/user_model.js'
import PrivacyPolicyTemplate from '../template/privacy_policy_template_model.js'
import { jsonArrayColumn } from '#utils/serializers'

export default class PolicyCategory extends compose(BaseModel, SoftDeletes) {
  static table = 'policy_categories'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string | null // null for system-wide contexts

  @column()
  declare key: string // "work_hours", "personal_time", etc.

  @column()
  declare label: string // "Work Hours", "Personal Time"

  @column()
  declare description: string | null

  @column(jsonArrayColumn)
  declare suggestedKeywords: string[] | null // ["meeting", "call", "presentation"]

  @column()
  declare isSystemContext: boolean

  @column()
  declare isActive: boolean

  @column()
  declare displayOrder: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @hasMany(() => PrivacyPolicyTemplate, {
    foreignKey: 'categoryId',
  })
  declare privacyPolicyTemplates: HasMany<typeof PrivacyPolicyTemplate>
}
