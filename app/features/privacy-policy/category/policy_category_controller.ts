import type { HttpContext } from '@adonisjs/core/http'
import PolicyCategoryService from './policy_category_service.js'
import {
  policyCategoryQueryValidator,
  createPolicyCategoryValidator,
  updatePolicyCategoryValidator,
} from '../policy_validators.js'
import { createSuccessResponse } from '#utils/response'

export default class PolicyCategoryController {
  private service = new PolicyCategoryService()
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.service,
      resourceName: 'policy category',
      validateQueryParams: async (request: any) =>
        await request.validateUsing(policyCategoryQueryValidator),
      getStatusMapping: () => undefined, // No status field for policy categories
      getSortMapping: () => ({
        name: 'name',
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
      }),
      applyCustomFilters: () => {
        // No additional custom filters for now
      },
    }
  }

  /**
   * List all policy categories for the authenticated user with filtering, sorting, and pagination
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Get a specific policy category by ID
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete a policy category (soft delete)
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Create a new policy category
   */
  async store({ request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'policy category')

      const data = await request.validateUsing(createPolicyCategoryValidator)

      const policyCategory = await this.service.create(userId, {
        key: data.name.toLowerCase().replace(/\s+/g, '_'),
        label: data.name,
        description: data.description,
      })

      return response.status(201).json(
        createSuccessResponse({
          data: policyCategory,
          message: 'Policy category created successfully',
        })
      )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Update an existing policy category
   */
  async update({ params, request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'policy category')

      const { id: categoryId } = params
      if (!categoryId) {
        throw new Error('Policy category ID is required')
      }

      await base.validateResourceOwnership(categoryId, userId, this.service, 'policy category')
      const data = await request.validateUsing(updatePolicyCategoryValidator)

      const updateData: any = {}
      if (data.name) {
        updateData.key = data.name.toLowerCase().replace(/\s+/g, '_')
        updateData.label = data.name
      }
      if (data.description !== undefined) {
        updateData.description = data.description
      }

      const updatedCategory = await this.service.update(categoryId, userId, updateData)

      return response.status(200).json(
        createSuccessResponse({
          data: updatedCategory,
          message: 'Policy category updated successfully',
        })
      )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }
}
