import PolicyCategory from './policy_category_model.js'
import User from '../../user/user_model.js'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import type { NonPatchableModelFields } from '#types/model_utilities'
import { notFoundException } from '#utils/error'
import { BaseResourceService } from '#controllers/base_controller'
import { PolicyCategoryFilters, PolicyCategorySort } from '../policy_types.js'
import { PaginatedResponse } from '#types/base_query_types'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

// Derive patchable attributes from the PolicyCategory model
type AllowedPolicyCategoryPatchAttributes = Omit<
  ModelAttributes<PolicyCategory>,
  NonPatchableModelFields
>

// PolicyCategoryForMerge: Represents the data structure for merging user input during policy category updates.
// It excludes critical fields that should not be directly modifiable via user input.
type PolicyCategoryPatchData = Partial<AllowedPolicyCategoryPatchAttributes>

// Extended interface for PolicyCategory with guaranteed userId (for base controller compatibility)
interface PolicyCategoryWithUser extends PolicyCategory {
  userId: string
}

/**
 * PolicyCategoryService
 *
 * Service for managing policy categories including CRUD operations
 * and system-wide category management.
 */
export default class PolicyCategoryService
  implements BaseResourceService<PolicyCategoryWithUser, PolicyCategoryFilters, PolicyCategorySort>
{
  /**
   * Create a new policy category for a user
   */
  async create(
    userId: string,
    categoryData: {
      key: string
      label: string
      description?: string | null
      suggestedKeywords?: string[] | null
      isSystemContext?: boolean
      isActive?: boolean
      displayOrder?: number
    }
  ): Promise<PolicyCategory> {
    // Verify user exists
    await User.findOrFail(userId)

    const category = await PolicyCategory.create({
      userId,
      key: categoryData.key,
      label: categoryData.label,
      description: categoryData.description || null,
      suggestedKeywords: categoryData.suggestedKeywords || null,
      isSystemContext: categoryData.isSystemContext ?? false,
      isActive: categoryData.isActive ?? true,
      displayOrder: categoryData.displayOrder ?? 0,
    })

    return category
  }

  /**
   * Get policy categories for a specific user
   */
  async getByUserId(userId: string) {
    return PolicyCategory.query()
      .where('userId', userId)
      .orderBy('displayOrder', 'asc')
      .orderBy('label', 'asc')
  }

  /**
   * Get a policy category by ID (optionally filtered by user)
   */
  async getById(id: string, userId?: string) {
    const query = PolicyCategory.query().where('id', id)

    if (userId) {
      query.where('userId', userId)
    }

    const category = await query.first()
    if (!category) {
      throw notFoundException('Policy category not found')
    }

    return category
  }

  /**
   * Update a policy category
   */
  async update(id: string, userId: string, data: PolicyCategoryPatchData) {
    const category = await PolicyCategory.query().where('id', id).where('userId', userId).first()

    if (!category) {
      throw notFoundException('Policy category not found')
    }

    category.merge(data)
    await category.save()

    return category
  }

  /**
   * Delete a policy category
   */
  async delete(id: string, userId: string) {
    const category = await PolicyCategory.query().where('id', id).where('userId', userId).first()

    if (!category) {
      throw notFoundException('Policy category not found')
    }

    await category.delete()
    return true
  }

  /**
   * Get system-wide policy categories
   */
  async getSystemCategories() {
    return PolicyCategory.query()
      .whereNull('userId')
      .where('isSystemContext', true)
      .where('isActive', true)
      .orderBy('displayOrder', 'asc')
      .orderBy('label', 'asc')
  }

  // Base interface implementation methods

  /**
   * Get resource by ID (base interface implementation)
   */
  async getResourceById(id: string): Promise<PolicyCategoryWithUser | null> {
    const category = await PolicyCategory.find(id)
    // Only return categories with valid userId (not system categories)
    if (category && category.userId) {
      return category as PolicyCategoryWithUser
    }
    return null
  }

  /**
   * Get resources with pagination (base interface implementation)
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean }
  ): Promise<PaginatedResponse<PolicyCategoryWithUser>> {
    const { queryParams = {}, paginate = true } = options
    const { filters, sort, pagination } = queryParams

    let query = PolicyCategory.query().where('userId', userId)

    if (filters) {
      query = this.applyFilters(query, filters)
    }

    if (sort) {
      query = this.applySorting(query, sort)
    } else {
      query = query.orderBy('displayOrder', 'asc').orderBy('label', 'asc')
    }

    if (!paginate) {
      const categories = await query
      return {
        data: categories as PolicyCategoryWithUser[],
        pagination: undefined,
      }
    }

    const { page = 1, limit = 20 } = pagination || {}
    const paginatedResult = await query.paginate(page, limit)

    return {
      data: paginatedResult.all() as PolicyCategoryWithUser[],
      pagination: {
        currentPage: paginatedResult.currentPage,
        totalPages: paginatedResult.lastPage,
        totalCount: paginatedResult.total,
        hasNextPage: paginatedResult.hasMorePages,
      },
    }
  }

  /**
   * Delete resource (base interface implementation)
   */
  async deleteResource(id: string, userId: string): Promise<boolean> {
    try {
      return await this.delete(id, userId)
    } catch {
      return false
    }
  }

  private applyFilters(
    query: ModelQueryBuilderContract<typeof PolicyCategory>,
    filters: PolicyCategoryFilters
  ): ModelQueryBuilderContract<typeof PolicyCategory> {
    if (filters.search) {
      query = query.where((subQuery) => {
        subQuery
          .whereILike('label', `%${filters.search}%`)
          .orWhereILike('description', `%${filters.search}%`)
          .orWhereILike('key', `%${filters.search}%`)
      })
    }

    return query
  }

  private applySorting(
    query: ModelQueryBuilderContract<typeof PolicyCategory>,
    sort: PolicyCategorySort
  ): ModelQueryBuilderContract<typeof PolicyCategory> {
    const { field, direction } = sort

    switch (field) {
      case 'name':
        // Map 'name' to 'label' field
        query = query.orderBy('label', direction)
        break
      case 'createdAt':
        query = query.orderBy('createdAt', direction)
        break
      case 'updatedAt':
        query = query.orderBy('updatedAt', direction)
        break
      default:
        query = query.orderBy('displayOrder', direction)
    }

    return query
  }
}
