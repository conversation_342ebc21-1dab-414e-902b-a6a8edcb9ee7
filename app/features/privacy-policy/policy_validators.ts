import vine from '@vinejs/vine'
import { createQueryValidator } from '#validators/base_validators'
import { AvailabilityScopeType, ActivityDetailVisibilityLevel } from '#types/policy_template_types'

// Policy Category query validator
export const policyCategoryQueryValidator = createQueryValidator({
  statusOptions: [], // No status field for policy categories
  sortFields: ['name', 'createdAt', 'updatedAt'],
  additionalFields: {
    // Add any specific query fields for policy categories if needed
  },
})

// Privacy Policy Template query validator
export const privacyPolicyTemplateQueryValidator = createQueryValidator({
  statusOptions: [], // No status field for privacy policy templates
  sortFields: ['name', 'isDefault', 'createdAt', 'updatedAt'],
  additionalFields: {
    isDefault: vine.boolean().optional(),
    policyCategory: vine.string().uuid().optional(),
  },
})

// Create validators
export const createPolicyCategoryValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255),
    description: vine.string().trim().optional(),
  })
)

export const updatePolicyCategoryValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255).optional(),
    description: vine.string().trim().optional(),
  })
)

// Privacy Policy Template create validator (reusing existing logic)
export const createPrivacyPolicyTemplateValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255),
    description: vine.string().trim().optional(),
    permissions: vine.object({
      canViewCalendar: vine.boolean().optional(),
      canScheduleMeeting: vine.boolean().optional(),
      showDetails: vine.boolean().optional(),
    }).optional(),
    isDefault: vine.boolean().optional(),
    policyCategoryId: vine.string().uuid().optional(),
  })
)

// Privacy Policy Template update validator (reusing existing logic)
export const updatePrivacyPolicyTemplateValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(1).maxLength(255).optional(),
    description: vine.string().trim().optional(),
    permissions: vine.object({
      canViewCalendar: vine.boolean().optional(),
      canScheduleMeeting: vine.boolean().optional(),
      showDetails: vine.boolean().optional(),
    }).optional(),
    isDefault: vine.boolean().optional(),
    policyCategoryId: vine.string().uuid().optional(),
  })
)

// Create Policy Template Rule validator
export const createPolicyTemplateRuleValidator = vine.compile(
  vine.object({
    templateId: vine.string().uuid(),
    viewerScopeType: vine.enum(AvailabilityScopeType),
    viewerTargetUserId: vine.string().uuid().optional(),
    viewerTargetGroupId: vine.string().uuid().optional(),
    viewerTargetContactListId: vine.string().uuid().optional(),
    blocksScheduling: vine.boolean().optional(),
    detailVisibility: vine.enum(ActivityDetailVisibilityLevel),
    customMessage: vine.string().trim().optional(),
    priority: vine.number().min(1).max(100).optional(),
  })
)

// Update Policy Template Rule validator
export const updatePolicyTemplateRuleValidator = vine.compile(
  vine.object({
    viewerScopeType: vine.enum(AvailabilityScopeType).optional(),
    viewerTargetUserId: vine.string().uuid().optional(),
    viewerTargetGroupId: vine.string().uuid().optional(),
    viewerTargetContactListId: vine.string().uuid().optional(),
    blocksScheduling: vine.boolean().optional(),
    detailVisibility: vine.enum(ActivityDetailVisibilityLevel).optional(),
    customMessage: vine.string().trim().optional(),
    priority: vine.number().min(1).max(100).optional(),
  })
)
