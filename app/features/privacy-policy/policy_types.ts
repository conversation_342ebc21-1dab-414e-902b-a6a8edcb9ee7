import {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  GetResourcesOptions,
} from '#types/base_query_types'

// Policy Category Types
export interface PolicyCategoryFilters extends BaseFilters {
  // Add any specific filters for policy categories if needed
}

export interface PolicyCategorySort extends BaseSort {
  field: 'name' | 'createdAt' | 'updatedAt'
}

export type PolicyCategoryQueryParams = BaseQueryParams<PolicyCategoryFilters, PolicyCategorySort>

export type GetPolicyCategoriesOptions = GetResourcesOptions<
  PolicyCategoryFilters,
  PolicyCategorySort
>

// Privacy Policy Template Types
export interface PrivacyPolicyTemplateFilters extends BaseFilters {
  isDefault?: boolean
  policyCategory?: string
}

export interface PrivacyPolicyTemplateSort extends BaseSort {
  field: 'name' | 'isDefault' | 'createdAt' | 'updatedAt'
}

export type PrivacyPolicyTemplateQueryParams = BaseQueryParams<
  PrivacyPolicyTemplateFilters,
  PrivacyPolicyTemplateSort
>

export type GetPrivacyPolicyTemplatesOptions = GetResourcesOptions<
  PrivacyPolicyTemplateFilters,
  PrivacyPolicyTemplateSort
>

// Policy Template Rule Types
export interface PolicyTemplateRuleFilters extends BaseFilters {
  templateId?: string
  viewerScopeType?: string
  blocksScheduling?: boolean
  detailVisibility?: string
}

export interface PolicyTemplateRuleSort extends BaseSort {
  field: 'priority' | 'blocksScheduling' | 'detailVisibility' | 'createdAt' | 'updatedAt'
}

export type PolicyTemplateRuleQueryParams = BaseQueryParams<
  PolicyTemplateRuleFilters,
  PolicyTemplateRuleSort
>

export type GetPolicyTemplateRulesOptions = GetResourcesOptions<
  PolicyTemplateRuleFilters,
  PolicyTemplateRuleSort
>
