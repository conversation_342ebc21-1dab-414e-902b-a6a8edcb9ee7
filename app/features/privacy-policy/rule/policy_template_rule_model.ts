import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { AvailabilityScopeType, ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import User from '../../user/user_model.js'
import PrivacyPolicyTemplate from '../template/privacy_policy_template_model.js'

export default class PolicyTemplateRule extends BaseModel {
  static table = 'policy_template_rules'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare templateId: string

  @column()
  declare viewerScopeType: AvailabilityScopeType

  @column()
  declare viewerTargetUserId: string | null

  @column()
  declare viewerTargetGroupId: string | null

  @column()
  declare viewerTargetContactListId: string | null

  @column()
  declare blocksScheduling: boolean // Renamed from isAccountedFor

  @column()
  declare detailVisibility: ActivityDetailVisibilityLevel

  @column()
  declare customMessage: string | null

  @column()
  declare priority: number // For conflict resolution

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => PrivacyPolicyTemplate, {
    foreignKey: 'templateId',
  })
  declare template: BelongsTo<typeof PrivacyPolicyTemplate>

  @belongsTo(() => User, {
    foreignKey: 'viewerTargetUserId',
  })
  declare targetUser: BelongsTo<typeof User>

  // Note: Group and ContactList relationships will be added when those models are created
  // @belongsTo(() => UserGroup, {
  //   foreignKey: 'viewerTargetGroupId',
  // })
  // declare targetGroup: BelongsTo<typeof UserGroup>

  // @belongsTo(() => UserContactList, {
  //   foreignKey: 'viewerTargetContactListId',
  // })
  // declare targetContactList: BelongsTo<typeof UserContactList>
}
