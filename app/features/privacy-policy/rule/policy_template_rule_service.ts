import PolicyTemplateRule from './policy_template_rule_model.js'
import PrivacyPolicyTemplate from '../template/privacy_policy_template_model.js'
import User from '../../user/user_model.js'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import type { NonPatchableModelFields } from '#types/model_utilities'
import { notFoundException } from '#utils/error'
import { BaseResourceService } from '#controllers/base_controller'
import { PaginatedResponse } from '#types/base_query_types'
import { DateTime } from 'luxon'

// Derive patchable attributes from the PolicyTemplateRule model
type AllowedPolicyTemplateRulePatchAttributes = Omit<
  ModelAttributes<PolicyTemplateRule>,
  NonPatchableModelFields
>

// PolicyTemplateRulePatchData: Represents the data structure for merging user input during policy template rule updates.
export type PolicyTemplateRulePatchData = Partial<AllowedPolicyTemplateRulePatchAttributes>

// Extended interface for PolicyTemplateRule with guaranteed userId (for base controller compatibility)
interface PolicyTemplateRuleWithUser extends PolicyTemplateRule {
  userId: string
}

/**
 * PolicyTemplateRuleService
 *
 * Service for managing policy template rules including CRUD operations.
 */
export default class PolicyTemplateRuleService
  implements
    BaseResourceService<
      PolicyTemplateRuleWithUser,
      any, // Filters will no longer be used
      any // Sort will no longer be used
    >
{
  /**
   * Create a new policy template rule
   */
  async create(userId: string, ruleData: any): Promise<PolicyTemplateRule> {
    // Verify user exists
    await User.findOrFail(userId)

    // Verify template exists and belongs to the user
    const template = await PrivacyPolicyTemplate.query()
      .where('id', ruleData.templateId)
      .where('userId', userId)
      .first()

    if (!template) {
      throw notFoundException('Privacy policy template not found or access denied')
    }

    const rule = await PolicyTemplateRule.create({
      ...ruleData,
      userId,
    })

    return rule
  }

  /**
   * Update a policy template rule
   */
  async update(
    ruleId: string,
    userId: string,
    data: PolicyTemplateRulePatchData
  ): Promise<PolicyTemplateRule> {
    const rule = await PolicyTemplateRule.query()
      .where('id', ruleId)
      .where('userId', userId)
      .whereNull('deletedAt')
      .first()

    if (!rule) {
      throw notFoundException('Policy template rule not found')
    }

    rule.merge(data)
    await rule.save()

    return rule
  }

  /**
   * Get a policy template rule by ID
   */
  async getById(ruleId: string, userId: string): Promise<PolicyTemplateRule> {
    const rule = await PolicyTemplateRule.query()
      .where('id', ruleId)
      .where('userId', userId)
      .whereNull('deletedAt')
      .preload('template')
      .preload('targetUser')
      .first()

    if (!rule) {
      throw notFoundException('Policy template rule not found')
    }

    return rule
  }

  /**
   * Delete a policy template rule (soft delete)
   */
  async delete(ruleId: string, userId: string): Promise<boolean> {
    const rule = await PolicyTemplateRule.query()
      .where('id', ruleId)
      .where('userId', userId)
      .whereNull('deletedAt')
      .first()

    if (!rule) {
      throw notFoundException('Policy template rule not found')
    }

    rule.deletedAt = DateTime.now()
    await rule.save()
    return true
  }

  // Base interface implementation methods

  /**
   * Get resource by ID (base interface implementation)
   */
  async getResourceById(id: string): Promise<PolicyTemplateRuleWithUser | null> {
    const rule = await PolicyTemplateRule.query().where('id', id).whereNull('deletedAt').first()
    return rule ? (rule as PolicyTemplateRuleWithUser) : null
  }

  /**
   * Get resources without filtering or pagination (base interface implementation)
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean } = {}
  ): Promise<PaginatedResponse<PolicyTemplateRuleWithUser>> {
    let query = PolicyTemplateRule.query()
      .where('userId', userId)
      .whereNull('deletedAt')
      .preload('template')
      .preload('targetUser')

    if (options.paginate) {
      const page = options.queryParams?.page || 1
      const limit = options.queryParams?.limit || 10
      const result = await query.paginate(page, limit)

      return {
        data: result.all() as PolicyTemplateRuleWithUser[],
        pagination: {
          totalCount: result.getMeta().total,
          currentPage: result.getMeta().currentPage,
          totalPages: result.getMeta().lastPage,
          hasNextPage: result.getMeta().hasNextPage || false,
          hasPreviousPage: result.getMeta().hasPreviousPage || false,
        },
      }
    } else {
      const rules = await query
      return {
        data: rules as PolicyTemplateRuleWithUser[],
        pagination: {
          totalCount: rules.length,
          currentPage: 1,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }
    }
  }

  /**
   * Delete resource (base interface implementation)
   */
  async deleteResource(id: string, userId: string): Promise<boolean> {
    return await this.delete(id, userId)
  }
}
