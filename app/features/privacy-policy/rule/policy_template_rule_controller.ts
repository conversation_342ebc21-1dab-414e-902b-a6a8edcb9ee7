import type { HttpContext } from '@adonisjs/core/http'
import PolicyTemplateRuleService from './policy_template_rule_service.js'
import { PolicyTemplateRulePatchData } from './policy_template_rule_service.js'
import {
  createPolicyTemplateRuleValidator,
  updatePolicyTemplateRuleValidator,
} from '../policy_validators.js'
import { createSuccessResponse } from '#utils/response'

export default class PolicyTemplateRuleController {
  private service = new PolicyTemplateRuleService()
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.service,
      resourceName: 'policy template rule',
      getStatusMapping: () => undefined, // No status field for policy template rules
    }
  }

  /**
   * List all policy template rules for the authenticated user
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Get a specific policy template rule by ID
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete a policy template rule (soft delete)
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Create a new policy template rule
   */
  async store({ request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'policy template rule')

      const data = await request.validateUsing(createPolicyTemplateRuleValidator)

      const rule = await this.service.create(userId, data)

      return response.status(201).json(
        createSuccessResponse({
          data: rule,
          message: 'Policy template rule created successfully',
        })
      )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Update an existing policy template rule
   */
  async update({ params, request, response, auth }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'policy template rule')

      const { id: ruleId } = params
      if (!ruleId) {
        throw new Error('Policy template rule ID is required')
      }

      await base.validateResourceOwnership(ruleId, userId, this.service, 'policy template rule')
      const data: PolicyTemplateRulePatchData = await request.validateUsing(
        updatePolicyTemplateRuleValidator
      )

      const updatedRule = await this.service.update(ruleId, userId, data)

      return response.status(200).json(
        createSuccessResponse({
          data: updatedRule,
          message: 'Policy template rule updated successfully',
        })
      )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }
}
