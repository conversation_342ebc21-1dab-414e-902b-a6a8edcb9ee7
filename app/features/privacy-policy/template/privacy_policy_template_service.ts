import PrivacyPolicyTemplate from './privacy_policy_template_model.js'
import PolicyCategory from '../category/policy_category_model.js'
import User from '../../user/user_model.js'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import type { NonPatchableModelFields } from '#types/model_utilities'
import { notFoundException } from '#utils/error'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import { BaseResourceService } from '#controllers/base_controller'
import { PrivacyPolicyTemplateFilters, PrivacyPolicyTemplateSort } from '../policy_types.js'
import { PaginatedResponse } from '#types/base_query_types'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

// Derive patchable attributes from the PrivacyPolicyTemplate model
type AllowedPrivacyPolicyTemplatePatchAttributes = Omit<
  ModelAttributes<PrivacyPolicyTemplate>,
  NonPatchableModelFields
>

// PrivacyPolicyTemplateForMerge: Represents the data structure for merging user input during privacy policy template updates.
// It excludes critical fields that should not be directly modifiable via user input.
type PrivacyPolicyTemplatePatchData = Partial<AllowedPrivacyPolicyTemplatePatchAttributes>

// Extended interface for PrivacyPolicyTemplate with guaranteed userId (for base controller compatibility)
interface PrivacyPolicyTemplateWithUser extends PrivacyPolicyTemplate {
  userId: string
}

/**
 * PrivacyPolicyTemplateService
 *
 * Service for managing privacy policy templates including CRUD operations
 * and system-wide template management.
 */
export default class PrivacyPolicyTemplateService
  implements
    BaseResourceService<
      PrivacyPolicyTemplateWithUser,
      PrivacyPolicyTemplateFilters,
      PrivacyPolicyTemplateSort
    >
{
  /**
   * Create a new privacy policy template for a user
   */
  async create(
    userId: string,
    templateData: {
      name: string
      description?: string | null
      categoryId?: string | null
      policyCategoryId?: string | null // API field mapping
      permissions?: {
        canViewCalendar?: boolean
        canScheduleMeeting?: boolean
        showDetails?: boolean
      }
      isDefault?: boolean
      isSystemTemplate?: boolean
      blocksScheduling?: boolean
      defaultDetailVisibility?: ActivityDetailVisibilityLevel
      defaultCustomMessage?: string | null
    }
  ): Promise<PrivacyPolicyTemplate> {
    // Verify user exists
    await User.findOrFail(userId)

    // Handle API field mapping: policyCategoryId -> categoryId
    const categoryId = templateData.policyCategoryId || templateData.categoryId

    // If a category is specified, verify it exists and belongs to the user or is a system category
    if (categoryId) {
      const category = await PolicyCategory.query()
        .where('id', categoryId)
        .where((query) => {
          query.where('userId', userId).orWhereNull('userId')
        })
        .first()

      if (!category) {
        throw notFoundException('Policy category not found or access denied')
      }
    }

    // Handle permissions mapping to internal fields
    let blocksScheduling = templateData.blocksScheduling ?? false
    let defaultDetailVisibility = templateData.defaultDetailVisibility ?? ActivityDetailVisibilityLevel.HIDDEN

    if (templateData.permissions) {
      // If canScheduleMeeting is false, then it blocks scheduling
      if (templateData.permissions.canScheduleMeeting !== undefined) {
        blocksScheduling = !templateData.permissions.canScheduleMeeting
      }
      
      // Map canViewCalendar and showDetails to defaultDetailVisibility
      // Priority: showDetails > canViewCalendar
      if (templateData.permissions.showDetails !== undefined) {
        if (templateData.permissions.showDetails) {
          defaultDetailVisibility = ActivityDetailVisibilityLevel.FULL
        } else {
          // showDetails is false, but check canViewCalendar for intermediate level
          defaultDetailVisibility = templateData.permissions.canViewCalendar 
            ? ActivityDetailVisibilityLevel.BUSY_ONLY 
            : ActivityDetailVisibilityLevel.HIDDEN
        }
      } else if (templateData.permissions.canViewCalendar !== undefined) {
        // If canViewCalendar is false, set to hidden; otherwise set to busy_only (default)
        defaultDetailVisibility = templateData.permissions.canViewCalendar 
          ? ActivityDetailVisibilityLevel.BUSY_ONLY 
          : ActivityDetailVisibilityLevel.HIDDEN
      }
    }

    const template = await PrivacyPolicyTemplate.create({
      userId,
      name: templateData.name,
      description: templateData.description || null,
      categoryId: categoryId || null,
      isDefault: templateData.isDefault ?? false,
      isSystemTemplate: templateData.isSystemTemplate ?? false,
      blocksScheduling,
      defaultDetailVisibility,
      defaultCustomMessage: templateData.defaultCustomMessage || null,
    })

    return template
  }

  /**
   * Get privacy policy templates for a specific user
   */
  async getByUserId(userId: string) {
    return PrivacyPolicyTemplate.query()
      .where('userId', userId)
      .preload('category')
      .orderBy('name', 'asc')
  }

  /**
   * Get a privacy policy template by ID (optionally filtered by user)
   */
  async getById(id: string, userId?: string) {
    const query = PrivacyPolicyTemplate.query().where('id', id).preload('category').preload('rules')

    if (userId) {
      query.where('userId', userId)
    }

    const template = await query.first()
    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    return template
  }

  /**
   * Update a privacy policy template
   */
  async update(id: string, userId: string, data: PrivacyPolicyTemplatePatchData) {
    const template = await PrivacyPolicyTemplate.query()
      .where('id', id)
      .where('userId', userId)
      .first()

    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    // If updating category, verify it exists and belongs to the user or is a system category
    if (data.categoryId !== undefined) {
      if (data.categoryId !== null) {
        const category = await PolicyCategory.query()
          .where('id', data.categoryId)
          .where((query) => {
            query.where('userId', userId).orWhereNull('userId')
          })
          .first()

        if (!category) {
          throw notFoundException('Policy category not found or access denied')
        }
      }
    }

    template.merge(data)
    await template.save()

    return template
  }

  /**
   * Delete a privacy policy template
   */
  async delete(id: string, userId: string) {
    const template = await PrivacyPolicyTemplate.query()
      .where('id', id)
      .where('userId', userId)
      .first()

    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    await template.delete()
    return true
  }

  /**
   * Get system-wide privacy policy templates
   */
  async getSystemTemplates() {
    return PrivacyPolicyTemplate.query()
      .whereNull('userId')
      .where('isSystemTemplate', true)
      .preload('category')
      .orderBy('name', 'asc')
  }

  /**
   * Get default privacy policy template for a user
   */
  async getDefaultTemplate(userId: string) {
    return PrivacyPolicyTemplate.query()
      .where('userId', userId)
      .where('isDefault', true)
      .preload('category')
      .preload('rules')
      .first()
  }

  // Base interface implementation methods

  /**
   * Get resource by ID (base interface implementation)
   */
  async getResourceById(id: string): Promise<PrivacyPolicyTemplateWithUser | null> {
    const template = await PrivacyPolicyTemplate.find(id)
    // Only return templates with valid userId (not system templates)
    if (template && template.userId) {
      return template as PrivacyPolicyTemplateWithUser
    }
    return null
  }

  /**
   * Get resources with pagination (base interface implementation)
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean }
  ): Promise<PaginatedResponse<PrivacyPolicyTemplateWithUser>> {
    console.log('[PrivacyPolicyTemplateService] getResources called')
    console.log('[PrivacyPolicyTemplateService] userId:', userId)
    console.log('[PrivacyPolicyTemplateService] options:', JSON.stringify(options, null, 2))
    
    const { queryParams = {}, paginate = true } = options
    const { filters, sort, pagination } = queryParams

    console.log('[PrivacyPolicyTemplateService] queryParams:', JSON.stringify(queryParams, null, 2))
    console.log('[PrivacyPolicyTemplateService] filters:', JSON.stringify(filters, null, 2))
    console.log('[PrivacyPolicyTemplateService] sort:', JSON.stringify(sort, null, 2))
    console.log('[PrivacyPolicyTemplateService] pagination:', JSON.stringify(pagination, null, 2))
    console.log('[PrivacyPolicyTemplateService] paginate:', paginate)

    let query = PrivacyPolicyTemplate.query().where('userId', userId).preload('category')
    console.log('[PrivacyPolicyTemplateService] Base query created for userId:', userId)

    if (filters) {
      console.log('[PrivacyPolicyTemplateService] Applying filters:', JSON.stringify(filters, null, 2))
      query = this.applyFilters(query, filters)
      console.log('[PrivacyPolicyTemplateService] Filters applied')
    }

    if (sort) {
      console.log('[PrivacyPolicyTemplateService] Applying sort:', JSON.stringify(sort, null, 2))
      query = this.applySorting(query, sort)
      console.log('[PrivacyPolicyTemplateService] Sort applied')
    } else {
      console.log('[PrivacyPolicyTemplateService] Using default sort: name asc')
      query = query.orderBy('name', 'asc')
    }

    if (!paginate) {
      console.log('[PrivacyPolicyTemplateService] Running query without pagination')
      const templates = await query
      console.log('[PrivacyPolicyTemplateService] Query result count:', templates.length)
      console.log('[PrivacyPolicyTemplateService] Query results:', JSON.stringify(templates.map(t => ({ id: t.id, name: t.name })), null, 2))
      
      const result = {
        data: templates as PrivacyPolicyTemplateWithUser[],
        pagination: undefined,
      }
      console.log('[PrivacyPolicyTemplateService] Returning unpaginated result')
      return result
    }

    const { page = 1, limit = 20 } = pagination || {}
    console.log('[PrivacyPolicyTemplateService] Running paginated query with page:', page, 'limit:', limit)
    
    try {
      const paginatedResult = await query.paginate(page, limit)
      console.log('[PrivacyPolicyTemplateService] Paginated query completed')
      console.log('[PrivacyPolicyTemplateService] Paginated result meta:', {
        currentPage: paginatedResult.currentPage,
        lastPage: paginatedResult.lastPage,
        total: paginatedResult.total,
        hasMorePages: paginatedResult.hasMorePages,
        dataCount: paginatedResult.all().length
      })
      console.log('[PrivacyPolicyTemplateService] Paginated data items:', JSON.stringify(paginatedResult.all().map(t => ({ id: t.id, name: t.name })), null, 2))

      const result = {
        data: paginatedResult.all() as PrivacyPolicyTemplateWithUser[],
        pagination: {
          currentPage: paginatedResult.currentPage,
          totalPages: paginatedResult.lastPage,
          totalCount: paginatedResult.total,
          hasNextPage: paginatedResult.hasMorePages,
        },
      }
      console.log('[PrivacyPolicyTemplateService] Returning paginated result:', JSON.stringify({
        dataCount: result.data.length,
        pagination: result.pagination
      }, null, 2))
      
      return result
    } catch (error) {
      console.error('[PrivacyPolicyTemplateService] Error in paginated query:', error)
      console.error('[PrivacyPolicyTemplateService] Error message:', error.message)
      console.error('[PrivacyPolicyTemplateService] Error stack:', error.stack)
      throw error
    }
  }

  /**
   * Delete resource (base interface implementation)
   */
  async deleteResource(id: string, userId: string): Promise<boolean> {
    try {
      return await this.delete(id, userId)
    } catch {
      return false
    }
  }

  /**
   * Update resource (base interface implementation)
   */
  async updateResource(id: string, data: any): Promise<PrivacyPolicyTemplateWithUser> {
    console.log('[PrivacyPolicyTemplateService] updateResource called')
    console.log('[PrivacyPolicyTemplateService] id:', id)
    console.log('[PrivacyPolicyTemplateService] data:', JSON.stringify(data, null, 2))
    
    try {
      // Get the template to find the userId (ownership was already validated by base controller)
      console.log('[PrivacyPolicyTemplateService] Finding template with id:', id)
      const template = await PrivacyPolicyTemplate.findOrFail(id)
      console.log('[PrivacyPolicyTemplateService] Template found:', JSON.stringify(template.serialize(), null, 2))
      
      const userId = template.userId
      console.log('[PrivacyPolicyTemplateService] userId from template:', userId)

      // Handle API field mapping: policyCategoryId -> categoryId
      const updateData: PrivacyPolicyTemplatePatchData = { ...data }
      console.log('[PrivacyPolicyTemplateService] Initial updateData:', JSON.stringify(updateData, null, 2))
      
      if (data.policyCategoryId !== undefined) {
        updateData.categoryId = data.policyCategoryId
        delete updateData.policyCategoryId
        console.log('[PrivacyPolicyTemplateService] Mapped policyCategoryId to categoryId:', updateData.categoryId)
      }

      // Handle permissions mapping to internal fields
      if (data.permissions) {
        console.log('[PrivacyPolicyTemplateService] Processing permissions:', JSON.stringify(data.permissions, null, 2))
        
        if (data.permissions.canScheduleMeeting !== undefined) {
          updateData.blocksScheduling = !data.permissions.canScheduleMeeting
          console.log('[PrivacyPolicyTemplateService] Mapped canScheduleMeeting to blocksScheduling:', updateData.blocksScheduling)
        }
        
        // Map canViewCalendar and showDetails to defaultDetailVisibility
        // Priority: showDetails > canViewCalendar
        if (data.permissions.showDetails !== undefined) {
          updateData.defaultDetailVisibility = data.permissions.showDetails 
            ? ActivityDetailVisibilityLevel.FULL 
            : (data.permissions.canViewCalendar === false ? ActivityDetailVisibilityLevel.HIDDEN : ActivityDetailVisibilityLevel.BUSY_ONLY)
          console.log('[PrivacyPolicyTemplateService] Mapped showDetails to defaultDetailVisibility:', updateData.defaultDetailVisibility)
        } else if (data.permissions.canViewCalendar !== undefined) {
          // If canViewCalendar is false, set to hidden; otherwise set to busy_only (default)
          updateData.defaultDetailVisibility = data.permissions.canViewCalendar 
            ? ActivityDetailVisibilityLevel.BUSY_ONLY 
            : ActivityDetailVisibilityLevel.HIDDEN
          console.log('[PrivacyPolicyTemplateService] Mapped canViewCalendar to defaultDetailVisibility:', updateData.defaultDetailVisibility)
        }
        
        delete updateData.permissions
      }

      console.log('[PrivacyPolicyTemplateService] Final updateData:', JSON.stringify(updateData, null, 2))
      
      const updatedTemplate = await this.update(id, userId, updateData)
      console.log('[PrivacyPolicyTemplateService] Template updated successfully:', JSON.stringify(updatedTemplate.serialize(), null, 2))
      
      return updatedTemplate as PrivacyPolicyTemplateWithUser
    } catch (error) {
      console.error('[PrivacyPolicyTemplateService] updateResource error:', error)
      console.error('[PrivacyPolicyTemplateService] updateResource error message:', error.message)
      console.error('[PrivacyPolicyTemplateService] updateResource error stack:', error.stack)
      throw error
    }
  }

  /**
   * Create resource (base interface implementation)
   */
  async createResource(userId: string, data: any): Promise<PrivacyPolicyTemplateWithUser> {
    console.log('[PrivacyPolicyTemplateService] createResource called')
    console.log('[PrivacyPolicyTemplateService] userId:', userId)
    console.log('[PrivacyPolicyTemplateService] data:', JSON.stringify(data, null, 2))
    
    try {
      const template = await this.create(userId, data)
      console.log('[PrivacyPolicyTemplateService] Template created successfully:', JSON.stringify(template.serialize(), null, 2))
      return template as PrivacyPolicyTemplateWithUser
    } catch (error) {
      console.error('[PrivacyPolicyTemplateService] createResource error:', error)
      console.error('[PrivacyPolicyTemplateService] createResource error message:', error.message)
      console.error('[PrivacyPolicyTemplateService] createResource error stack:', error.stack)
      throw error
    }
  }

  private applyFilters(
    query: ModelQueryBuilderContract<typeof PrivacyPolicyTemplate>,
    filters: PrivacyPolicyTemplateFilters
  ): ModelQueryBuilderContract<typeof PrivacyPolicyTemplate> {
    if (filters.search) {
      query = query.where((subQuery) => {
        subQuery
          .whereILike('name', `%${filters.search}%`)
          .orWhereILike('description', `%${filters.search}%`)
      })
    }

    if (filters.isDefault !== undefined) {
      query = query.where('isDefault', filters.isDefault)
    }

    if (filters.policyCategory) {
      query = query.where('categoryId', filters.policyCategory)
    }

    return query
  }

  private applySorting(
    query: ModelQueryBuilderContract<typeof PrivacyPolicyTemplate>,
    sort: PrivacyPolicyTemplateSort
  ): ModelQueryBuilderContract<typeof PrivacyPolicyTemplate> {
    const { field, direction } = sort

    switch (field) {
      case 'name':
        query = query.orderBy('name', direction)
        break
      case 'isDefault':
        query = query.orderBy('isDefault', direction)
        break
      case 'createdAt':
        query = query.orderBy('createdAt', direction)
        break
      case 'updatedAt':
        query = query.orderBy('updatedAt', direction)
        break
      default:
        query = query.orderBy('name', direction)
    }

    return query
  }
}
