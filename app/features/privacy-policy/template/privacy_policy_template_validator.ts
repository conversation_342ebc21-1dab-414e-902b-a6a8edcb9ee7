import vine from '@vinejs/vine'
import { ACTIVITY_DETAIL_VISIBILITY_LEVELS } from '#types/policy_template_types'

// Base schema definitions for each privacy policy template field
const privacyPolicyTemplateFieldSchemas = {
  name: vine.string().trim().minLength(1).maxLength(255),
  description: vine.string().trim().maxLength(1000),
  categoryId: vine.string().uuid(),
  isDefault: vine.boolean(),
  isSystemTemplate: vine.boolean(),
  blocksScheduling: vine.boolean(),
  defaultDetailVisibility: vine.enum(ACTIVITY_DETAIL_VISIBILITY_LEVELS),
  defaultCustomMessage: vine.string().trim().maxLength(500),
}

/**
 * Validator for privacy policy template creation/full update
 */
const privacyPolicyTemplateValidator = vine.compile(
  vine.object({
    name: privacyPolicyTemplateFieldSchemas.name.clone(),
    description: privacyPolicyTemplateFieldSchemas.description.clone().optional(),
    categoryId: privacyPolicyTemplateFieldSchemas.categoryId.clone().optional(),
    isDefault: privacyPolicyTemplateFieldSchemas.isDefault.clone().optional(),
    isSystemTemplate: privacyPolicyTemplateFieldSchemas.isSystemTemplate.clone().optional(),
    blocksScheduling: privacyPolicyTemplateFieldSchemas.blocksScheduling.clone().optional(),
    defaultDetailVisibility: privacyPolicyTemplateFieldSchemas.defaultDetailVisibility
      .clone()
      .optional(),
    defaultCustomMessage: privacyPolicyTemplateFieldSchemas.defaultCustomMessage.clone().optional(),
  })
)

/**
 * Validator for partial privacy policy template update (all fields optional and nullable)
 */
const partialPrivacyPolicyTemplateSchemaObject = Object.fromEntries(
  Object.entries(privacyPolicyTemplateFieldSchemas).map(([key, schema]) => [
    key,
    schema.clone().nullable().optional(),
  ])
) as { [K in keyof typeof privacyPolicyTemplateFieldSchemas]: any }

const partialPrivacyPolicyTemplateObjectSchema = vine.object(
  partialPrivacyPolicyTemplateSchemaObject
)

const partialPrivacyPolicyTemplateValidator = vine.compile(partialPrivacyPolicyTemplateObjectSchema)

export { privacyPolicyTemplateValidator, partialPrivacyPolicyTemplateValidator }
