import BaseModel from '#models/base_model'
import { column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import User from '../../user/user_model.js'
import PolicyCategory from '../category/policy_category_model.js'
import PolicyTemplateRule from '../rule/policy_template_rule_model.js'
import PrivacyPolicyAssignment from '../privacy_policy_assignment_model.js'

export default class PrivacyPolicyTemplate extends compose(BaseModel, SoftDeletes) {
  static table = 'privacy_policy_templates'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare name: string // "Work Privacy", "Family Friendly", "Personal Time"

  @column()
  declare description: string | null

  @column()
  declare categoryId: string | null

  @column()
  declare isDefault: boolean

  @column()
  declare isSystemTemplate: boolean

  @column()
  declare blocksScheduling: boolean // Renamed from isAccountedFor

  @column()
  declare defaultDetailVisibility: ActivityDetailVisibilityLevel

  @column()
  declare defaultCustomMessage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => PolicyCategory, {
    foreignKey: 'categoryId',
  })
  declare category: BelongsTo<typeof PolicyCategory>

  @hasMany(() => PolicyTemplateRule, {
    foreignKey: 'templateId',
  })
  declare rules: HasMany<typeof PolicyTemplateRule>

  @hasMany(() => PrivacyPolicyAssignment, {
    foreignKey: 'templateId',
  })
  declare assignments: HasMany<typeof PrivacyPolicyAssignment>
}
