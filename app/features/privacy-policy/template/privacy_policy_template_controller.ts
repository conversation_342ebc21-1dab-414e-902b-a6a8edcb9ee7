import type { HttpContext } from '@adonisjs/core/http'
import PrivacyPolicyTemplateService from './privacy_policy_template_service.js'
import {
  privacyPolicyTemplateQueryValidator,
  createPrivacyPolicyTemplateValidator,
  updatePrivacyPolicyTemplateValidator,
} from '../policy_validators.js'
import { createSuccessResponse } from '#utils/response'
import { PrivacyPolicyTemplateFilters } from '../policy_types.js'

export default class PrivacyPolicyTemplateController {
  private service = new PrivacyPolicyTemplateService()
  private baseController: any = null

  /**
   * Transform template data for API response
   */
  private transformTemplate(template: any) {
    // If template has serialize method (Lucid model), use it, otherwise use as-is
    const cleanTemplate = template.serialize ? template.serialize() : template
    
    return {
      id: cleanTemplate.id,
      userId: cleanTemplate.userId,
      name: cleanTemplate.name,
      description: cleanTemplate.description,
      policyCategoryId: cleanTemplate.categoryId, // Map categoryId to policyCategoryId for API consistency
      isDefault: cleanTemplate.isDefault,
      isSystemTemplate: cleanTemplate.isSystemTemplate,
      createdAt: cleanTemplate.createdAt,
      updatedAt: cleanTemplate.updatedAt,
      permissions: {
        // Map internal fields to expected permissions structure
        // canViewCalendar: derive from detail visibility - if not hidden, calendar can be viewed
        canViewCalendar: cleanTemplate.defaultDetailVisibility !== 'hidden',
        // canScheduleMeeting: inverse of blocksScheduling
        canScheduleMeeting: !cleanTemplate.blocksScheduling,
        // showDetails: true if full details are visible
        showDetails: cleanTemplate.defaultDetailVisibility === 'full_details'
      }
    }
  }

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.service,
      resourceName: 'privacy policy template',
      validateQueryParams: async (request: any) =>
        await request.validateUsing(privacyPolicyTemplateQueryValidator),
      getStatusMapping: () => undefined, // No status field for privacy policy templates
      getSortMapping: () => ({
        name: 'name',
        isDefault: 'isDefault',
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<PrivacyPolicyTemplateFilters>) => {
        if (queryParams.isDefault !== undefined) {
          filters.isDefault = queryParams.isDefault
        }
        if (queryParams.policyCategory) {
          filters.policyCategory = queryParams.policyCategory
        }
      },
      transformResource: (template: any) => this.transformTemplate(template),
      validateCreatePayload: async (request: any) => {
        console.log('[PrivacyPolicyTemplateController] Validating create payload')
        console.log('[PrivacyPolicyTemplateController] Raw request body:', JSON.stringify(request.body(), null, 2))
        try {
          const validated = await request.validateUsing(createPrivacyPolicyTemplateValidator)
          console.log('[PrivacyPolicyTemplateController] Validation successful:', JSON.stringify(validated, null, 2))
          return validated
        } catch (error) {
          console.error('[PrivacyPolicyTemplateController] Validation error:', error)
          console.error('[PrivacyPolicyTemplateController] Validation error messages:', error.messages)
          throw error
        }
      },
      validateUpdatePayload: async (request: any) => {
        console.log('[PrivacyPolicyTemplateController] Validating update payload')
        console.log('[PrivacyPolicyTemplateController] Raw request body for update:', JSON.stringify(request.body(), null, 2))
        try {
          const validated = await request.validateUsing(updatePrivacyPolicyTemplateValidator)
          console.log('[PrivacyPolicyTemplateController] Update validation successful:', JSON.stringify(validated, null, 2))
          return validated
        } catch (error) {
          console.error('[PrivacyPolicyTemplateController] Update validation error:', error)
          console.error('[PrivacyPolicyTemplateController] Update validation error messages:', error.messages)
          throw error
        }
      },
    }
  }

  /**
   * List all privacy policy templates for the authenticated user with filtering, sorting, and pagination
   */
  index = async (ctx: HttpContext) => {
    console.log('[PrivacyPolicyTemplateController] Index method called')
    console.log('[PrivacyPolicyTemplateController] Request query params:', JSON.stringify(ctx.request.qs(), null, 2))
    console.log('[PrivacyPolicyTemplateController] Request headers:', ctx.request.headers())
    console.log('[PrivacyPolicyTemplateController] Auth user:', ctx.auth.user ? 'Present' : 'Missing')
    
    try {
      const base = await this.initBaseController()
      console.log('[PrivacyPolicyTemplateController] Base controller initialized for index')
      
      // Manually implement the index logic to get proper response structure
      const userId = await base.validateAuthentication(ctx.auth, 'privacy policy template')
      console.log('[PrivacyPolicyTemplateController] Authentication successful, userId:', userId)
      
      const config = await this.getConfig()
      const queryParams = config.validateQueryParams
        ? await config.validateQueryParams(ctx.request)
        : {}
      console.log('[PrivacyPolicyTemplateController] Query params validated:', JSON.stringify(queryParams, null, 2))
      
      const transformedParams = base.transformQueryParams(
        userId,
        queryParams,
        config.getStatusMapping ? config.getStatusMapping() : undefined,
        config.getSortMapping ? config.getSortMapping() : undefined,
        config.applyCustomFilters
      )
      console.log('[PrivacyPolicyTemplateController] Transformed params:', JSON.stringify(transformedParams, null, 2))

      const result = await config.service.getResources(userId, {
        queryParams: transformedParams,
        paginate: true,
      })
      console.log('[PrivacyPolicyTemplateController] Service getResources result:', JSON.stringify({
        dataCount: result.data.length,
        pagination: result.pagination
      }, null, 2))

      const transformedData = result.data.map((resource) =>
        config.transformResource ? config.transformResource(resource) : resource
      )
      console.log('[PrivacyPolicyTemplateController] Data transformed, count:', transformedData.length)

      // Create response structure that matches test expectations: body.data.data and body.data.pagination
      // Transform pagination to match test expectations (currentPage -> page, add perPage)
      const transformedPagination = result.pagination ? {
        page: result.pagination.currentPage,
        perPage: transformedParams.pagination?.limit || 20,
        total: result.pagination.totalCount,
        totalPages: result.pagination.totalPages,
        hasNextPage: result.pagination.hasNextPage,
        hasPreviousPage: result.pagination.hasPreviousPage,
      } : undefined
      
      const response = createSuccessResponse({
        data: {
          data: transformedData,
          pagination: transformedPagination,
        },
      })
      console.log('[PrivacyPolicyTemplateController] Response created:', JSON.stringify({
        status: response.status,
        dataDataCount: response.data.data.length,
        hasPagination: !!response.data.pagination
      }, null, 2))
      
      return ctx.response.status(200).json(response)
    } catch (error) {
      console.error('[PrivacyPolicyTemplateController] Index error:', error)
      console.error('[PrivacyPolicyTemplateController] Index error message:', error.message)
      console.error('[PrivacyPolicyTemplateController] Index error stack:', error.stack)
      return base.handleControllerError(error, ctx.response)
    }
  }

  /**
   * Get a specific privacy policy template by ID
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete a privacy policy template (soft delete)
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Create a new privacy policy template
   */
  store = async (ctx: HttpContext) => {
    console.log('[PrivacyPolicyTemplateController] Store method called')
    console.log('[PrivacyPolicyTemplateController] Request body:', JSON.stringify(ctx.request.body(), null, 2))
    console.log('[PrivacyPolicyTemplateController] Request headers:', ctx.request.headers())
    
    try {
      const base = await this.initBaseController()
      console.log('[PrivacyPolicyTemplateController] Base controller initialized')
      
      const config = await this.getConfig()
      console.log('[PrivacyPolicyTemplateController] Config loaded:', JSON.stringify({
        resourceName: config.resourceName,
        hasValidateCreatePayload: !!config.validateCreatePayload,
        hasTransformResource: !!config.transformResource,
        hasService: !!config.service
      }, null, 2))
      
      const handler = base.createStoreHandler(config)
      console.log('[PrivacyPolicyTemplateController] Store handler created')
      
      const result = await handler(ctx)
      console.log('[PrivacyPolicyTemplateController] Store handler result:', JSON.stringify(result, null, 2))
      
      return result
    } catch (error) {
      console.error('[PrivacyPolicyTemplateController] Store error:', error)
      console.error('[PrivacyPolicyTemplateController] Error stack:', error.stack)
      throw error
    }
  }

  /**
   * Update an existing privacy policy template
   */
  update = async (ctx: HttpContext) => {
    console.log('[PrivacyPolicyTemplateController] Update method called')
    console.log('[PrivacyPolicyTemplateController] Request params:', JSON.stringify(ctx.params, null, 2))
    console.log('[PrivacyPolicyTemplateController] Request body:', JSON.stringify(ctx.request.body(), null, 2))
    console.log('[PrivacyPolicyTemplateController] Auth user:', ctx.auth.user ? 'Present' : 'Missing')
    
    try {
      const base = await this.initBaseController()
      console.log('[PrivacyPolicyTemplateController] Base controller initialized for update')
      
      const config = await this.getConfig()
      console.log('[PrivacyPolicyTemplateController] Update config loaded:', JSON.stringify({
        resourceName: config.resourceName,
        hasValidateUpdatePayload: !!config.validateUpdatePayload,
        hasTransformResource: !!config.transformResource,
        hasService: !!config.service,
        hasUpdateResource: !!config.service.updateResource
      }, null, 2))
      
      const handler = base.createUpdateHandler(config)
      console.log('[PrivacyPolicyTemplateController] Update handler created')
      
      const result = await handler(ctx)
      console.log('[PrivacyPolicyTemplateController] Update handler result:', JSON.stringify(result, null, 2))
      
      return result
    } catch (error) {
      console.error('[PrivacyPolicyTemplateController] Update error:', error)
      console.error('[PrivacyPolicyTemplateController] Update error message:', error.message)
      console.error('[PrivacyPolicyTemplateController] Update error status:', error.status)
      console.error('[PrivacyPolicyTemplateController] Update error stack:', error.stack)
      throw error
    }
  }
}
