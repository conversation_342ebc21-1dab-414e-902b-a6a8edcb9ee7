import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Privacy Policy Template Basic', (group) => {
  // Use cleanup instead of global transactions to prevent cascade failures
  group.each.teardown(async (test) => {
    // Clean up in reverse order to handle foreign key constraints
    const tables = [
      'privacy_policy_assignments',
      'policy_template_rules', 
      'privacy_policy_templates',
      'policy_categories',
      'profiles',
      'users',
      'auth_users'
    ]
    for (const table of tables) {
      try {
        await db.from(table).del()
      } catch (error) {
        // Ignore foreign key constraint errors during cleanup
        if (!error.message.includes('violates foreign key constraint')) {
          console.warn(`Failed to clean table ${table}:`, error.message)
        }
      }
    }
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  // Helper function to create a test policy category
  async function createTestPolicyCategory(client: ApiClient, authToken: string, categoryData = {}) {
    const defaultCategoryData = {
      name: 'Work',
      description: 'Work-related privacy policies',
      ...categoryData,
    }

    const response = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json(defaultCategoryData)

    return response.body().data
  }

  // Helper function to create a test privacy policy template
  async function createTestPrivacyTemplate(client: ApiClient, authToken: string, templateData = {}) {
    console.log('[Test Helper] createTestPrivacyTemplate called')
    console.log('[Test Helper] authToken:', authToken ? 'Present' : 'Missing')
    console.log('[Test Helper] templateData:', JSON.stringify(templateData, null, 2))
    
    const defaultTemplateData = {
      name: 'Work Hours Privacy',
      description: 'Privacy settings for work hours',
      ...templateData,
    }
    console.log('[Test Helper] Final templateData to send:', JSON.stringify(defaultTemplateData, null, 2))

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(defaultTemplateData)

    console.log('[Test Helper] Create template response status:', response.response.status)
    
    if (response.response.status !== 201) {
      console.error('[Test Helper] Template creation failed with status:', response.response.status)
      console.error('[Test Helper] Create error response:', response.body())
    } else {
      console.log('[Test Helper] Template creation successful')
      console.log('[Test Helper] Create response body:', JSON.stringify(response.body(), null, 2))
    }

    // Return an object that supports multiple usage patterns:
    // 1. Direct access: const template = await createTestPrivacyTemplate(...)
    // 2. Destructured: const { response: createResponse } = await createTestPrivacyTemplate(...)
    // 3. Response access: const result = await createTestPrivacyTemplate(...); result.body().data
    const responseData = response.body().data
    console.log('[Test Helper] Extracted responseData:', JSON.stringify(responseData, null, 2))
    
    const result = {
      response: {
        assertStatus: (status: number) => response.assertStatus(status),
        body: () => response.body(),
        response: response.response // for nested access like response.response.status
      },
      templateData: defaultTemplateData,
      // For direct usage compatibility, spread the template data
      ...responseData,
      // Add response methods for compatibility
      body: () => response.body(),
      assertStatus: (status: number) => response.assertStatus(status)
    }
    
    console.log('[Test Helper] Returning result with keys:', Object.keys(result))
    console.log('[Test Helper] Result id:', result.id)
    
    return result
  }

  test('user can create privacy policy template', async ({ client, assert }) => {
    const { authToken } = await createTestUser('templateuser')

    const templateData = {
      name: 'Family Time',
      description: 'Privacy settings for family time',
      permissions: {
        canViewCalendar: true,
        canScheduleMeeting: false,
        showDetails: false,
      },
      isDefault: false,
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(templateData)

    if (response.response.status !== 201) {
      console.log('Error response:', response.body())
    }
    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.name, templateData.name)
    assert.equal(body.data.description, templateData.description)
    assert.deepEqual(body.data.permissions, templateData.permissions)
    assert.equal(body.data.isDefault, templateData.isDefault)
    assert.exists(body.data.id)
    assert.exists(body.data.createdAt)
    assert.exists(body.data.updatedAt)
  }).timeout(10000)

  test('user can create privacy template with policy category', async ({ client, assert }) => {
    const { authToken } = await createTestUser('templatecategoryuser')

    // Create a policy category first
    const category = await createTestPolicyCategory(client, authToken, { name: 'Business' })

    const templateData = {
      name: 'Business Hours',
      description: 'Business hour privacy settings',
      policyCategoryId: category.id,
      isDefault: true,
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(templateData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.name, templateData.name)
    assert.equal(body.data.policyCategoryId, category.id)
    assert.equal(body.data.isDefault, true)
  }).timeout(10000)

  test('user can create minimal privacy template with only name', async ({ client, assert }) => {
    const { authToken } = await createTestUser('minimaltemplateuser')

    const templateData = {
      name: 'Simple Template',
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(templateData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.name, templateData.name)
    assert.equal(body.data.isDefault, false) // Default value
    assert.isNull(body.data.description)
    assert.isNull(body.data.policyCategoryId)
  }).timeout(10000)

  test('privacy template creation validates required fields', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validationtemplateuser')

    // Test missing name
    const response1 = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json({})

    response1.assertStatus(422) // Validation error

    // Test name too long
    const response2 = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json({
        name: 'a'.repeat(256), // Exceeds 255 character limit
      })

    response2.assertStatus(422)

    // Test invalid policy category ID
    const response3 = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json({
        name: 'Valid Template',
        policyCategoryId: 'invalid-uuid',
      })

    response3.assertStatus(422)
  }).timeout(10000)

  test('user can get specific privacy template by ID', async ({ client, assert }) => {
    const { authToken } = await createTestUser('gettemplateuser')

    // Create a privacy template first
    const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id

    // Get the privacy template
    const response = await client
      .get(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.id, templateId)
    assert.equal(body.data.name, 'Work Hours Privacy')
    assert.equal(body.data.description, 'Privacy settings for work hours')
  }).timeout(10000)

  test('user cannot access other users privacy templates', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('templateuser1')
    const { authToken: user2Token } = await createTestUser('templateuser2')

    // User 1 creates a privacy template
    const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id

    // User 2 tries to access User 1's privacy template
    const response = await client
      .get(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can list their privacy templates', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listtemplateuser')

    // Create multiple privacy templates
    await createTestPrivacyTemplate(client, authToken, { name: 'Work Template' })
    await createTestPrivacyTemplate(client, authToken, { name: 'Personal Template' })
    await createTestPrivacyTemplate(client, authToken, { name: 'Public Template' })

    const response = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.exists(body.data.data)
    assert.exists(body.data.pagination)
    assert.isArray(body.data.data)
    assert.isAtLeast(body.data.data.length, 3)
  }).timeout(15000)

  test('privacy template listing supports pagination', async ({ client, assert }) => {
    const { authToken } = await createTestUser('paginationtemplateuser')

    // Create multiple privacy templates
    for (let i = 1; i <= 5; i++) {
      await createTestPrivacyTemplate(client, authToken, { name: `Template ${i}` })
    }

    // Test pagination
    const response = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .qs({ page: 1, limit: 2 })

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.data.length, 2)
    assert.exists(body.data.pagination)
    assert.equal(body.data.pagination.page, 1)
    assert.equal(body.data.pagination.perPage, 2)
    assert.isAtLeast(body.data.pagination.total, 5)
  }).timeout(20000)

  test('privacy template listing supports isDefault filtering', async ({ client, assert }) => {
    const { authToken } = await createTestUser('filtertemplateuser')

    // Create templates with different isDefault values
    await createTestPrivacyTemplate(client, authToken, { name: 'Default Template', isDefault: true })
    await createTestPrivacyTemplate(client, authToken, { name: 'Custom Template', isDefault: false })

    // Filter by isDefault
    const response = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .qs({ isDefault: true })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.data.length, 1)
    // All templates should be default
    body.data.data.forEach((template: any) => {
      assert.equal(template.isDefault, true)
    })
  }).timeout(15000)

  test('privacy template listing supports search', async ({ client, assert }) => {
    const { authToken } = await createTestUser('searchtemplateuser')

    // Create templates with searchable content
    await createTestPrivacyTemplate(client, authToken, { 
      name: 'Work Hours Template', 
      description: 'Business time settings' 
    })
    await createTestPrivacyTemplate(client, authToken, { 
      name: 'Family Template', 
      description: 'Personal family settings' 
    })

    // Search by name
    const response = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .qs({ search: 'Work' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.data.length, 1)
    // Should find the work template
    const foundTemplate = body.data.data.find((template: any) => template.name.includes('Work'))
    assert.exists(foundTemplate)
  }).timeout(15000)

  test('privacy template listing supports sorting', async ({ client, assert }) => {
    console.log('[Test] Starting privacy template listing supports sorting test')
    
    const { authToken } = await createTestUser('sorttemplateuser')
    console.log('[Test] Test user created with token:', authToken ? 'Present' : 'Missing')

    // Create templates with different names
    console.log('[Test] Creating Zebra Template')
    const zebraResult = await createTestPrivacyTemplate(client, authToken, { name: 'Zebra Template', isDefault: false })
    console.log('[Test] Zebra Template created:', zebraResult.id ? 'Success' : 'Failed')
    
    console.log('[Test] Creating Alpha Template')
    const alphaResult = await createTestPrivacyTemplate(client, authToken, { name: 'Alpha Template', isDefault: true })
    console.log('[Test] Alpha Template created:', alphaResult.id ? 'Success' : 'Failed')

    console.log('[Test] Making list request with sorting')
    // Sort by name ascending
    const response = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .qs({ sortBy: 'name', sortDirection: 'asc' })

    console.log('[Test] List response status:', response.response.status)
    console.log('[Test] List response headers:', response.response.headers)
    
    if (response.response.status !== 200) {
      console.error('[Test] List request failed with status:', response.response.status)
      console.error('[Test] List error response body:', response.body())
    }
    
    response.assertStatus(200)
    const body = response.body()
    
    console.log('[Test] List response body structure:', JSON.stringify({
      status: body.status,
      hasData: !!body.data,
      dataType: typeof body.data,
      dataKeys: body.data ? Object.keys(body.data) : 'No data',
      hasDataData: body.data ? !!body.data.data : false,
      dataDataType: body.data && body.data.data ? typeof body.data.data : 'No data.data',
      dataDataLength: body.data && body.data.data ? body.data.data.length : 'No data.data'
    }, null, 2))
    
    console.log('[Test] Full response body:', JSON.stringify(body, null, 2))

    assert.isAtLeast(body.data.data.length, 2)
    // First template should be Alpha (alphabetically first)
    assert.equal(body.data.data[0].name, 'Alpha Template')
    
    console.log('[Test] Sorting test completed successfully')
  }).timeout(15000)

  test('user can update privacy template', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updatetemplateuser')

    // Create a privacy template first
    const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
    createResponse.assertStatus(201)
    
    console.log('[Test Debug] createResponse structure:', JSON.stringify(createResponse, null, 2))
    console.log('[Test Debug] createResponse.body():', JSON.stringify(createResponse.body(), null, 2))
    console.log('[Test Debug] createResponse.body().data:', JSON.stringify(createResponse.body().data, null, 2))
    
    const templateId = createResponse.body().data.id
    console.log('[Test Debug] extracted templateId:', templateId)

    // Update the privacy template
    const updateData = {
      name: 'Updated Work Template',
      description: 'Updated work privacy settings',
      permissions: {
        canViewCalendar: true,
        canScheduleMeeting: false,
        showDetails: false, // Since canViewCalendar:true maps to BUSY_ONLY, showDetails should be false
      },
      isDefault: true,
    }

    const response = await client
      .put(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(authToken)
      .json(updateData)

    if (response.response.status !== 200) {
      console.log('Update error response:', response.body())
    }
    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.name, updateData.name)
    assert.equal(body.data.description, updateData.description)
    assert.deepEqual(body.data.permissions, updateData.permissions)
    assert.equal(body.data.isDefault, updateData.isDefault)
    assert.equal(body.data.id, templateId)
  }).timeout(10000)

  test('user can partially update privacy template', async ({ client, assert }) => {
    const { authToken } = await createTestUser('partialupdatetemplateuser')

    // Create a privacy template first
    const { response: createResponse, templateData } = await createTestPrivacyTemplate(client, authToken)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id
    const originalTemplate = createResponse.body().data

    // Partial update - only name
    const updateData = {
      name: 'Partially Updated Template',
    }

    const response = await client
      .put(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(authToken)
      .json(updateData)

    if (response.response.status !== 200) {
      console.log('Update failed with status:', response.response.status)
      console.log('Error response:', response.body())
    }
    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.name, updateData.name)
    // Other fields should remain unchanged (compare with what the API originally returned)
    assert.equal(body.data.description, originalTemplate.description)
    assert.deepEqual(body.data.permissions, originalTemplate.permissions)
  }).timeout(10000)

  test('user cannot update other users privacy templates', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('updatetemplateuser1')
    const { authToken: user2Token } = await createTestUser('updatetemplateuser2')

    // User 1 creates a privacy template
    const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id

    // User 2 tries to update User 1's privacy template
    const response = await client
      .put(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(user2Token)
      .json({ name: 'Hacked Template' })

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can delete privacy template', async ({ client, assert }) => {
    const { authToken } = await createTestUser('deletetemplateuser')

    // Create a privacy template first
    const { response: createResponse } = await createTestPrivacyTemplate(client, authToken)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id

    // Delete the privacy template
    const response = await client
      .delete(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.message)

    // Verify privacy template is deleted (should return 404)
    const getResponse = await client
      .get(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(404)
  }).timeout(10000)

  test('user cannot delete other users privacy templates', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('deletetemplateuser1')
    const { authToken: user2Token } = await createTestUser('deletetemplateuser2')

    // User 1 creates a privacy template
    const { response: createResponse } = await createTestPrivacyTemplate(client, user1Token)
    createResponse.assertStatus(201)
    const templateId = createResponse.body().data.id

    // User 2 tries to delete User 1's privacy template
    const response = await client
      .delete(`/api/v1/privacy-profiles/${templateId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('privacy template operations require authentication', async ({ client, assert }) => {
    // Test all endpoints without authentication
    const response1 = await client.get('/api/v1/privacy-profiles')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/privacy-profiles').json({ name: 'Test' })
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/privacy-profiles/123')
    response3.assertStatus(401)

    const response4 = await client.put('/api/v1/privacy-profiles/123').json({ name: 'Test' })
    response4.assertStatus(401)

    const response5 = await client.delete('/api/v1/privacy-profiles/123')
    response5.assertStatus(401)
  }).timeout(10000)

  test('privacy template with category relationship', async ({ client, assert }) => {
    const { authToken } = await createTestUser('relationshiptemplateuser')

    // Create a policy category first
    const category = await createTestPolicyCategory(client, authToken, { name: 'Project Work' })

    // Create a template with category relationship
    const templateData = {
      name: 'Project Privacy',
      description: 'Privacy for project work',
      policyCategoryId: category.id,
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(templateData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.data.policyCategoryId, category.id)

    // Verify relationship through listing with category filter
    const listResponse = await client
      .get('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .qs({ policyCategory: category.id })

    listResponse.assertStatus(200)
    const listBody = listResponse.body()

    assert.isAtLeast(listBody.data.data.length, 1)
    const foundTemplate = listBody.data.data.find((t: any) => t.id === body.data.id)
    assert.exists(foundTemplate)
  }).timeout(15000)
})