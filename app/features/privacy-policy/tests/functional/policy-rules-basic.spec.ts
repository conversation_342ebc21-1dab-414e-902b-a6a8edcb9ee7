import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Policy Template Rules Basic', (group) => {
  // Use cleanup instead of global transactions to prevent cascade failures
  group.each.teardown(async (test) => {
    // Clean up in reverse order to handle foreign key constraints
    const tables = [
      'policy_template_rules',
      'privacy_policy_templates', 
      'policy_categories',
      'profiles',
      'users',
      'auth_users'
    ]
    for (const table of tables) {
      try {
        await db.from(table).del()
      } catch (error) {
        // Ignore foreign key constraint errors during cleanup
        if (!error.message.includes('violates foreign key constraint')) {
          console.warn(`Failed to clean table ${table}:`, error.message)
        }
      }
    }
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  // Helper function to create a test privacy policy template
  async function createTestPrivacyTemplate(client: ApiClient, authToken: string, templateData = {}) {
    const defaultTemplateData = {
      name: 'Test Template',
      description: 'Test privacy template',
      ...templateData,
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(defaultTemplateData)

    return response
  }

  // Helper function to create a test policy rule
  async function createTestPolicyRule(client: ApiClient, authToken: string, templateId: string, ruleData = {}) {
    const defaultRuleData = {
      templateId,
      viewerScopeType: 'public',
      detailVisibility: 'title_only',
      blocksScheduling: false,
      priority: 50,
      ...ruleData,
    }

    const response = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json(defaultRuleData)

    // Return consistent structure that works with both usage patterns
    return { response }
  }

  test('user can create policy template rule', async ({ client, assert }) => {
    const { authToken } = await createTestUser('ruleuser')

    // Create a privacy template first
    const templateResponse = await createTestPrivacyTemplate(client, authToken, { name: 'Conflict Resolution Template' })
    const template = templateResponse.body().data

    const ruleData = {
      templateId: template.id, // Ensure templateId is passed
      viewerScopeType: 'all_contacts',
      detailVisibility: 'busy_only',
      blocksScheduling: true,
      customMessage: 'Currently in a meeting',
      priority: 75,
    }

    const response = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json(ruleData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.templateId, ruleData.templateId)
    assert.equal(body.data.viewerScopeType, ruleData.viewerScopeType)
    assert.equal(body.data.detailVisibility, ruleData.detailVisibility)
    assert.equal(body.data.blocksScheduling, ruleData.blocksScheduling)
    assert.equal(body.data.customMessage, ruleData.customMessage)
    assert.equal(body.data.priority, ruleData.priority)
    assert.exists(body.data.id)
    assert.exists(body.data.createdAt)
    assert.exists(body.data.updatedAt)
  }).timeout(10000)

  test('user can create minimal policy rule', async ({ client, assert }) => {
    const { authToken } = await createTestUser('minimalruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    const ruleData = {
      templateId: template.id, // Ensure templateId is passed
      viewerScopeType: 'public',
      detailVisibility: 'hidden',
      blocksScheduling: false, // Add required field
      priority: 50, // Add required field
    }

    const response = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json(ruleData)

    if (response.status !== 201) {
      console.log('Policy rule creation failed with status:', response.status)
      console.log('Response body:', JSON.stringify(response.body(), null, 2))
      console.log('Request data:', JSON.stringify(ruleData, null, 2))
    }
    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.templateId, ruleData.templateId)
    assert.equal(body.data.viewerScopeType, ruleData.viewerScopeType)
    assert.equal(body.data.detailVisibility, ruleData.detailVisibility)
    assert.equal(body.data.blocksScheduling, false) // Default value
    assert.isNull(body.data.customMessage)
    assert.exists(body.data.priority) // Should have default priority
  }).timeout(10000)

  test('policy rule creation validates required fields and enums', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validationruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Test missing templateId
    const response1 = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json({
        viewerScopeType: 'public',
        detailVisibility: 'hidden',
      })

    response1.assertStatus(422) // Validation error

    // Test invalid viewerScopeType
    const response2 = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json({
        templateId: template.id,
        viewerScopeType: 'invalid_scope',
        detailVisibility: 'hidden',
      })

    response2.assertStatus(422)

    // Test invalid detailVisibility
    const response3 = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json({
        templateId: template.id,
        viewerScopeType: 'public',
        detailVisibility: 'invalid_visibility',
      })

    response3.assertStatus(422)

    // Test invalid priority range (outside 1-100)
    const response4 = await client
      .post('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .json({
        templateId: template.id,
        viewerScopeType: 'public',
        detailVisibility: 'hidden',
        priority: 150, // Outside valid range
      })

    response4.assertStatus(422)
  }).timeout(10000)

  test('user can get specific policy rule by ID', async ({ client, assert }) => {
    const { authToken } = await createTestUser('getruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create a policy rule
    const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
    createResponse.assertStatus(201)
    const ruleId = createResponse.body().data.id

    // Get the policy rule
    const response = await client
      .get(`/api/v1/privacy-profile-rules/${ruleId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.id, ruleId)
    assert.equal(body.data.templateId, template.id)
    assert.equal(body.data.viewerScopeType, 'public')
    assert.equal(body.data.detailVisibility, 'title_only')
  }).timeout(10000)

  test('user cannot access other users policy rules', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('ruleuser1')
    const { authToken: user2Token } = await createTestUser('ruleuser2')

    // User 1 creates a template and rule
    const template = await createTestPrivacyTemplate(client, user1Token)
    const { response: createResponse } = await createTestPolicyRule(client, user1Token, template.id)
    createResponse.assertStatus(201)
    const ruleId = createResponse.body().data.id

    // User 2 tries to access User 1's policy rule
    const response = await client
      .get(`/api/v1/privacy-profile-rules/${ruleId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can list their policy rules', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create multiple policy rules
    await createTestPolicyRule(client, authToken, template.id, { priority: 10 })
    await createTestPolicyRule(client, authToken, template.id, { priority: 20 })
    await createTestPolicyRule(client, authToken, template.id, { priority: 30 })

    const response = await client
      .get('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.exists(body.data.data)
    assert.exists(body.data.pagination)
    assert.isArray(body.data)
    assert.isAtLeast(body.data.data.length, 3)
  }).timeout(15000)

  test('user can update policy rule', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updateruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create a policy rule
    const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
    createResponse.assertStatus(201)
    const ruleId = createResponse.body().data.id

    // Update the policy rule
    const updateData = {
      viewerScopeType: 'specific_contact',
      detailVisibility: 'full_details',
      blocksScheduling: true,
      customMessage: 'Updated message',
      priority: 90,
    }

    const response = await client
      .put(`/api/v1/privacy-profile-rules/${ruleId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.viewerScopeType, updateData.viewerScopeType)
    assert.equal(body.data.detailVisibility, updateData.detailVisibility)
    assert.equal(body.data.blocksScheduling, updateData.blocksScheduling)
    assert.equal(body.data.customMessage, updateData.customMessage)
    assert.equal(body.data.priority, updateData.priority)
    assert.equal(body.data.id, ruleId)
  }).timeout(10000)

  test('user can delete policy rule', async ({ client, assert }) => {
    const { authToken } = await createTestUser('deleteruleuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create a policy rule
    const { response: createResponse } = await createTestPolicyRule(client, authToken, template.id)
    createResponse.assertStatus(201)
    const ruleId = createResponse.body().data.id

    // Delete the policy rule
    const response = await client
      .delete(`/api/v1/privacy-profile-rules/${ruleId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.message)

    // Verify policy rule is deleted (should return 404)
    const getResponse = await client
      .get(`/api/v1/privacy-profile-rules/${ruleId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(404)
  }).timeout(10000)

  test('policy rule operations require authentication', async ({ client, assert }) => {
    // Test all endpoints without authentication
    const response1 = await client.get('/api/v1/privacy-profile-rules')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/privacy-profile-rules').json({ 
      templateId: 'test-id',
      viewerScopeType: 'public',
      detailVisibility: 'hidden'
    })
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/privacy-profile-rules/123')
    response3.assertStatus(401)

    const response4 = await client.put('/api/v1/privacy-profile-rules/123').json({ priority: 50 })
    response4.assertStatus(401)

    const response5 = await client.delete('/api/v1/privacy-profile-rules/123')
    response5.assertStatus(401)
  }).timeout(10000)

  // Priority and precedence testing
  test('policy rules support priority-based precedence', async ({ client, assert }) => {
    const { authToken } = await createTestUser('precedenceuser')

    // Create a privacy template first
    const templateResponse = await createTestPrivacyTemplate(client, authToken, { name: 'Precedence Template' })
    const template = templateResponse.body().data

    // Create rules with different priorities
    const highPriorityRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'public',
      detailVisibility: 'hidden',
      priority: 90,
      customMessage: 'High priority rule',
    })

    const mediumPriorityRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'all_contacts',
      detailVisibility: 'busy_only',
      priority: 50,
      customMessage: 'Medium priority rule',
    })

    const lowPriorityRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'specific_contact',
      detailVisibility: 'full_details',
      priority: 10,
      customMessage: 'Low priority rule',
    })

    // All should be created successfully
    highPriorityRule.response.assertStatus(201)
    mediumPriorityRule.response.assertStatus(201)
    lowPriorityRule.response.assertStatus(201)

    // List rules and verify they can be sorted by priority
    const response = await client
      .get('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .qs({ sortBy: 'priority', sortDirection: 'desc' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.data.length, 3)
    
    // Find our rules in the sorted list
    const rules = body.data.data
    const highRule = rules.find((r: any) => r.priority === 90)
    const medRule = rules.find((r: any) => r.priority === 50)
    const lowRule = rules.find((r: any) => r.priority === 10)

    assert.exists(highRule)
    assert.exists(medRule)
    assert.exists(lowRule)

    // Verify rule content
    assert.equal(highRule.detailVisibility, 'hidden')
    assert.equal(medRule.detailVisibility, 'busy_only')
    assert.equal(lowRule.detailVisibility, 'full_details')
  }).timeout(20000)

  test('policy rules with same priority are handled consistently', async ({ client, assert }) => {
    const { authToken } = await createTestUser('samepriority')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create multiple rules with same priority
    const rule1 = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'public',
      detailVisibility: 'hidden',
      priority: 50,
      customMessage: 'Rule 1',
    })

    const rule2 = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'all_contacts',
      detailVisibility: 'busy_only',
      priority: 50,
      customMessage: 'Rule 2',
    })

    // Both should be created successfully
    rule1.response.assertStatus(201)
    rule2.response.assertStatus(201)

    // List rules to verify both exist
    const response = await client
      .get('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    const samePriorityRules = body.data.filter((r: any) => r.priority === 50)
    assert.isAtLeast(samePriorityRules.length, 2)
  }).timeout(15000)

  test('policy rules with scope-specific target validation', async ({ client, assert }) => {
    const { authToken } = await createTestUser('scopetargetuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    // Create rule with specific_contact scope (would need target in real scenario)
    const contactScopeRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'specific_contact',
      detailVisibility: 'title_only',
      priority: 60,
    })

    // Create rule with specific_group scope
    const groupScopeRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'specific_group',
      detailVisibility: 'busy_only',
      priority: 70,
    })

    // Both should be created successfully (target validation may be handled at business logic level)
    contactScopeRule.response.assertStatus(201)
    groupScopeRule.response.assertStatus(201)

    assert.equal(contactScopeRule.response.body().data.viewerScopeType, 'specific_contact')
    assert.equal(groupScopeRule.response.body().data.viewerScopeType, 'specific_group')
  }).timeout(15000)

  test('policy rules conflict resolution through priority ordering', async ({ client, assert }) => {
    const { authToken } = await createTestUser('conflictuser')

    // Create a privacy template first
    const templateResponse = await createTestPrivacyTemplate(client, authToken, { name: 'Conflict Resolution Template' })
    const template = templateResponse.body().data

    // Create conflicting rules for same scope but different priorities
    const restrictiveRule = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'public',
      detailVisibility: 'hidden',
      blocksScheduling: true,
      priority: 80,
      customMessage: 'High priority - hide everything',
    })

    const permissiveRuleResponse = await createTestPolicyRule(client, authToken, template.id, {
      viewerScopeType: 'public',
      detailVisibility: 'full_details',
      blocksScheduling: false,
      priority: 20,
      customMessage: 'Low priority - show everything',
    })

    // Both should be created
    restrictiveRule.response.assertStatus(201)
    permissiveRuleResponse.response.assertStatus(201)

    // List rules ordered by priority to verify conflict resolution order
    const response = await client
      .get('/api/v1/privacy-profile-rules')
      .bearerToken(authToken)
      .qs({ sortBy: 'priority', sortDirection: 'desc' })

    response.assertStatus(200)
    const body = response.body()

    const rules = body.data.data
    const highRule = rules.find((r: any) => r.priority === 80)
    const lowRule = rules.find((r: any) => r.priority === 20)

    assert.exists(highRule)
    assert.exists(lowRule)

    // High priority rule should be more restrictive
    assert.equal(highRule.detailVisibility, 'hidden')
    assert.equal(highRule.blocksScheduling, true)

    // Low priority rule should be more permissive
    assert.equal(lowRule.detailVisibility, 'full_details')
    assert.equal(lowRule.blocksScheduling, false)
  }).timeout(20000)

  test('policy rules support all valid enum values', async ({ client, assert }) => {
    const { authToken } = await createTestUser('enumuser')

    // Create a privacy template first
    const template = await createTestPrivacyTemplate(client, authToken)

    const scopeTypes = ['public', 'all_contacts', 'specific_contact', 'specific_group', 'specific_contact_list']
    const visibilityLevels = ['hidden', 'busy_only', 'title_only', 'full_details']

    // Test each scope type
    for (const scope of scopeTypes) {
      const { response } = await createTestPolicyRule(client, authToken, template.id, {
        viewerScopeType: scope,
        detailVisibility: 'title_only',
        priority: 50,
      })

      response.assertStatus(201)
      assert.equal(response.body().data.viewerScopeType, scope)
    }

    // Test each visibility level
    for (const visibility of visibilityLevels) {
      const { response } = await createTestPolicyRule(client, authToken, template.id, {
        viewerScopeType: 'public',
        detailVisibility: visibility,
        priority: 40,
      })

      response.assertStatus(201)
      assert.equal(response.body().data.detailVisibility, visibility)
    }
  }).timeout(25000)
})