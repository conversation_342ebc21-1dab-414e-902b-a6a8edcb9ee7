import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import PrivacyPolicyTemplate from './template/privacy_policy_template_model.js'

export default class PrivacyPolicyAssignment extends BaseModel {
  static table = 'privacy_policy_assignments'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare activityId: string

  @column()
  declare templateId: string

  // Override fields (optional, inherits from template if null)
  @column()
  declare overrideBlocksScheduling: boolean | null // Renamed from overrideIsAccountedFor

  @column()
  declare overrideDetailVisibility: ActivityDetailVisibilityLevel | null

  @column()
  declare overrideCustomMessage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => PrivacyPolicyTemplate, {
    foreignKey: 'templateId',
  })
  declare template: BelongsTo<typeof PrivacyPolicyTemplate>

  // Activity relationship will be added when Activity model is available/updated
  // @belongsTo(() => Activity, {
  //   foreignKey: 'activityId',
  // })
  // declare activity: BelongsTo<typeof Activity>
}
