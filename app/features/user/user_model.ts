import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo, hasMany, hasOne } from '@adonisjs/lucid/orm'
import AuthUser from '#models/auth_user_model'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import Task from '#features/task/task_model'
import UserContact from '#features/contact/user_contact_model'
import UserContactList from '#features/contact/user_contact_list_model'
import ContactListMember from '#features/contact/contact_list_member_model'
import Profile from '#features/profile/profile_model'

export default class User extends compose(BaseModel, SoftDeletes) {
  @column({ isPrimary: true })
  declare id: string

  @column({ columnName: 'auth_user_id' })
  declare authUserId: string

  @belongsTo(() => AuthUser, { foreignKey: 'authUserId' })
  declare user: BelongsTo<typeof AuthUser>

  @hasOne(() => Profile, { foreignKey: 'userId' })
  declare profile: HasOne<typeof Profile>

  @hasMany(() => Task, { foreignKey: 'userId' })
  declare tasks: HasMany<typeof Task>

  /**
   * Contact requests initiated by this user
   */
  @hasMany(() => UserContact, { foreignKey: 'requesterUserId' })
  declare sentContactRequests: HasMany<typeof UserContact>

  /**
   * Contact requests received by this user
   */
  @hasMany(() => UserContact, { foreignKey: 'addresseeUserId' })
  declare receivedContactRequests: HasMany<typeof UserContact>

  /**
   * Contact lists owned by this user
   */
  @hasMany(() => UserContactList, { foreignKey: 'ownerUserId' })
  declare ownedContactLists: HasMany<typeof UserContactList>

  /**
   * Contact list memberships where this user is a member
   */
  @hasMany(() => ContactListMember, { foreignKey: 'userId' })
  declare contactListMemberships: HasMany<typeof ContactListMember>
}
