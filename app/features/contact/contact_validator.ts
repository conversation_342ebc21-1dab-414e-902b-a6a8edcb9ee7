import vine from '@vinejs/vine'
import { createQueryValidator } from '#validators/base_validators'
import { CONTACT_REQUEST_STATUS_VALUES } from '#types/contact_types'

// Validator for querying contacts
export const contactQueryValidator = createQueryValidator({
  statusOptions: CONTACT_REQUEST_STATUS_VALUES,
  sortFields: ['createdAt', 'updatedAt', 'status'],
  additionalFields: {
    includeRequests: vine.boolean().optional(),
    includeReceived: vine.boolean().optional(),
  },
  maxLimit: 100,
})
