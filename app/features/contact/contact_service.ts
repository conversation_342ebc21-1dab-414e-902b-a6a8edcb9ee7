import UserContact from './user_contact_model.js'
import UserContactList from './user_contact_list_model.js'
import ContactListMember from './contact_list_member_model.js'
import User from '#features/user/user_model'
import {
  ContactRequestStatus,
  CreateContactRequestData,
  UpdateContactRequestData,
  CreateContactListData,
  UpdateContactListData,
  AddContactListMemberData,
  ContactFilters,
  GetContactsOptions,
} from '#types/contact_types'
import {
  notFoundException,
  badRequestException,
  validationException,
  authorizationException,
} from '#utils/error'
import db from '@adonisjs/lucid/services/db'
import { PaginatedResponse } from '#types/base_query_types'
import { PaginationHelper } from '#utils/pagination'

export default class ContactService {
  // ===== CONTACT REQUEST MANAGEMENT =====

  /**
   * Send a contact request to another user
   */
  async sendContactRequest(
    requesterUserId: string,
    data: CreateContactRequestData
  ): Promise<UserContact> {
    const { addresseeUserId } = data

    // Validate that users exist
    let requester
    let addressee
    try {
      requester = await User.findOrFail(requesterUserId)
      console.log('✅ Requester found:', requester.id)
    } catch (error) {
      console.log('❌ Requester NOT found:', requesterUserId)
      throw notFoundException(`Requester user with ID ${requesterUserId} not found`)
    }

    try {
      addressee = await User.findOrFail(addresseeUserId)
      console.log('✅ Addressee found:', addressee.id)
    } catch (error) {
      console.log('❌ Addressee NOT found:', addresseeUserId)
      throw notFoundException(`Addressee user with ID ${addresseeUserId} not found`)
    }

    // Prevent self-contact
    if (requesterUserId === addresseeUserId) {
      throw badRequestException('You cannot send a contact request to yourself')
    }

    // Check if a contact relationship already exists
    const existingContact = await UserContact.query()
      .where((query) => {
        query.where('requesterUserId', requesterUserId).andWhere('addresseeUserId', addresseeUserId)
      })
      .orWhere((query) => {
        query.where('requesterUserId', addresseeUserId).andWhere('addresseeUserId', requesterUserId)
      })
      .first()

    if (existingContact) {
      if (existingContact.status === ContactRequestStatus.PENDING) {
        throw badRequestException('A contact request is already pending between these users')
      }
      if (existingContact.status === ContactRequestStatus.ACCEPTED) {
        throw badRequestException('These users are already connected')
      }
      if (existingContact.isBlocked()) {
        throw badRequestException('Contact request cannot be sent due to blocking')
      }
    }

    // Create the contact request
    const contact = await UserContact.create({
      requesterUserId,
      addresseeUserId,
      status: ContactRequestStatus.PENDING,
    })

    // Load relationships including profiles for complete response
    await contact.load('requester', (requesterQuery) => {
      requesterQuery.preload('profile')
    })
    await contact.load('addressee', (addresseeQuery) => {
      addresseeQuery.preload('profile')
    })

    return contact
  }

  /**
   * Respond to a contact request (accept, decline, or block)
   */
  async respondToContactRequest(
    contactId: string,
    addresseeUserId: string,
    data: UpdateContactRequestData
  ): Promise<UserContact> {
    const { status } = data

    // Find the contact request
    const contact = await UserContact.findOrFail(contactId)

    // Verify that the responding user is the addressee
    if (status === ContactRequestStatus.ACCEPTED && contact.addresseeUserId !== addresseeUserId) {
      throw authorizationException('You can only respond to contact requests sent to you')
    }

    // Validate the new status
    const allowedResponses = [
      ContactRequestStatus.ACCEPTED,
      ContactRequestStatus.DECLINED,
      ContactRequestStatus.BLOCKED_BY_ADDRESSEE,
    ]
    if (!allowedResponses.includes(status)) {
      throw validationException(
        'Invalid response status. Must be accepted, declined, or blockedByAddressee'
      )
    }

    // Update the contact status
    contact.status = status
    await contact.save()

    // Load relationships including profiles for complete response
    await contact.load('requester', (requesterQuery) => {
      requesterQuery.preload('profile')
    })
    await contact.load('addressee', (addresseeQuery) => {
      addresseeQuery.preload('profile')
    })

    return contact
  }

  /**
   * Block a contact (can be done by either party)
   */
  async blockContact(contactId: string, userId: string): Promise<UserContact> {
    const contact = await UserContact.findOrFail(contactId)

    // Verify that the user is involved in this contact
    if (!contact.involvesUser(userId)) {
      throw authorizationException('You can only block contacts that involve you')
    }

    // Determine the appropriate block status
    const blockStatus =
      contact.requesterUserId === userId
        ? ContactRequestStatus.BLOCKED_BY_REQUESTER
        : ContactRequestStatus.BLOCKED_BY_ADDRESSEE

    contact.status = blockStatus
    await contact.save()

    await contact.load('requester', (requesterQuery) => {
      requesterQuery.preload('profile')
    })
    await contact.load('addressee', (addresseeQuery) => {
      addresseeQuery.preload('profile')
    })

    return contact
  }

  /**
   * Get all contacts for a user with filtering options
   */
  async getUserContacts(
    userId: string,
    options: GetContactsOptions = {}
  ): Promise<PaginatedResponse<UserContact>> {
    const { queryParams = {} } = options as unknown as { queryParams?: any }
    const { filters = {} } = queryParams
    const { status, includeRequests = true, includeReceived = true } = filters as ContactFilters

    // Extract pagination parameters
    const paginationParams = PaginationHelper.extractPaginationParams(queryParams)

    let query = UserContact.query()

    // Filter by involvement
    if (includeRequests && includeReceived) {
      query = query.where((q) => {
        q.where('requesterUserId', userId).orWhere('addresseeUserId', userId)
      })
    } else if (includeRequests) {
      query = query.where('requesterUserId', userId)
    } else if (includeReceived) {
      query = query.where('addresseeUserId', userId)
    } else {
      // If neither is included, return empty
      return PaginationHelper.emptyResponse<UserContact>()
    }

    // Filter by status if provided
    if (status) {
      query = query.where('status', status)
    }

    // Default sort by most recent
    query = query.orderBy('createdAt', 'desc')

    // Always preload both requester and addressee with profiles for now
    query = query
      .preload('requester', (q) => q.preload('profile'))
      .preload('addressee', (q) => q.preload('profile'))

    // Apply pagination using the helper
    return PaginationHelper.paginate<UserContact>(query, paginationParams)
  }

  // ===== CONTACT LIST MANAGEMENT =====

  /**
   * Create a new contact list
   */
  async createContactList(ownerUserId: string, data: CreateContactListData): Promise<any> {
    const { name } = data

    // Verify user exists
    await User.findOrFail(ownerUserId)

    // Check if a list with this name already exists for the user
    const existingList = await UserContactList.query()
      .where('ownerUserId', ownerUserId)
      .where('name', name)
      .first()

    if (existingList) {
      throw badRequestException('A contact list with this name already exists')
    }

    // Create the contact list
    const contactList = await UserContactList.create({
      ownerUserId,
      name,
    })

    await contactList.load('owner')
    await contactList.load('members', (memberQuery) => {
      memberQuery.preload('user', (userQuery: any) => {
        userQuery.preload('profile')
      })
    })

    // Transform the response to clean up member data
    return {
      id: contactList.id,
      ownerUserId: contactList.ownerUserId,
      name: contactList.name,
      createdAt: contactList.createdAt,
      updatedAt: contactList.updatedAt,
      owner: contactList.owner,
      members: contactList.members.map((member) => ({
        id: member.id,
        listId: member.listId,
        userId: member.userId,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        profile: member.user?.profile || null,
      })),
    }
  }

  /**
   * Get all contact lists for a user
   */
  async getUserContactLists(ownerUserId: string): Promise<any[]> {
    const contactLists = await UserContactList.query()
      .where('ownerUserId', ownerUserId)
      .preload('owner')
      .preload('members', (memberQuery) => {
        memberQuery.preload('user', (userQuery: any) => {
          userQuery.preload('profile')
        })
      })
      .orderBy('name', 'asc')

    // Transform the response to clean up member data
    return contactLists.map((list) => ({
      id: list.id,
      ownerUserId: list.ownerUserId,
      name: list.name,
      createdAt: list.createdAt,
      updatedAt: list.updatedAt,
      owner: list.owner,
      members: list.members.map((member) => ({
        id: member.id,
        listId: member.listId,
        userId: member.userId,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        profile: member.user?.profile || null,
      })),
    }))
  }

  /**
   * Get a specific contact list by ID
   */
  async getContactList(listId: string, ownerUserId: string): Promise<any> {
    const contactList = await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .preload('owner')
      .preload('members', (memberQuery) => {
        memberQuery.preload('user', (userQuery: any) => {
          userQuery.preload('profile')
        })
      })
      .firstOrFail()

    // Transform the response to clean up member data
    return {
      id: contactList.id,
      ownerUserId: contactList.ownerUserId,
      name: contactList.name,
      createdAt: contactList.createdAt,
      updatedAt: contactList.updatedAt,
      owner: contactList.owner,
      members: contactList.members.map((member) => ({
        id: member.id,
        listId: member.listId,
        userId: member.userId,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        profile: member.user?.profile || null,
      })),
    }
  }

  /**
   * Update a contact list
   */
  async updateContactList(
    listId: string,
    ownerUserId: string,
    data: UpdateContactListData
  ): Promise<any> {
    const { name } = data

    // Find the contact list and verify ownership
    const contactList = await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .firstOrFail()

    // Check if the new name conflicts with existing lists
    if (name !== contactList.name) {
      const existingList = await UserContactList.query()
        .where('ownerUserId', ownerUserId)
        .where('name', name)
        .where('id', '!=', listId)
        .first()

      if (existingList) {
        throw badRequestException('A contact list with this name already exists')
      }
    }

    // Update the contact list
    contactList.name = name
    await contactList.save()

    await contactList.load('owner')
    await contactList.load('members', (memberQuery) => {
      memberQuery.preload('user', (userQuery: any) => {
        userQuery.preload('profile')
      })
    })

    // Transform the response to clean up member data
    return {
      id: contactList.id,
      ownerUserId: contactList.ownerUserId,
      name: contactList.name,
      createdAt: contactList.createdAt,
      updatedAt: contactList.updatedAt,
      owner: contactList.owner,
      members: contactList.members.map((member) => ({
        id: member.id,
        listId: member.listId,
        userId: member.userId,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        profile: member.user?.profile || null,
      })),
    }
  }

  /**
   * Delete a contact list
   */
  async deleteContactList(listId: string, ownerUserId: string): Promise<void> {
    // Find the contact list and verify ownership
    const contactList = await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .firstOrFail()

    // Use transaction to ensure data consistency
    await db.transaction(async (trx) => {
      // Delete all members first
      await ContactListMember.query({ client: trx }).where('listId', listId).delete()

      // Delete the contact list
      await contactList.useTransaction(trx).delete()
    })
  }

  // ===== CONTACT LIST MEMBER MANAGEMENT =====

  /**
   * Add a contact to a contact list
   */
  async addContactToList(
    listId: string,
    ownerUserId: string,
    data: AddContactListMemberData
  ): Promise<any> {
    const { userId } = data

    // Verify the contact list exists and is owned by the user
    await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .firstOrFail()

    // Verify the user to be added exists
    await User.findOrFail(userId)

    // Verify that there's an accepted contact relationship
    const acceptedContact = await UserContact.query()
      .where('status', ContactRequestStatus.ACCEPTED)
      .where((query) => {
        query.where('requesterUserId', ownerUserId).andWhere('addresseeUserId', userId)
      })
      .orWhere((query) => {
        query.where('requesterUserId', userId).andWhere('addresseeUserId', ownerUserId)
      })
      .first()

    if (!acceptedContact) {
      throw badRequestException('You can only add accepted contacts to your lists')
    }

    // Check if the user is already in the list
    const existingMember = await ContactListMember.query()
      .where('listId', listId)
      .where('userId', userId)
      .first()

    if (existingMember) {
      throw badRequestException('This user is already a member of this contact list')
    }

    // Add the member to the list
    const member = await ContactListMember.create({
      listId,
      userId,
    })

    await member.load('contactList')
    await member.load('user', (userQuery: any) => {
      userQuery.preload('profile')
    })

    // Return clean response structure
    return {
      id: member.id,
      listId: member.listId,
      userId: member.userId,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
      profile: member.user?.profile || null,
    }
  }

  /**
   * Remove a contact from a contact list
   */
  async removeContactFromList(
    listId: string,
    memberId: string,
    ownerUserId: string
  ): Promise<void> {
    // Verify the contact list exists and is owned by the user
    await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .firstOrFail()

    // Find and delete the member
    const member = await ContactListMember.query()
      .where('id', memberId)
      .where('listId', listId)
      .firstOrFail()

    await member.delete()
  }

  /**
   * Get all members of a contact list
   */
  async getContactListMembers(listId: string, ownerUserId: string): Promise<any[]> {
    // Verify the contact list exists and is owned by the user
    await UserContactList.query()
      .where('id', listId)
      .where('ownerUserId', ownerUserId)
      .firstOrFail()

    const members = await ContactListMember.query()
      .where('listId', listId)
      .preload('user', (userQuery: any) => {
        userQuery.preload('profile')
      })
      .orderBy('createdAt', 'asc')

    // Transform the response to only include necessary data
    return members.map((member) => ({
      id: member.id,
      listId: member.listId,
      userId: member.userId,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
      profile: member.user?.profile || null,
    }))
  }

  /**
   * Remove a contact from any contact list by member ID
   */
  async removeContactListMember(memberId: string, ownerUserId: string): Promise<void> {
    // Find the contact list member and preload the associated contact list
    const member = await ContactListMember.query()
      .where('id', memberId)
      .preload('contactList', (query) => query.select(['id', 'ownerUserId']))
      .firstOrFail()

    if (!member.contactList) {
      throw notFoundException('Associated contact list not found')
    }

    // Verify ownership of the contact list
    if (member.contactList.ownerUserId !== ownerUserId) {
      throw authorizationException('You are not authorized to remove this member from the list')
    }

    await member.delete()
  }

  /**
   * Comply with BaseResourceService - get resource by ID
   */
  async getResourceById(id: string): Promise<UserContact | null> {
    return UserContact.find(id)
  }

  /**
   * Comply with BaseResourceService - get resources wrapper
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean }
  ): Promise<PaginatedResponse<UserContact>> {
    const { queryParams, paginate } = options
    return this.getUserContacts(userId, {
      queryParams: queryParams || {},
      paginate: paginate !== false, // default true
    } as unknown as GetContactsOptions)
  }

  /**
   * Comply with BaseResourceService - delete resource (not supported for contacts)
   */
  async deleteResource(_id: string, _userId: string): Promise<boolean> {
    // Contact deletion is not supported currently
    return false
  }
}
