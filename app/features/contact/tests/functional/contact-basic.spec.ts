import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Contact Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
      user: { userId: testUser.userId }, // Maintain compatibility with existing test structure
    }
  }

  test('user can view contacts', async ({ client, assert }) => {
    const { authToken } = await createTestUser('contactviewer')

    const response = await client.get('/api/v1/contacts').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(10000)

  test('user can send contact request', async ({ client, assert }) => {
    const { authToken } = await createTestUser('requester')
    const { user: targetUser } = await createTestUser('target')

    const requestData = {
      addresseeUserId: targetUser.userId,
      message: 'Hi! Let\'s connect!'
    }

    const response = await client
      .post('/api/v1/contacts/requests')
      .bearerToken(authToken)
      .json(requestData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.addresseeUserId, targetUser.userId)
  }).timeout(10000)

  test('user can create contact list', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listcreator')

    const listData = {
      name: 'Test Contacts',
      description: 'Test contact list'
    }

    const response = await client
      .post('/api/v1/contacts/lists')
      .bearerToken(authToken)
      .json(listData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.name, listData.name)
  }).timeout(10000)

  test('user can view contact lists', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listviewer')

    const response = await client.get('/api/v1/contacts/lists').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.isArray(body.data)
  }).timeout(10000)

  test('contact operations require authentication', async ({ client, assert }) => {
    const response1 = await client.get('/api/v1/contacts')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/contacts/requests').json({
      addresseeUserId: 'test-id'
    })
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/contacts/lists')
    response3.assertStatus(401)
  }).timeout(10000)
})