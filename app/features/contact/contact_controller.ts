import type { HttpContext } from '@adonisjs/core/http'
import ContactService from './contact_service.js'
import { badRequestException } from '#utils/error'
import { createSuccessResponse } from '#utils/response'
import { ContactRequestStatus, isContactRequestStatus, ContactFilters } from '#types/contact_types'

// Query validator for contacts
import { contactQueryValidator } from '#features/contact/contact_validator'
import UserContactList from './user_contact_list_model.js'
import vine from '@vinejs/vine'

export default class ContactController {
  private contactService = new ContactService()
  private baseController: any = null

  // ===== Validators for contact lists =====
  private createListValidator = vine.compile(
    vine.object({
      name: vine.string().trim().minLength(1).maxLength(255),
    })
  )

  private updateListValidator = this.createListValidator

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.contactService,
      resourceName: 'contact',
      validateQueryParams: async (request: any) =>
        await request.validateUsing(contactQueryValidator),
      getStatusMapping: () => ({
        pending: ContactRequestStatus.PENDING,
        accepted: ContactRequestStatus.ACCEPTED,
        declined: ContactRequestStatus.DECLINED,
        blockedByRequester: ContactRequestStatus.BLOCKED_BY_REQUESTER,
        blockedByAddressee: ContactRequestStatus.BLOCKED_BY_ADDRESSEE,
      }),
      getSortMapping: () => ({
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
        status: 'status',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<ContactFilters>) => {
        if (queryParams.includeRequests !== undefined) {
          filters.includeRequests =
            queryParams.includeRequests === true || queryParams.includeRequests === 'true'
        }
        if (queryParams.includeReceived !== undefined) {
          filters.includeReceived =
            queryParams.includeReceived === true || queryParams.includeReceived === 'true'
        }
      },
    }
  }

  /**
   * Configuration for contact list endpoints using base controller helpers
   */
  private async getListConfig() {
    await this.initBaseController()

    const contactService = this.contactService

    // Adapter wrapping contactService to conform to BaseResourceService interface
    const listServiceAdapter = {
      // All resource objects must expose userId for the base utils
      mapOwner(list: any): any {
        if (list && !list.userId) list.userId = list.ownerUserId
        return list
      },

      async getResourceById(id: string): Promise<any> {
        const list = await UserContactList.find(id)
        return this.mapOwner(list)
      },

      async getResources(userId: string): Promise<any> {
        const lists = await contactService.getUserContactLists(userId)
        return {
          data: lists.map((list: any) => this.mapOwner(list)),
          pagination: undefined,
        }
      },

      async deleteResource(id: string, userId: string): Promise<boolean> {
        await contactService.deleteContactList(id, userId)
        return true
      },

      async createResource(userId: string, data: any): Promise<any> {
        const list = await contactService.createContactList(userId, { name: data.name })
        return this.mapOwner(list)
      },

      async updateResource(id: string, data: any): Promise<any> {
        const list = await contactService.updateContactList(id, data.__userId, {
          name: data.name,
        })
        return this.mapOwner(list)
      },
    }

    return {
      service: listServiceAdapter,
      resourceName: 'contact list',
      validateQueryParams: async () => ({}), // no query params for lists yet
      getStatusMapping: () => undefined,
      getSortMapping: () => ({ name: 'name', createdAt: 'createdAt' }),
      validateCreatePayload: async (request: any) =>
        await request.validateUsing(this.createListValidator),
      validateUpdatePayload: async (request: any) =>
        await request.validateUsing(this.updateListValidator),
    }
  }

  /**
   * List contacts for authenticated user with pagination & filters
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getConfig())
    return handler(ctx)
  }

  // Alias for existing route compatibility
  getContacts = async (ctx: HttpContext) => this.index(ctx)

  // ===== CONTACT REQUEST ENDPOINTS =====

  /**
   * Send a contact request to another user
   * POST /contacts/requests
   */
  async sendContactRequest({ request, auth, response }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact request')

      const { addresseeUserId } = request.only(['addresseeUserId'])

      if (!addresseeUserId) {
        throw badRequestException('addresseeUserId is required')
      }

      const contact = await this.contactService.sendContactRequest(userId, {
        addresseeUserId,
      })

      return response
        .status(201)
        .json(
          createSuccessResponse({ data: contact, message: 'Contact request sent successfully' })
        )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Respond to a contact request (accept, decline, block)
   * PATCH /contacts/requests/:contactId/respond
   */
  async respondToContactRequest({ request, auth, response, params }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact request')

      const { contactId } = params
      const { status } = request.only(['status'])

      if (!status || !isContactRequestStatus(status)) {
        throw badRequestException(
          'Invalid or missing status. Must be accepted, declined, or blockedByAddressee'
        )
      }

      const contact = await this.contactService.respondToContactRequest(contactId, userId, {
        status,
      })

      const statusMessages: Record<string, string> = {
        [ContactRequestStatus.ACCEPTED]: 'Contact request accepted',
        [ContactRequestStatus.DECLINED]: 'Contact request declined',
        [ContactRequestStatus.BLOCKED_BY_ADDRESSEE]: 'Contact blocked',
      }

      return response.status(200).json(
        createSuccessResponse({
          data: contact,
          message: statusMessages[status] || 'Contact request updated',
        })
      )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Block a contact
   * PATCH /contacts/:contactId/block
   */
  async blockContact({ auth, response, params }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact')

      const { contactId } = params
      const contact = await this.contactService.blockContact(contactId, userId)

      return response
        .status(200)
        .json(createSuccessResponse({ data: contact, message: 'Contact blocked successfully' }))
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  // ===== CONTACT LIST ENDPOINTS (using base controller) =====

  /**
   * Create a new contact list
   * POST /contacts/lists
   */
  createContactList = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createStoreHandler(await this.getListConfig())
    return handler(ctx)
  }

  /**
   * Get all contact lists for the authenticated user
   * GET /contacts/lists
   */
  getContactLists = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getListConfig())
    return handler(ctx)
  }

  /**
   * Get a specific contact list
   * GET /contacts/lists/:listId
   */
  getContactList = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getListConfig())
    return handler(ctx)
  }

  /**
   * Update a contact list
   * PATCH /contacts/lists/:listId
   */
  updateContactList = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createUpdateHandler(await this.getListConfig())
    return handler(ctx)
  }

  /**
   * Delete a contact list
   * DELETE /contacts/lists/:listId
   */
  deleteContactList = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getListConfig())
    return handler(ctx)
  }

  // ===== CONTACT LIST MEMBER ENDPOINTS =====

  /**
   * Add a contact to a contact list
   * POST /contacts/lists/:listId/members
   */
  async addContactToList({ request, auth, response, params }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact list member')

      const { listId } = params
      const { userId: memberUserId } = request.only(['userId'])

      if (!memberUserId) {
        throw badRequestException('userId is required')
      }

      const member = await this.contactService.addContactToList(listId, userId, {
        userId: memberUserId,
      })

      return response
        .status(201)
        .json(
          createSuccessResponse({ data: member, message: 'Contact added to list successfully' })
        )
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Remove a contact from any contact list by member ID
   * DELETE /contacts/lists/members/:memberId
   */
  async removeContactFromList({ auth, response, params }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact list member')

      const { memberId } = params
      await this.contactService.removeContactListMember(memberId, userId)

      return response
        .status(200)
        .json(createSuccessResponse({ message: 'Contact removed from list successfully' }))
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }

  /**
   * Get all members of a contact list
   * GET /contacts/lists/:listId/members
   */
  async getContactListMembers({ auth, response, params }: HttpContext) {
    try {
      const base = await this.initBaseController()
      const userId = await base.validateAuthentication(auth, 'contact list member')

      const { listId } = params
      const members = await this.contactService.getContactListMembers(listId, userId)

      return response.status(200).json(createSuccessResponse({ data: members }))
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, response)
    }
  }
}
