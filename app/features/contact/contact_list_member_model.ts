import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import UserContactList from './user_contact_list_model.js'

/**
 * ContactListMember Model
 *
 * Represents the membership of a user in a contact list.
 * This is a junction table that connects users to contact lists,
 * allowing many-to-many relationships between contacts and lists.
 *
 * Based on the contact_list_member table in the ERD.
 */
export default class ContactListMember extends BaseModel {
  static table = 'contact_list_member'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare listId: string

  @column()
  declare userId: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationship to the contact list this member belongs to
   */
  @belongsTo(() => UserContactList, { foreignKey: 'listId' })
  declare contactList: BelongsTo<typeof UserContactList>

  /**
   * Relationship to the user who is a member of the contact list
   */
  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  /**
   * Check if this membership is for a specific user
   */
  public isForUser(userId: string): boolean {
    return this.userId === userId
  }

  /**
   * Check if this membership is for a specific contact list
   */
  public isForList(listId: string): boolean {
    return this.listId === listId
  }
}
