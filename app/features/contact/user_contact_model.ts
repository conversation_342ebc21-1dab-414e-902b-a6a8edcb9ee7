import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import { ContactRequestStatus } from '#types/contact_types'

/**
 * UserContact Model
 *
 * Represents a contact relationship between two users,
 * including contact requests, accepted contacts, and blocked users.
 *
 * Based on the user_contact table in the ERD.
 */
export default class UserContact extends compose(BaseModel, SoftDeletes) {
  static table = 'user_contact'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare requesterUserId: string

  @column()
  declare addresseeUserId: string

  @column()
  declare status: ContactRequestStatus

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationship to the user who initiated the contact request
   */
  @belongsTo(() => User, { foreignKey: 'requesterUserId' })
  declare requester: BelongsTo<typeof User>

  /**
   * Relationship to the user who received the contact request
   */
  @belongsTo(() => User, { foreignKey: 'addresseeUserId' })
  declare addressee: BelongsTo<typeof User>

  /**
   * Check if the contact is in a specific status
   */
  public isStatus(status: ContactRequestStatus): boolean {
    return this.status === status
  }

  /**
   * Check if the contact is accepted (mutual connection)
   */
  public isAccepted(): boolean {
    return this.status === ContactRequestStatus.ACCEPTED
  }

  /**
   * Check if the contact is pending approval
   */
  public isPending(): boolean {
    return this.status === ContactRequestStatus.PENDING
  }

  /**
   * Check if the contact is blocked by either party
   */
  public isBlocked(): boolean {
    return (
      this.status === ContactRequestStatus.BLOCKED_BY_REQUESTER ||
      this.status === ContactRequestStatus.BLOCKED_BY_ADDRESSEE
    )
  }

  /**
   * Check if a specific user is involved in this contact relationship
   */
  public involvesUser(userId: string): boolean {
    return this.requesterUserId === userId || this.addresseeUserId === userId
  }

  /**
   * Get the other user ID in the relationship for a given user
   */
  public getOtherUserId(userId: string): string | null {
    if (this.requesterUserId === userId) {
      return this.addresseeUserId
    }
    if (this.addresseeUserId === userId) {
      return this.requesterUserId
    }
    return null
  }
}
