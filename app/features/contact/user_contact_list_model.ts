import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import ContactListMember from './contact_list_member_model.js'

/**
 * UserContactList Model
 *
 * Represents a custom contact list created by a user to organize their contacts.
 * Users can create multiple lists (e.g., "Work Contacts", "Family", "Close Friends")
 * and add their accepted contacts to these lists.
 *
 * Based on the user_contact_list table in the ERD.
 */
export default class UserContactList extends compose(BaseModel, SoftDeletes) {
  static table = 'user_contact_list'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare ownerUserId: string

  @column()
  declare name: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationship to the user who owns this contact list
   */
  @belongsTo(() => User, { foreignKey: 'ownerUserId' })
  declare owner: BelongsTo<typeof User>

  /**
   * Relationship to the members of this contact list
   */
  @hasMany(() => ContactListMember, { foreignKey: 'listId' })
  declare members: HasMany<typeof ContactListMember>

  /**
   * Check if the list is owned by a specific user
   */
  public isOwnedBy(userId: string): boolean {
    return this.ownerUserId === userId
  }

  /**
   * Get the number of members in this list
   * Note: This requires the members relationship to be loaded
   */
  public getMemberCount(): number {
    return this.members ? this.members.length : 0
  }
}
