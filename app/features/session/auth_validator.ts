import vine from '@vinejs/vine'

/**
 * Validator for user registration
 */
export const createUserValidator = vine.compile(
  vine.object({
    username: vine.string().trim().minLength(3).maxLength(50),
    email: vine.string().trim().email(),
    password: vine.string().minLength(8).confirmed(),
    password_confirmation: vine.string(), // Will be validated by password.confirmed()
  })
)

/**
 * Validator for user login
 */
export const loginValidator = vine.compile(
  vine.object({
    email: vine.string().trim().email(),
    password: vine.string(),
  })
)

/**
 * Validator for refresh token requests
 */
export const refreshTokenValidator = vine.compile(
  vine.object({
    refreshToken: vine.string(),
    rotateRefreshToken: vine.boolean().optional(),
  })
)
