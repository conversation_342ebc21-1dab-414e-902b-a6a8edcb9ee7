import { createUserValidator, loginValidator, refreshTokenValidator } from './auth_validator.js'
import type { HttpContext } from '@adonisjs/core/http'
import SessionService from './session_service.js'

export default class SessionController {
  private sessionService = new SessionService()

  async register({ request, response }: HttpContext) {
    const payload = await request.validateUsing(createUserValidator)

    try {
      const { authUser, user } = await this.sessionService.registerUser(payload)

      return response.created({
        id: authUser.id,
        username: authUser.username,
        email: authUser.email,
        userId: user.id,
      })
    } catch (error) {
      console.error('User registration error:', error)
      return response.internalServerError({ error: 'Failed to register user' })
    }
  }

  async login({ request, response, auth }: HttpContext) {
    const payload = await request.validateUsing(loginValidator)

    try {
      const { authUser, token, refreshToken, userId } = await this.sessionService.loginUser(
        payload,
        auth
      )

      const { id, username, email, createdAt } = authUser

      return response.ok({
        authUser: { id, username, email, createdAt },
        token,
        refreshToken: refreshToken.token,
        expiresIn: 7200,
        userId,
      })
    } catch (error) {
      console.error('Login error:', error)
      return response.badRequest({ error: 'Invalid credentials' })
    }
  }

  async refresh({ request, response, auth }: HttpContext) {
    const payload = await request.validateUsing(refreshTokenValidator)

    try {
      const result = await this.sessionService.refreshAuthToken(
        payload.refreshToken,
        auth,
        payload.rotateRefreshToken
      )

      if (result.refreshToken) {
        return response.ok({
          type: 'bearer',
          token: result.token,
          expiresAt: result.token.expiresAt,
          refreshToken: result.refreshToken.token,
        })
      }

      return response.ok({
        type: 'bearer',
        token: result.token,
        expiresAt: result.token.expiresAt,
      })
    } catch (error) {
      console.error('Token refresh error:', error)
      return response.internalServerError({ error: 'Failed to refresh token' })
    }
  }

  async logout({ request, response, auth }: HttpContext) {
    try {
      console.log('Logging out')

      const { refreshToken } = request.body() || {}
      await this.sessionService.logoutUser(auth, refreshToken)

      console.log('Logged out successfully')

      return response.ok({
        message: 'Logged out successfully',
      })
    } catch (error) {
      console.error('Logout error:', error)
      return response.internalServerError({ error: 'Failed to logout' })
    }
  }
}
