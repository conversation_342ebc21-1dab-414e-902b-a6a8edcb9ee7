import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import AuthUser from '#models/auth_user_model'
import User from '#features/user/user_model'

test.group('Authentication Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  test('user can register successfully', async ({ client, assert }) => {
    const uniqueId = Date.now() + Math.random().toString(36).substring(2, 8)
    const userData = {
      username: `testuser${uniqueId}`,
      email: `test${uniqueId}@example.com`,
      password: 'TestPassword123!',
      password_confirmation: 'TestPassword123!',
    }

    const response = await client.post('/api/v1/auth/register').form(userData)
    
    if (response.response.status !== 201) {
      console.log('Registration error response:', response.body())
    }
    
    response.assertStatus(201)
    const body = response.body()
    
    assert.exists(body.id)
    assert.exists(body.username)
    assert.equal(body.username, userData.username)
    assert.equal(body.email, userData.email)

    // Verify in database
    const authUser = await AuthUser.findBy('email', userData.email)
    assert.exists(authUser)
    
    const user = await User.findBy('authUserId', authUser!.id)
    assert.exists(user)
  }).timeout(15000)

  test('user can login with valid credentials', async ({ client, assert }) => {
    // Create a user first
    const uniqueId = Date.now() + Math.random().toString(36).substring(2, 8)
    const userData = {
      username: `loginuser${uniqueId}`,
      email: `login${uniqueId}@example.com`,
      password: 'TestPassword123!',
      password_confirmation: 'TestPassword123!',
    }

    await client.post('/api/v1/auth/register').form(userData)
    
    // Now login
    const response = await client.post('/api/v1/auth/login').form({
      email: userData.email,
      password: userData.password,
    })

    response.assertStatus(200)
    const body = response.body()

    assert.exists(body.authUser)
    assert.exists(body.token)
    assert.exists(body.token.token)
    assert.equal(body.token.type, 'bearer')
    assert.exists(body.userId)
  }).timeout(15000)

  test('login with invalid credentials fails', async ({ client, assert }) => {
    const response = await client.post('/api/v1/auth/login').form({
      email: '<EMAIL>',
      password: 'wrongpassword',
    })

    response.assertStatus(400)
    const body = response.body()
    // Error response should have error details
    assert.isTrue(body.status !== 'success' || body.errors || body.message)
  }).timeout(10000)

  test('protected routes require authentication', async ({ client, assert }) => {
    const response = await client.get('/api/v1/profile')
    response.assertStatus(401)
  }).timeout(10000)

  test('valid token can access protected routes', async ({ client, assert }) => {
    // Create and login user
    const uniqueId = Date.now() + Math.random().toString(36).substring(2, 8)
    const userData = {
      username: `tokenuser${uniqueId}`,
      email: `token${uniqueId}@example.com`,
      password: 'TestPassword123!',
      password_confirmation: 'TestPassword123!',
    }

    await client.post('/api/v1/auth/register').form(userData)
    
    const loginResponse = await client.post('/api/v1/auth/login').form({
      email: userData.email,
      password: userData.password,
    })
    
    const token = loginResponse.body().token.token
    
    // Try protected route
    const response = await client.get('/api/v1/profile').bearerToken(token)
    
    // Should get 404 because user doesn't have profile, but auth worked
    response.assertStatus(404)
  }).timeout(15000)
})