import { DateTime } from 'luxon'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { randomUUID } from 'node:crypto'
import AuthUser from '#models/auth_user_model'

export default class RefreshToken extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare token: string

  @column()
  declare revoked: boolean

  @column.dateTime()
  declare expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => AuthUser)
  declare user: BelongsTo<typeof AuthUser>

  /**
   * Create a new refresh token for a user
   */
  static async createForUser(user: AuthUser): Promise<RefreshToken> {
    const expiresAt = DateTime.now().plus({ days: 7 })

    return await RefreshToken.create({
      id: randomUUID(),
      userId: user.id,
      token: randomUUID(),
      revoked: false,
      expiresAt,
    })
  }

  /**
   * Find a valid refresh token
   */
  static async findValid(token: string): Promise<RefreshToken | null> {
    return await RefreshToken.query()
      .where('token', token)
      .where('revoked', false)
      .where('expires_at', '>', DateTime.now().toSQL())
      .first()
  }

  /**
   * Revoke a refresh token
   */
  async revoke(): Promise<void> {
    this.revoked = true
    await this.save()
  }

  /**
   * Revoke all refresh tokens for a user
   */
  static async revokeAllForUser(userId: string): Promise<void> {
    await RefreshToken.query().where('user_id', userId).update({ revoked: true })
  }
}
