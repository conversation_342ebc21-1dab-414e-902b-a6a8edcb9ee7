import AuthUser from '#models/auth_user_model'
import RefreshToken from './refresh_token_model.js'
import User from '#features/user/user_model'
import hash from '@adonisjs/core/services/hash'
import { authorizationException, badRequestException, notFoundException } from '#utils/error'
import { DateTime } from 'luxon'

// Define types inline to avoid import issues
export interface UserAuthPayload {
  username: string
  email: string
  password: string
  password_confirmation: string
}

export interface UserLoginPayload {
  email: string
  password: string
}

export default class SessionService {
  /**
   * Check if a user exists by email.
   */
  async checkUserExists(email: string): Promise<boolean> {
    const existingUser = await AuthUser.findBy('email', email)
    return !!existingUser
  }

  /**
   * Register a new user with associated User record.
   */
  async registerUser(payload: UserAuthPayload): Promise<{ authUser: AuthUser; user: User }> {
    const existingUser = await AuthUser.findBy('email', payload.email)
    if (existingUser) {
      throw badRequestException('User with this email already exists')
    }

    const authUser = await AuthUser.create({
      username: payload.username,
      email: payload.email,
      passwordHash: payload.password, // Let the model handle password hashing
    })

    // Create a User record linked to the AuthUser
    const user = await User.create({
      authUserId: authUser.id,
    })

    return { authUser, user }
  }

  /**
   * Authenticate a user and generate tokens.
   */
  async loginUser(
    payload: UserLoginPayload,
    auth: any
  ): Promise<{
    authUser: AuthUser
    token: any
    refreshToken: RefreshToken
    userId: string | undefined
  }> {
    const authUser = await AuthUser.verifyCredentials(payload.email, payload.password)
    const userId = await authUser.getUserId()

    const token = await auth.use('api').createToken(authUser)
    const refreshToken = await RefreshToken.createForUser(authUser)

    return { authUser, token, refreshToken, userId }
  }

  /**
   * Refresh an access token using a refresh token.
   */
  async refreshAuthToken(
    refreshTokenString: string,
    auth: any,
    rotateRefreshToken = false
  ): Promise<{
    token: any
    refreshToken?: RefreshToken
  }> {
    // Find and validate the refresh token
    const refreshToken = await RefreshToken.findValid(refreshTokenString)
    if (!refreshToken) {
      throw authorizationException('Invalid or expired refresh token')
    }

    // Get the user
    const user = await AuthUser.find(refreshToken.userId)
    if (!user) {
      throw notFoundException('User not found')
    }

    const newToken = await auth.use('api').createToken(user)

    if (rotateRefreshToken) {
      await refreshToken.revoke()
      const newRefreshToken = await RefreshToken.createForUser(user)
      return { token: newToken, refreshToken: newRefreshToken }
    }

    return { token: newToken }
  }

  /**
   * Logout a user by invalidating tokens.
   */
  async logoutUser(auth: any, refreshTokenString?: string): Promise<void> {
    if (!auth.user) {
      throw authorizationException('No authenticated user found for logout')
    }

    // Invalidate the current access token
    await auth.use('api').invalidateToken()

    // If a refresh token was provided, revoke it
    if (refreshTokenString) {
      const token = await RefreshToken.findBy('token', refreshTokenString)
      if (token) {
        await token.revoke()
      }
    }
  }
}
