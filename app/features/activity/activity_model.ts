import BaseModel from '#models/base_model'
import { column, hasOne } from '@adonisjs/lucid/orm'
import type { HasOne } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import { ActivityType } from '#types/activity_types'

// Import subtype models to avoid circular dependency issues
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'

export default class Activity extends compose(BaseModel, SoftDeletes) {
  @column({ isPrimary: true })
  declare id: string

  @column({ columnName: 'activity_type' })
  declare activityType: ActivityType

  @column()
  declare title: string

  @column()
  declare description: string | null

  @column.dateTime({ columnName: 'start_date' })
  declare startDate: DateTime | null

  @column.dateTime({ columnName: 'end_date' })
  declare endDate: DateTime | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => (value ? JSON.parse(value) : null),
  })
  declare location: Record<string, any> | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  declare deletedAt: DateTime | null

  // Relationships to subtypes
  @hasOne(() => Task, { foreignKey: 'activityId', localKey: 'id' })
  declare task: HasOne<typeof Task>

  @hasOne(() => Event, { foreignKey: 'activityId' })
  declare event: HasOne<typeof Event>

  /**
   * Helper method to get the specific subtype instance
   * This will be implemented fully after all subtype models are created
   */
  async getSubtypeInstance() {
    switch (this.activityType) {
      case ActivityType.TASK:
        // Will be implemented after Task model refactoring
        throw new Error('Task subtype relationship not yet established')
      case ActivityType.EVENT:
        throw new Error('Event subtype not yet implemented')
      default:
        throw new Error(`Unknown activity type: ${this.activityType}`)
    }
  }
}
