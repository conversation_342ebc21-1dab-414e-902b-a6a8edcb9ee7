import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { compose } from '@adonisjs/core/helpers'
import hash from '@adonisjs/core/services/hash'
import BaseModel from '#models/base_model'
import { column, hasMany, hasOne, computed } from '@adonisjs/lucid/orm'
import type { HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import RefreshToken from '#features/session/refresh_token_model'
import { Profile } from '#features/profile/index'
import User from '#features/user/user_model'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'passwordHash',
})

export default class AuthUser extends compose(BaseMode<PERSON>, AuthFinder, SoftDeletes) {
  static accessTokens = DbAccessTokensProvider.forModel(AuthUser, {
    expiresIn: '30 minutes',
    prefix: 'oat_',
    table: 'auth_access_tokens',
    type: 'auth_token',
    tokenSecretLength: 40,
  })

  async getUserId(): Promise<string | undefined> {
    if (this.user === undefined && this.$isPersisted) {
      await this.load('user')
    }

    return this.user?.id
  }

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare username: string

  @column()
  declare email: string

  @column({ serializeAs: null })
  declare passwordHash: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  @hasMany(() => RefreshToken)
  declare refreshTokens: HasMany<typeof RefreshToken>

  @hasOne(() => Profile)
  declare profile: HasOne<typeof Profile>

  @hasOne(() => User)
  declare user: HasOne<typeof User>

  @computed()
  get userId(): string | undefined {
    // If user relationship is not loaded, return undefined
    // The getUserId() method should be used to ensure the relationship is loaded
    return this.user?.id
  }
}
