import { BaseModel as LucidBaseModel } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class BaseModel extends LucidBaseModel {
  /**
   * Override default serialization to format DateTime fields to 'MM/DD/YYYY HH:mm:ss' strings.
   */
  public serialize() {
    const json = super.serialize()
    for (const key of Object.keys(json)) {
      const value = this[key as keyof this]
      if (value instanceof DateTime) {
        json[key] = value.toISO()
      }
    }
    return json
  }
}
